import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';
import { AdditionActivityService } from '@src/modules/shared/addition-activity/addition-activity.service';
import { AdditionActivityEntity } from '@src/entities/addition-activity.entity';
import * as dayjs from 'dayjs';

jest.mock('@src/common/utils/image-handler', () => ({
  getConfidentialPrivateFile: jest.fn().mockImplementation((imgPath) => 'some/path'),
}));

describe('AdditionActivityService', () => {
  let service: AdditionActivityService;
  const mockedAdditionAttemptEntity = {
    findOne: jest.fn(),
    find: jest.fn(),
  };
  const mockedAdditionActivityEntity = {
    find: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdditionActivityService,
        {
          provide: getRepositoryToken(AdditionAttemptEntity),
          useValue: mockedAdditionAttemptEntity,
        },
        {
          provide: getRepositoryToken(AdditionActivityEntity),
          useValue: mockedAdditionActivityEntity,
        },
      ],
    }).compile();

    service = module.get<AdditionActivityService>(AdditionActivityService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getActivityByAddition', () => {
    it('should return activities by addition information id', async () => {
      const id = '1';
      const attempts = [
        {
          id: 'id2',
          additionalInformationId: id,
          isDeleted: false,
          createdAt: dayjs().subtract(7, 'day'),
          documents: [{ path: 'path', name: 'name.pdf' }],
        },
        {
          id: 'id3',
          additionalInformationId: id,
          isDeleted: true,
          createdAt: dayjs().subtract(7, 'day'),
          documents: [{ path: 'path', name: 'name.pdf' }],
        },
        {
          id: 'id4',
          additionalInformationId: id,
          isDeleted: false,
          createdAt: dayjs().subtract(380, 'day'),
          documents: [{ path: 'path', name: 'name.pdf' }],
        },
      ];
      mockedAdditionActivityEntity.find.mockResolvedValueOnce([
        {
          additionAttemptId: 'id1',
          type: 1,
        },
        {
          additionAttemptId: 'id2',
          type: 2,
        },
        {
          additionAttemptId: 'id3',
          type: 2,
        },
        {
          additionAttemptId: 'id4',
          type: 2,
        },
        {
          additionAttemptId: 'id5',
          type: 2,
        },
      ]);
      mockedAdditionAttemptEntity.find.mockResolvedValueOnce(attempts);
      const result = await service.getActivityByAddition(id);
      expect(result).toBeTruthy();
    });
  });
});
