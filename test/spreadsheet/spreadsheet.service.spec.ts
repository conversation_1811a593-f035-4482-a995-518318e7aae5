import { Test, TestingModule } from '@nestjs/testing';
import { SpreadsheetService } from '@src/modules/shared/spreadsheet/spreadsheet.service';
import { google } from 'googleapis';

jest.mock('googleapis', () => ({
  google: {
    auth: {
      GoogleAuth: jest.fn().mockImplementation(() => ({
        getClient: jest.fn().mockResolvedValue({}),
      })),
    },
    sheets: jest.fn().mockReturnValue({
      spreadsheets: {
        values: {
          append: jest.fn().mockResolvedValue({ data: 'mocked data' }),
        },
      },
    }),
  },
}));

describe('SpreadsheetService', () => {
  let service: SpreadsheetService;
  beforeEach(async () => {
    process.env.GOOGLE_SERVICE_ACCOUNT_KEY = JSON.stringify({
      private_key: 'mocked_private_key',
    });
    process.env.GOOGLE_SPREADSHEET_ID = 'mocked_spreadsheet_id';
    process.env.GOOGLE_SPREADSHEET_ADDITIONAL_INFORMATION_ID = 'mocked_spreadsheet_id';

    const module: TestingModule = await Test.createTestingModule({
      providers: [SpreadsheetService],
    }).compile();

    service = module.get<SpreadsheetService>(SpreadsheetService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('initGoogleAuthClient', () => {
    it('should initialize authClient if not already initialized', async () => {
      const res = await service.initGoogleAuthClient();
      expect(res).toBe(void 0);
    });
  });

  describe('saveReservationDataOnGoogleSheet', () => {
    it('should append data to Google Sheet aircon', async () => {
      const data = {
        workingDate1: '2023-01-01',
        workingDate2: '2023-01-02',
        packages: [{ id: 'id', name: 'Package1', quantity: 1 }],
        lastname: 'Doe',
        firstname: 'John',
        email: '<EMAIL>',
        postalcode: '123456',
        prefecture: 'Prefecture',
        city: 'City',
        address: 'Address',
        phoneNumber: '**********',
        existingAirConditionerRemoval: true,
        existingAirConditionerRetrieval: false,
        indoorPipeDecorativeCover: true,
        outdoorPipeDecorativeCover: false,
        outdoorUnitMountWithBrackets: true,
        twoLevelOutdoorUnitWithBrackets: false,
        productDeliveryType: '0',
        packType: 1,
      } as any;
      const result = await service.saveReservationDataOnGoogleSheet(data, '123456');
      expect(result).toBeUndefined();
    });

    it('should append data to Google Sheet water heater', async () => {
      const data = {
        workingDate1: '2023-01-01',
        workingDate2: '2023-01-02',
        packages: [{ id: 'id', name: 'Package1', quantity: 1 }],
        lastname: 'Doe',
        firstname: 'John',
        email: '<EMAIL>',
        postalcode: '123456',
        prefecture: 'Prefecture',
        city: 'City',
        address: 'Address',
        phoneNumber: '**********',
        packType: 2,
      } as any;
      const result = await service.saveReservationDataOnGoogleSheet(data, '123456');
      expect(result).toBeUndefined();
    });

    it('should append data to Google Sheet washing machine', async () => {
      const data = {
        workingDate1: '2023-01-01',
        workingDate2: '2023-01-02',
        packages: [{ id: 'id', name: 'Package1', quantity: 1 }],
        lastname: 'Doe',
        firstname: 'John',
        email: '<EMAIL>',
        postalcode: '123456',
        prefecture: 'Prefecture',
        city: 'City',
        address: 'Address',
        phoneNumber: '**********',
        productDeliveryType: '0',
        packType: 3,
      } as any;
      const result = await service.saveReservationDataOnGoogleSheet(data, '123456');
      expect(result).toBeUndefined();
    });

    it('should handle errors gracefully', async () => {
      (google.sheets({} as any).spreadsheets.values.append as jest.Mock).mockRejectedValueOnce(new Error('123456'));

      const data = {
        workingDate1: '2023-01-01',
        workingDate2: '2023-01-02',
        packages: [{ id: 'id', name: 'Package1', quantity: 1 }],
        lastname: 'Doe',
        firstname: 'John',
        email: '<EMAIL>',
        postalcode: '123456',
        prefecture: 'Prefecture',
        city: 'City',
        address: 'Address',
        phoneNumber: '**********',
        existingAirConditionerRemoval: true,
        existingAirConditionerRetrieval: false,
        indoorPipeDecorativeCover: true,
        outdoorPipeDecorativeCover: false,
        outdoorUnitMountWithBrackets: true,
        twoLevelOutdoorUnitWithBrackets: false,
      } as any;
      try {
        await service.saveReservationDataOnGoogleSheet(data, '123456');
      } catch (err) {
        expect(err).toBeDefined();
      }
    });
  });

  describe('saveAdditionalInformationOnGoogleSheet', () => {
    it('should append data to Google Sheet additional information', async () => {
      const data = {
        bankName: 'bankName',
        branchName: 'branchName',
        accountType: 'accountType',
        accountNumber: 'accountNumber',
        accountHolderNameKana: 'accountHolderNameKana',
        templateType: 1,
      } as any;
      const result = await service.saveAdditionalInformationOnGoogleSheet(data, 1, '123456');
      expect(result).toBeUndefined();
    });
    it('should append data to Google Sheet additional information 2', async () => {
      const data = {
        faucetModelNumber: 'bankName',
        templateType: 3,
      } as any;
      const result = await service.saveAdditionalInformationOnGoogleSheet(data, 3, '123456');
      expect(result).toBeUndefined();
    });
    it('should append data to Google Sheet additional information', async () => {
      const data = {} as any;
      const result = await service.saveAdditionalInformationOnGoogleSheet(data, 2, '123456');
      expect(result).toBeUndefined();
    });

    it('should handle errors gracefully', async () => {
      (google.sheets({} as any).spreadsheets.values.append as jest.Mock).mockRejectedValueOnce(new Error('123456'));

      const data = {
        bankName: 'bankName',
        branchName: 'branchName',
        accountType: 'accountType',
        accountNumber: 'accountNumber',
        accountHolderNameKana: 'accountHolderNameKana',
        templateType: 1,
      } as any;
      try {
        await service.saveAdditionalInformationOnGoogleSheet(data, 1, '123456');
      } catch (err) {
        expect(err).toBeDefined();
      }
    });
  });
});
