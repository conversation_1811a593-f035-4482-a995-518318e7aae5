import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PackageService } from '@src/modules/shared/package/package.service';
import { PackageEntity } from '@src/entities/package.entity';
import { ReservationsPackagesEntity } from '@src/entities/reservations-packages.entity';
import { AdminPaginatePackageDto } from '@src/modules/shared/package/dtos/admin-paging-package.dto';
import { CreatePackageDto, UpdatePackageDto } from '@src/modules/shared/package/dtos/create-package.dto';
import { PackageStatus } from '@src/common/enums/package';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { ObjectCacheService } from '@src/modules/shared/object-cache/object-cache.service';

describe('PackageService', () => {
  let service: PackageService;
  const mockedPackageEntity = {
    createQueryBuilder: jest.fn(),
    findOne: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    find: jest.fn(),
    query: jest.fn(),
  };
  const mockedReservationsPackagesEntity = {
    createQueryBuilder: jest.fn(),
    findOne: jest.fn(),
  };
  const mockedConfigurationService = {
    generateRandomCode: jest.fn(),
    getNextCode: jest.fn(),
    setNextCode: jest.fn(),
  };
  const mockedDataSource = {
    transaction: jest.fn(),
    query: jest.fn(),
  };
  const mockedObjectCacheService = {
    get: jest.fn(),
    addOrUpdate: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PackageService,
        {
          provide: getRepositoryToken(PackageEntity),
          useValue: mockedPackageEntity,
        },
        {
          provide: getRepositoryToken(ReservationsPackagesEntity),
          useValue: mockedReservationsPackagesEntity,
        },
        {
          provide: DataSource,
          useValue: mockedDataSource,
        },
        {
          provide: ConfigurationService,
          useValue: mockedConfigurationService,
        },
        {
          provide: ObjectCacheService,
          useValue: mockedObjectCacheService,
        },
      ],
    }).compile();

    service = module.get<PackageService>(PackageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should reset cache on module init', async () => {
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      await service.onModuleInit();
      expect(service.resetCache).toHaveBeenCalled();
    });
  });

  describe('getPackagePageCommonData', () => {
    it('should return common data for package page', async () => {
      mockedConfigurationService.getNextCode.mockReturnValue('PAK12345');
      const result = await service.getPackagePageCommonData();
      expect(result).toBeTruthy();
    });
  });

  describe('getPackages', () => {
    it('should return paginated packages', async () => {
      const paginatePackageDto: AdminPaginatePackageDto = {
        perPage: 10,
        pageNumber: 1,
        offset: 0,
        status: PackageStatus.ENABLE,
        packType: 1,
      };

      const packages = [
        { id: '1', code: 'PAK12345', name: 'Package 1', status: PackageStatus.ENABLE, createdAt: new Date() },
      ];
      const total = 1;
      const reservationCount = [{ packageId: '1', count: '5' }];

      mockedPackageEntity.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([packages, total]),
      });

      mockedReservationsPackagesEntity.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue(reservationCount),
      });

      const result = await service.getPackages(paginatePackageDto);
      expect(result).toEqual({
        packages: packages.map((pack) => ({
          ...pack,
          reservationCount: 5,
        })),
        pagination: {
          perPage: 10,
          pageNumber: 1,
          total,
        },
      });
    });

    it('should return package list with 0 reservation count', async () => {
      const paginatePackageDto: AdminPaginatePackageDto = {
        perPage: 10,
        pageNumber: 1,
        offset: 0,
        status: PackageStatus.ENABLE,
      };
      const reservationCount = [];
      const packages = [
        { id: '1', code: 'PAK12345', name: 'Package 1', status: PackageStatus.ENABLE, createdAt: new Date() },
      ];
      const total = 1;

      mockedPackageEntity.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([packages, total]),
      });

      mockedReservationsPackagesEntity.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue(reservationCount),
      });

      const result = await service.getPackages(paginatePackageDto);
      expect(result).toEqual({
        packages: packages.map((pack) => ({
          ...pack,
          reservationCount: 0,
        })),
        pagination: {
          perPage: 10,
          pageNumber: 1,
          total: 1,
        },
      });
    });
  });

  describe('getPackageList', () => {
    it('should return list of packages', async () => {
      const packages = [{ id: '1', name: 'Package 1', fee: 100 }];
      mockedPackageEntity.find.mockResolvedValue(packages);
      const result = await service.getPackageList();
      expect(result).toEqual(packages);
    });
    it('should return list of packages with sort', async () => {
      const packages = [{ id: '1', name: 'Package 1', fee: 100 }];
      mockedPackageEntity.find.mockResolvedValue(packages);
      const result = await service.getPackageList({ conditions: {}, select: ['id', 'name'], needSort: true });
      expect(result).toEqual(packages);
    });
  });

  describe('getEnablePackages', () => {
    it('should return cached data if available', async () => {
      const cachedData = [{ id: '1', enabled: true }];
      mockedObjectCacheService.get.mockResolvedValue(cachedData);

      const result = await service.getEnablePackages(1);
      expect(result).toEqual(cachedData);
    });

    it('should return packages', async () => {
      mockedObjectCacheService.get.mockResolvedValue(null);
      service.getPackageList = jest.fn().mockResolvedValue([]);
      mockedObjectCacheService.addOrUpdate.mockResolvedValue(undefined);
      const result = await service.getEnablePackages(1);
      expect(result).toEqual([]);
    });
  });

  describe('getPackageDetail', () => {
    it('should return package detail', async () => {
      const packageDetail = { id: '1', name: 'Package 1' };
      mockedPackageEntity.findOne.mockResolvedValue(packageDetail);
      const result = await service.getPackageDetail('1');
      expect(result).toEqual(packageDetail);
    });
  });

  describe('createPackage', () => {
    it('should create a new package', async () => {
      const createPackageDto: CreatePackageDto = {
        name: 'Package 1',
        fee: 100,
        sort: 1,
        status: PackageStatus.ENABLE,
        code: 'PAK12345',
        outOfStock: true,
        packType: 1,
      };
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedConfigurationService.setNextCode.mockResolvedValue({});
      mockedPackageEntity.findOne.mockResolvedValue(null);
      mockedPackageEntity.insert.mockResolvedValue({});
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      const result = await service.createPackage(createPackageDto);
      expect(result).toBeTruthy();
    });

    it('should throw error if transaction fails exist name', async () => {
      const createPackageDto: CreatePackageDto = {
        name: 'Package 1',
        fee: 100,
        sort: 1,
        status: PackageStatus.ENABLE,
        code: 'PAK12345',
        outOfStock: true,
        packType: 1,
      };
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedPackageEntity.findOne.mockResolvedValueOnce(null).mockResolvedValueOnce({});
      const result = await service.createPackage(createPackageDto);
      expect(result).toBeTruthy();
    });

    it('should throw error if transaction fails', async () => {
      const createPackageDto: CreatePackageDto = {
        name: 'Package 1',
        fee: 100,
        sort: 1,
        status: PackageStatus.ENABLE,
        code: 'PAK12345',
        outOfStock: true,
        packType: 1,
      };
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedPackageEntity.findOne.mockResolvedValue({});
      const result = await service.createPackage(createPackageDto);
      expect(result).toBeTruthy();
    });
  });

  describe('updatePackage', () => {
    it('should update a package', async () => {
      const updatePackageDto: UpdatePackageDto = {
        name: 'Updated Package',
        fee: 200,
        sort: 2,
        status: PackageStatus.DISABLE,
        code: 'PAK12345',
        outOfStock: true,
      };
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedConfigurationService.setNextCode.mockResolvedValue({});
      mockedPackageEntity.findOne.mockResolvedValue(null);
      mockedPackageEntity.update.mockResolvedValue({} as any);
      service.resetCache = jest.fn().mockResolvedValue(undefined);

      const result = await service.updatePackage('1', updatePackageDto);
      expect(result).toBeTruthy();
    });

    it('should throw error if update fails', async () => {
      const updatePackageDto: UpdatePackageDto = {
        name: 'Updated Package',
        fee: 200,
        sort: 2,
        status: PackageStatus.DISABLE,
        code: 'PAK12345',
      };
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedPackageEntity.findOne.mockResolvedValue({});

      const result = await service.updatePackage('1', updatePackageDto);
      expect(result).toBeTruthy();
    });

    it('should throw error if update fails exist name', async () => {
      const updatePackageDto: UpdatePackageDto = {
        name: 'Updated Package',
        fee: 200,
        sort: 2,
        status: PackageStatus.DISABLE,
        code: 'PAK12345',
      };
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedPackageEntity.findOne.mockResolvedValueOnce(null).mockResolvedValueOnce({});

      const result = await service.updatePackage('1', updatePackageDto);
      expect(result).toBeTruthy();
    });
  });

  describe('deletePackage', () => {
    it('should delete a package', async () => {
      mockedReservationsPackagesEntity.findOne.mockResolvedValue(null);
      mockedPackageEntity.delete.mockResolvedValue({} as any);
      service.resetCache = jest.fn().mockResolvedValue(undefined);

      const result = await service.deletePackage('1');
      expect(result).toBeTruthy();
    });

    it('should throw BadRequestException if package is in use', async () => {
      mockedReservationsPackagesEntity.findOne.mockResolvedValue({ packageId: '1' });
      const result = await service.deletePackage('1');
      expect(result).toBeTruthy();
    });

    it('should throw error if delete fails', async () => {
      mockedReservationsPackagesEntity.findOne.mockResolvedValue(null);
      const error = new Error('delete failed');
      mockedPackageEntity.delete.mockRejectedValue(error);

      const result = await service.deletePackage('1');
      expect(result).toEqual({ error: error.message });
    });
  });

  describe('exportPackages', () => {
    it('should export packages', async () => {
      const packageIds = ['1', '2'];
      const packages = [
        { code: 'PAK12345', name: 'Package 1', fee: 100, outOfStock: true, sort: 1, status: PackageStatus.ENABLE },
      ];
      service.getPackageList = jest.fn().mockResolvedValue(packages);
      const res = {
        header: jest.fn().mockImplementation(() => ({})),
        attachment: jest.fn().mockImplementation(() => ({})),
        send: jest.fn().mockImplementation(() => ({})),
      };
      const result = await service.exportPackages(packageIds, res);
      expect(result).toBeUndefined();
    });
  });

  describe('importPackages', () => {
    it('should throw error not csv', async () => {
      const file = {
        buffer: Buffer.from(
          'code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345,Package 1,100,true,1,ENABLE,1\n',
        ),
        originalname: 'file.text',
      } as Express.Multer.File;
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error empty file', async () => {
      const file = {
        buffer: Buffer.from('code,name,fee,out_of_stock,sort,status,pack_type\n'),
        originalname: 'file.csv',
      } as Express.Multer.File;
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error invalid data', async () => {
      const file = {
        buffer: Buffer.from('code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345,,,,,,\n'),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error invalid data 1', async () => {
      const file = {
        buffer: Buffer.from('code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345\n'),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error invalid data 2', async () => {
      const file = {
        buffer: Buffer.from(
          'code,name,fee,out_of_stock,sort,status,pack_type\nPAKSS12345,abc,test,test,test,test,test\n',
        ),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'abc' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error invalid data 3', async () => {
      const file = {
        buffer: Buffer.from('code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345,test,2.3,true,3.4,1,5\n'),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error overlap package code', async () => {
      const file = {
        buffer: Buffer.from(
          'code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345,Package 1,100,true,1,1,1\nPAK12345,Package 3,100,true,1,1,1\nnPAK12345,Package 4,100,true,1,1,1\n',
        ),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should throw error overlap package name', async () => {
      const file = {
        buffer: Buffer.from(
          'code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345,Package 1,100,true,1,1,1\nPAK12346,Package 1,100,true,1,1,1\nPAK12346,Package 1,100,true,1,1,1\n',
        ),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12346', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should import packages', async () => {
      const file = {
        buffer: Buffer.from('code,name,fee,out_of_stock,sort,status,pack_type\nPAK12365,Package 1,100,true,1,1,1\n'),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback({
          save: jest.fn().mockReturnValue({}),
          query: jest.fn().mockReturnValue({}),
        });
      });
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
    it('should import packages 2', async () => {
      const file = {
        buffer: Buffer.from('code,name,fee,out_of_stock,sort,status,pack_type\n,Package 1,100,true,1,1,1\n'),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback({
          save: jest.fn().mockReturnValue({}),
          query: jest.fn().mockReturnValue({}),
        });
      });
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });

    it('should throw error if import fails', async () => {
      const file = {
        buffer: Buffer.from(
          'code,name,fee,out_of_stock,sort,status,pack_type\nPAK12345,Package 1,100,true,1,ENABLE,1\n',
        ),
        originalname: 'file.csv',
      } as Express.Multer.File;
      mockedPackageEntity.find.mockResolvedValue([{ code: 'PAK12345', name: 'Package 2' }]);
      mockedConfigurationService.getNextCode.mockResolvedValue('PAK12345');
      const error = new Error('import failed');
      mockedDataSource.transaction.mockRejectedValue(error);
      const result = await service.importPackages(file);
      expect(result).toBeTruthy();
    });
  });

  describe('resetCache', () => {
    it('should delete cache', async () => {
      mockedObjectCacheService.delete.mockResolvedValue(undefined);
      await service.resetCache();
    });
  });
});
