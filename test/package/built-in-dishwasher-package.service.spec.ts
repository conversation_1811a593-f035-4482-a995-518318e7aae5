import { Test, TestingModule } from '@nestjs/testing';
import { BuiltInDishwasherPackageService } from '@src/modules/shared/package/built-in-dishwasher-package.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { PackageStatus } from '@src/common/enums/package';

describe('BuiltInDishwasherPackageService', () => {
  let service: BuiltInDishwasherPackageService;
  const mockedPackageService = {
    getEnablePackages: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BuiltInDishwasherPackageService, { provide: PackageService, useValue: mockedPackageService }],
    }).compile();

    service = module.get<BuiltInDishwasherPackageService>(BuiltInDishwasherPackageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCategoryTraits', () => {
    it('should return category traits', async () => {
      const result = service.getCategoryTraits();
      expect(result).toBeTruthy();
    });
  });

  describe('getBuiltInDishwasherPackagesOrFail', () => {
    it('should throw error not match product code', async () => {
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          id: 'id',
          manufacturer: 'manufacturer',
          code: 'code1',
          image: 'image',
          depth: 'depth',
        },
      ]);
      try {
        await service.getBuiltInDishwasherPackagesOrFail('code2');
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error not found product data', async () => {
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          id: 'id',
          manufacturer: 'manufacturer',
          code: 'code1',
          image: 'image',
          depth: 'depth',
        },
      ]);
      mockedPackageService.getEnablePackages = jest
        .fn()
        .mockReturnValue([{ id: 'id', name: 'name', code: 'code2', fee: 100, status: 1, outOfStock: false }]);
      try {
        await service.getBuiltInDishwasherPackagesOrFail('code1');
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error package is disabled', async () => {
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          id: 'id',
          manufacturer: 'manufacturer',
          code: 'code1',
          image: 'image',
          depth: 'depth',
        },
      ]);
      mockedPackageService.getEnablePackages = jest
        .fn()
        .mockReturnValue([
          { id: 'id', name: 'name', code: 'code1', fee: 100, status: PackageStatus.DISABLE, outOfStock: false },
        ]);
      try {
        await service.getBuiltInDishwasherPackagesOrFail('code1');
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error package is out of stock', async () => {
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          id: 'id',
          manufacturer: 'manufacturer',
          code: 'code1',
          image: 'image',
          depth: 'depth',
        },
      ]);
      mockedPackageService.getEnablePackages = jest
        .fn()
        .mockReturnValue([
          { id: 'id', name: 'name', code: 'code1', fee: 100, status: PackageStatus.ENABLE, outOfStock: true },
        ]);
      try {
        await service.getBuiltInDishwasherPackagesOrFail('code1');
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should return selected package data', async () => {
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          id: 'id',
          manufacturer: 'manufacturer',
          code: 'code1',
          image: 'image',
          depth: 'depth',
        },
      ]);
      mockedPackageService.getEnablePackages = jest
        .fn()
        .mockReturnValue([
          { id: 'id', name: 'name', code: 'code1', fee: 100, status: PackageStatus.ENABLE, outOfStock: false },
        ]);
      const result = await service.getBuiltInDishwasherPackagesOrFail('code1');
      expect(result).toBeTruthy();
    });
  });
});
