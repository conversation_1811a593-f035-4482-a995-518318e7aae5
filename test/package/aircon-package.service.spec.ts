import { Test, TestingModule } from '@nestjs/testing';
import { AirconPackageService } from '@src/modules/shared/package/aircon-package.service';

describe('AirconPackageService', () => {
  let service: AirconPackageService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AirconPackageService],
    }).compile();

    service = module.get<AirconPackageService>(AirconPackageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCategoryTraits', () => {
    it('should return category traits', async () => {
      const result = service.getCategoryTraits();
      expect(result).toBeTruthy();
    });
  });

  describe('getCategorySpecifications', () => {
    it('should return category traits', async () => {
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          series: 'test',
          brandJp: '',
          seoDescription: 'seoDes',
          image: 'src',
          gtin13: '123',
          packages: [{ id: 'package 1' }],
        },
      ]);
      const result = service.getCategorySpecifications([
        { id: 'package 1', name: 'name1', code: 'code1', fee: 100 },
        { id: 'package 2', name: 'name1', code: 'code1', fee: 100 },
      ] as any);
      expect(result).toBeTruthy();
    });
    it('should return category traits 2', async () => {
      process.env.APP_URL = 'http://localhost:3000';
      service.getCategoryTraits = jest.fn().mockReturnValue([
        {
          series: 'test',
          brandJp: '',
          seoDescription: 'seoDes',
          image: 'src',
          gtin13: '123',
          packages: [{ id: 'package 1' }],
        },
      ]);
      const result = service.getCategorySpecifications([
        { id: 'package 1', name: 'name1', code: 'code1', fee: 100 },
        { id: 'package 2', name: 'name1', code: 'code1', fee: 100 },
      ] as any);
      expect(result).toBeTruthy();
    });
  });
});
