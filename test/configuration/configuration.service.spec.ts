import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigurationService } from '../../src/modules/shared/configuration/configuration.service';
import { ConfigurationEntity } from '../../src/entities/configuration.entity';
import { UpdateConfigurationDto } from '../../src/modules/shared/configuration/dtos/update-configuration.dto';
import { DataSource } from 'typeorm';
import { ObjectCacheService } from '../../src/modules/shared/object-cache/object-cache.service';

describe('ConfigurationService', () => {
  let service: ConfigurationService;
  const mockedConfigurationEntity = {
    find: jest.fn(),
    findOneBy: jest.fn(),
    update: jest.fn(),
  };
  const mockedObjectCacheService = {
    get: jest.fn(),
    addOrUpdate: jest.fn(),
    delete: jest.fn(),
  };
  const mockedDataSource = {
    query: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigurationService,
        {
          provide: getRepositoryToken(ConfigurationEntity),
          useValue: mockedConfigurationEntity,
        },
        {
          provide: ObjectCacheService,
          useValue: mockedObjectCacheService,
        },
        {
          provide: DataSource,
          useValue: mockedDataSource,
        },
      ],
    }).compile();

    service = module.get<ConfigurationService>(ConfigurationService);
  });

  describe('onModuleInit', () => {
    it('should reset cache on module init', async () => {
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      await service.onModuleInit();
      expect(service.resetCache).toHaveBeenCalled();
    });
  });

  describe('getConfigurations', () => {
    it('should return configurations from cache if available', async () => {
      const cachedData = [{ configKey: 'key1', value: 'value1' }];
      mockedObjectCacheService.get.mockResolvedValue(cachedData);
      const result = await service.getConfigurations();
      expect(result).toEqual(cachedData);
    });

    it('should return configurations from repository if not in cache', async () => {
      const repoData = [{ configKey: 'key1', value: 'value1' }];
      mockedObjectCacheService.get.mockResolvedValue(null);
      mockedConfigurationEntity.find.mockResolvedValue(repoData);
      mockedObjectCacheService.addOrUpdate.mockResolvedValue(undefined);
      const result = await service.getConfigurations();
      expect(result).toEqual(repoData);
    });
  });

  describe('getConfiguration', () => {
    it('should return a single configuration by key', async () => {
      const config = { configKey: 'key1', value: 'value1' };
      mockedConfigurationEntity.findOneBy.mockResolvedValue(config);
      const result = await service.getConfiguration('key1', 1);
      expect(result).toEqual(config);
    });
  });

  describe('updateConfigurations', () => {
    it('should update configuration and reset cache', async () => {
      const updateDto: UpdateConfigurationDto = { value: 'newValue' };
      mockedConfigurationEntity.update.mockResolvedValue(undefined);
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      const result = await service.updateConfigurations('1', updateDto);
      expect(result).toEqual({ data: {} });
    });

    it('should return error if update fails', async () => {
      const updateDto: UpdateConfigurationDto = { value: 'newValue' };
      mockedConfigurationEntity.update.mockRejectedValue(new Error('Update failed'));
      const result = await service.updateConfigurations('1', updateDto);
      expect(result).toEqual({ error: 'Update failed' });
    });
  });

  describe('resetCache', () => {
    it('should delete cache', async () => {
      mockedObjectCacheService.delete.mockResolvedValue(undefined);
      await service.resetCache();
    });
  });

  describe('generateNextCode', () => {
    it('should return next code', async () => {
      mockedDataSource.query.mockResolvedValueOnce([{ generate_code: '12345' }]);
      const result = await service.generateRandomCode('table', 'field', 'prefix', 5);
      expect(result).toBeTruthy();
    });
  });

  describe('getNextCode', () => {
    it('should return next reservation code', async () => {
      mockedDataSource.query.mockResolvedValueOnce([{ code: '12345' }]);
      const result = await service.getNextCode('sequence', 'prefix', 5);
      expect(result).toBeTruthy();
    });
  });

  describe('setNextCode', () => {
    it('should set next reservation code', async () => {
      mockedDataSource.query.mockResolvedValueOnce({});
      const result = await service.setNextCode('sequence');
      expect(result).toBeTruthy();
    });
  });
});
