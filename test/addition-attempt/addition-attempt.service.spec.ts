import { Test, TestingModule } from '@nestjs/testing';
import { AdditionAttemptService } from '@src/modules/shared/addition-attempt/addition-attempt.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';
import * as dayjs from 'dayjs';

jest.mock('@src/common/utils/image-handler', () => ({
  getConfidentialPrivateFile: jest.fn().mockImplementation((imgPath) => 'some/path'),
}));

describe('AdditionAttemptService', () => {
  let service: AdditionAttemptService;
  const mockedAdditionAttemptEntity = {
    findOne: jest.fn(),
    find: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdditionAttemptService,
        {
          provide: getRepositoryToken(AdditionAttemptEntity),
          useValue: mockedAdditionAttemptEntity,
        },
      ],
    }).compile();

    service = module.get<AdditionAttemptService>(AdditionAttemptService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAttemptById', () => {
    it('should return attempt by id', async () => {
      const id = '1';
      const attempt = {
        id,
        isDeleted: false,
        createdAt: dayjs().subtract(7, 'day'),
        documents: [{ path: 'path', name: 'name.pdf' }],
      };
      mockedAdditionAttemptEntity.findOne.mockResolvedValue(attempt);
      const result = await service.getAttemptById(id);
      expect(result).toBeTruthy();
    });

    it('should return attempt by id 2', async () => {
      const id = '1';
      const attempt = {
        id,
        isDeleted: true,
        createdAt: dayjs().subtract(7, 'day'),
        documents: [{ path: 'path', name: 'name.pdf' }],
      };
      mockedAdditionAttemptEntity.findOne.mockResolvedValue(attempt);
      const result = await service.getAttemptById(id);
      expect(result).toBeTruthy();
    });

    it('should return attempt by id 3', async () => {
      const id = '1';
      const attempt = {
        id,
        isDeleted: false,
        createdAt: dayjs().subtract(380, 'day'),
        documents: [{ path: 'path', name: 'name.pdf' }],
      };
      mockedAdditionAttemptEntity.findOne.mockResolvedValue(attempt);
      const result = await service.getAttemptById(id);
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if attempt not found', async () => {
      const id = '1';
      mockedAdditionAttemptEntity.findOne.mockResolvedValue(null);
      try {
        await service.getAttemptById(id);
      } catch (error) {
        expect(error.message).toBeTruthy();
      }
    });
  });
});
