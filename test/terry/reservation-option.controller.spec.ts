import { Test, TestingModule } from '@nestjs/testing';
import { ReservationOptionController } from '../../src/modules/terry/controllers/reservation-option.controller';
import { ReservationOptionService } from '../../src/modules/shared/reservation-option/reservation-option.service';
import { NotFoundException } from '@nestjs/common';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@vietnam/cnga-middleware';

describe('ReservationOptionController', () => {
  let controller: ReservationOptionController;

  const mockReservationOptionService = {
    getAllReservationOptions: jest.fn(),
    getReservationOptionDetail: jest.fn(),
    updateReservationOption: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReservationOptionController],
      providers: [
        {
          provide: ReservationOptionService,
          useValue: mockReservationOptionService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<ReservationOptionController>(ReservationOptionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getListReservationOptionsPage', () => {
    it('should return a list of reservation options', async () => {
      mockReservationOptionService.getAllReservationOptions.mockResolvedValue([{ id: '1', name: 'Option 1' }]);
      const result = await controller.getListReservationOptionsPage({});
      expect(result).toBeTruthy();
    });

    it('should return a list of reservation options', async () => {
      mockReservationOptionService.getAllReservationOptions.mockResolvedValue([{ id: '1', name: 'Option 1' }]);
      const result = await controller.getListReservationOptionsPage({ packType: 2 });
      expect(result).toBeTruthy();
    });
  });

  describe('getReservationOptionDetailPage', () => {
    it('should return reservation option detail', async () => {
      const result = { id: '1', name: 'Option 1' };
      mockReservationOptionService.getReservationOptionDetail.mockResolvedValue(result);

      expect(await controller.getReservationOptionDetailPage('1')).toEqual({
        data: { reservationOption: result },
      });
    });

    it('should throw NotFoundException if reservation option not found', async () => {
      mockReservationOptionService.getReservationOptionDetail.mockResolvedValue(null);

      await expect(controller.getReservationOptionDetailPage('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateReservationOption', () => {
    it('should update and return the updated reservation option', async () => {
      const result = { id: '1', name: 'Updated Option' };
      const updateDto = { name: 'Updated Option' } as any;
      mockReservationOptionService.getReservationOptionDetail.mockResolvedValue(result);
      mockReservationOptionService.updateReservationOption.mockResolvedValue(result);

      expect(await controller.updateReservationOption('1', updateDto)).toEqual(result);
    });

    it('should throw NotFoundException if reservation option not found', async () => {
      const updateDto = { name: 'Updated Option' } as any;
      mockReservationOptionService.getReservationOptionDetail.mockResolvedValue(null);

      await expect(controller.updateReservationOption('1', updateDto)).rejects.toThrow(NotFoundException);
    });
  });
});
