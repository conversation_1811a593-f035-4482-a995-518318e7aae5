import { Test, TestingModule } from '@nestjs/testing';
import { AdditionalInformationController } from '@src/modules/terry/controllers/additional-information.controller';
import { AdditionalInformationService } from '@src/modules/shared/additional-information/additional-information.service';
import { AdditionAttemptService } from '@src/modules/shared/addition-attempt/addition-attempt.service';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@vietnam/cnga-middleware';
import { AdditionalInfoStatus } from '@src/common/enums/additional-information';
import { AdditionActivityService } from '@src/modules/shared/addition-activity/addition-activity.service';

describe('AdditionalInformationController', () => {
  let controller: AdditionalInformationController;
  const mockedAdditionalInformationService = {
    getAdditionalInformationOrFail: jest.fn(),
    deleteAdditionalInformation: jest.fn(),
    refreshUploadUrl: jest.fn(),
  };
  const mockedAdditionAttemptService = {
    getAttemptById: jest.fn(),
  };
  const mockedAdditionActivityService = {
    getActivityByAddition: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdditionalInformationController],
      providers: [
        {
          provide: AdditionalInformationService,
          useValue: mockedAdditionalInformationService,
        },
        {
          provide: AdditionActivityService,
          useValue: mockedAdditionActivityService,
        },
        {
          provide: AdditionAttemptService,
          useValue: mockedAdditionAttemptService,
        },
        {
          provide: AdditionActivityService,
          useValue: mockedAdditionActivityService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<AdditionalInformationController>(AdditionalInformationController);
  });

  describe('getAdditionalInformationActivities', () => {
    it('should return additional information attempts', async () => {
      const id = 'test-id';
      const additionalInfo = { id, deletedAt: null };
      const activities = [{ id: 'activity-1' }, { id: 'activity-2' }];

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue(additionalInfo);
      mockedAdditionActivityService.getActivityByAddition.mockResolvedValue(activities);

      const result = await controller.getAdditionalInformationActivities(id);

      expect(result).toBeTruthy();
    });
  });

  describe('getAdditionalInfoAttemptDetail', () => {
    it('should return additional information attempt detail', async () => {
      const additionId = 'addition-id';
      const attemptId = 'attempt-id';
      const additionAttempt = { id: attemptId, additionalInformationId: 'addition-id' };

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({});
      mockedAdditionAttemptService.getAttemptById.mockResolvedValue(additionAttempt);

      const result = await controller.getAdditionalInfoAttemptDetail(additionId, attemptId);
      expect(result).toEqual({ data: additionAttempt });
    });
    it('should throw error not correct additionalInformationId', async () => {
      const additionId = 'addition-id';
      const attemptId = 'attempt-id';
      const additionAttempt = { id: attemptId, additionalInformationId: '' };

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({});
      mockedAdditionAttemptService.getAttemptById.mockResolvedValue(additionAttempt);

      try {
        await controller.getAdditionalInfoAttemptDetail(additionId, attemptId);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error additional information id not match', async () => {
      const additionId = 'addition-id';
      const attemptId = 'attempt-id';
      const additionAttempt = { id: attemptId, additionalInformationId: 'addition-id2' };

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({});
      mockedAdditionAttemptService.getAttemptById.mockResolvedValue(additionAttempt);

      try {
        await controller.getAdditionalInfoAttemptDetail(additionId, attemptId);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('deleteAdditionalInformationAttempt', () => {
    it('should delete additional information attempt', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({ isEnable: true });
      mockedAdditionalInformationService.deleteAdditionalInformation.mockResolvedValue({});

      const result = await controller.deleteAdditionalInformationAttempt(id);

      expect(result).toBeTruthy();
    });

    it('should throw error additionInformation not enable', async () => {
      const id = 'test-id';

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({ isEnable: false });
      mockedAdditionalInformationService.deleteAdditionalInformation.mockResolvedValue({});

      try {
        await controller.deleteAdditionalInformationAttempt(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it('should throw error addition information already delete', async () => {
      const id = 'test-id';

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({ isEnable: false });

      try {
        await controller.deleteAdditionalInformationAttempt(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it('should throw error addition information already delete', async () => {
      const id = 'test-id';

      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({ isEnable: false });

      try {
        await controller.deleteAdditionalInformationAttempt(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('refreshUploadUrl', () => {
    it('should update additional information to enable re-upload', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: true,
        status: AdditionalInfoStatus.UPLOADED,
      });
      mockedAdditionalInformationService.refreshUploadUrl.mockResolvedValue({});
      const result = await controller.refreshUploadUrl(id);

      expect(result).toBeTruthy();
    });
    it('should throw error additionInformation not enable', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: false,
        status: AdditionalInfoStatus.UPLOADED,
      });
      try {
        await controller.refreshUploadUrl(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error additionInformation not at status uploaded', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: true,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
      });
      try {
        await controller.refreshUploadUrl(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error additional information disable', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: false,
        status: AdditionalInfoStatus.UPLOADED,
      });
      try {
        await controller.refreshUploadUrl(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error additional information is not uploaded', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: true,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
      });
      try {
        await controller.refreshUploadUrl(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error additional information disable', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: false,
        status: AdditionalInfoStatus.UPLOADED,
      });
      try {
        await controller.refreshUploadUrl(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error additional information is not uploaded', async () => {
      const id = 'test-id';
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        isEnable: true,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
      });
      try {
        await controller.refreshUploadUrl(id);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });
});
