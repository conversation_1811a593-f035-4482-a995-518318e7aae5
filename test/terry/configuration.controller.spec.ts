import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationController } from '../../src/modules/terry/controllers/configuration.controller';
import { ConfigurationService } from '../../src/modules/shared/configuration/configuration.service';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@vietnam/cnga-middleware';

describe('ConfigurationController', () => {
  let controller: ConfigurationController;

  const mockConfigurationService = {
    updateConfigurations: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ConfigurationController],
      providers: [
        {
          provide: ConfigurationService,
          useValue: mockConfigurationService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<ConfigurationController>(ConfigurationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateConfiguration', () => {
    it('should call updateConfigurations with correct parameters', async () => {
      mockConfigurationService.updateConfigurations.mockResolvedValue({});

      const result = await controller.updateConfiguration('id', {} as any);
      expect(result).toBeTruthy();
    });
  });
});
