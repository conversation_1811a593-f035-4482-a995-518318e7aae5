import { Test, TestingModule } from '@nestjs/testing';
import { PackageController } from '../../src/modules/terry/controllers/package.controller';
import { PackageService } from '../../src/modules/shared/package/package.service';
import { NotFoundException } from '@nestjs/common';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@vietnam/cnga-middleware';

describe('PackageController', () => {
  let controller: PackageController;
  const mockedPackageService = {
    getPackagePageCommonData: jest.fn(),
    getPackages: jest.fn(),
    getPackageDetail: jest.fn(),
    createPackage: jest.fn(),
    updatePackage: jest.fn(),
    deletePackage: jest.fn(),
    getPackageList: jest.fn(),
    exportPackages: jest.fn(),
    importPackages: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PackageController],
      providers: [
        {
          provide: PackageService,
          useValue: mockedPackageService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<PackageController>(PackageController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getListPackagesPage', () => {
    it('should return package list page data', async () => {
      mockedPackageService.getPackagePageCommonData.mockResolvedValue({ status: 'status' });
      mockedPackageService.getPackages.mockResolvedValue({ packages: [], pagination: {} });

      const result = await controller.getListPackagesPage({} as any);

      expect(result).toBeTruthy();
    });

    it('should return package list page data', async () => {
      mockedPackageService.getPackagePageCommonData.mockResolvedValue({ status: 'status' });
      mockedPackageService.getPackages.mockResolvedValue({ packages: [], pagination: {} });

      const result = await controller.getListPackagesPage({ packType: 2 } as any);

      expect(result).toBeTruthy();
    });
  });

  describe('getPagingListPackage', () => {
    it('should return paginated package list', async () => {
      mockedPackageService.getPackages.mockResolvedValue({ packages: [], pagination: {} });
      const result = await controller.getPagingListPackage({} as any);
      expect(result).toBeTruthy();
    });
  });

  describe('getListAllPackage', () => {
    it('should return all packages', async () => {
      mockedPackageService.getPackageList.mockResolvedValue([]);
      const result = await controller.getListAllPackage({ packType: 1 });
      expect(result).toBeTruthy();
    });
  });

  describe('getNewPackagePage', () => {
    it('should return new package page data', async () => {
      mockedPackageService.getPackagePageCommonData.mockResolvedValue({ status: 'status' });
      const result = await controller.getNewPackagePage();
      expect(result).toBeTruthy();
    });
  });

  describe('createPackage', () => {
    it('should create a new package', async () => {
      const createdPackage = {};
      mockedPackageService.createPackage.mockResolvedValue({});
      const result = await controller.createPackage({} as any);
      expect(result).toEqual(createdPackage);
    });
  });

  describe('exportPackage', () => {
    it('should export a package', async () => {
      const exportedPackage = {};
      mockedPackageService.exportPackages.mockResolvedValue({});
      const result = await controller.exportPackages({} as any, {} as any);
      expect(result).toEqual(exportedPackage);
    });
  });

  describe('importPackage', () => {
    it('should import a package', async () => {
      const importedPackage = {};
      mockedPackageService.importPackages.mockResolvedValue({});
      const result = await controller.importPackages({} as any);
      expect(result).toEqual(importedPackage);
    });
  });

  describe('getPackageDetailPage', () => {
    it('should return package detail page data', async () => {
      mockedPackageService.getPackagePageCommonData.mockResolvedValue({ status: 'status' });
      mockedPackageService.getPackageDetail.mockResolvedValue({});
      const result = await controller.getPackageDetailPage('id');
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if package not found', async () => {
      mockedPackageService.getPackagePageCommonData.mockResolvedValue({ status: 'status' });
      mockedPackageService.getPackageDetail.mockResolvedValue(null);
      await expect(controller.getPackageDetailPage('id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updatePackage', () => {
    it('should update a package', async () => {
      mockedPackageService.getPackageDetail.mockResolvedValue({});
      mockedPackageService.updatePackage.mockResolvedValue({});
      const result = await controller.updatePackage('id', {} as any);
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if package not found', async () => {
      mockedPackageService.getPackageDetail.mockResolvedValue(null);
      await expect(controller.updatePackage('id', {} as any)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deletePackage', () => {
    it('should delete a package', async () => {
      mockedPackageService.getPackageDetail.mockResolvedValue({ reservationsPackages: [] });
      mockedPackageService.deletePackage.mockResolvedValue({ reservationsPackages: [] });

      const result = await controller.deletePackage('id');

      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if package not found', async () => {
      const id = 'uuid';
      mockedPackageService.getPackageDetail.mockResolvedValue(null);

      await expect(controller.deletePackage(id)).rejects.toThrow(NotFoundException);
    });
  });
});
