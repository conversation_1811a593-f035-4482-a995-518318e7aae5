import { Test, TestingModule } from '@nestjs/testing';
import { CalendarBlockController } from '@src/modules/terry/controllers/calendar-block.controller';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@vietnam/cnga-middleware';
import { PackageService } from '@src/modules/shared/package/package.service';

describe('CalendarBlockController', () => {
  let controller: CalendarBlockController;
  const mockedCalendarBlockService = {
    getCalendarBlockDates: jest.fn(),
    setCalendarBlockDate: jest.fn(),
    deleteCalendarBlockDate: jest.fn(),
  };
  const mockedConfigurationService = {
    getConfigurations: jest.fn(),
    getConfiguration: jest.fn(),
  };
  const mockedPackageService = {
    getPackagePageCommonData: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CalendarBlockController],
      providers: [
        {
          provide: CalendarBlockService,
          useValue: mockedCalendarBlockService,
        },
        {
          provide: PackageService,
          useValue: mockedPackageService,
        },
        {
          provide: ConfigurationService,
          useValue: mockedConfigurationService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<CalendarBlockController>(CalendarBlockController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getListCalendarBlockDatePage', () => {
    it('should return calendar block range with block range config', async () => {
      const mockConfigs = { configKey: ConfigKeyEnum.CALENDAR_BLOCK_RANGE, value: 'some-range' };
      mockedConfigurationService.getConfiguration.mockResolvedValue(mockConfigs);
      mockedPackageService.getPackagePageCommonData.mockResolvedValueOnce([]);

      const result = await controller.getListCalendarBlockDatePage({});
      expect(result).toBeTruthy();
    });
    it('should return calendar block range with no block range config', async () => {
      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedPackageService.getPackagePageCommonData.mockResolvedValueOnce([]);

      const result = await controller.getListCalendarBlockDatePage({ packType: 2 });
      expect(result).toBeTruthy();
    });
  });

  describe('getListCalendarBlockDate', () => {
    it('should return block dates', async () => {
      const query = { startDate: '2023-01-01', endDate: '2023-12-31' } as any;
      const mockBlockDates = [{ date: '2023-01-01' }];
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(mockBlockDates);

      const result = await controller.getListCalendarBlockDate(query);
      expect(result).toEqual({ blockDates: mockBlockDates });
    });
  });

  describe('setCalendarBlockDate', () => {
    it('should set calendar block date', async () => {
      const body = { date: '2023-01-01' } as any;
      const mockResponse = { success: true };
      mockedCalendarBlockService.setCalendarBlockDate.mockResolvedValue(mockResponse);

      const result = await controller.setCalendarBlockDate(body);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteCalendarBlockDate', () => {
    it('should delete calendar block date', async () => {
      const body = { date: '2023-01-01' } as any;
      const mockResponse = { success: true };
      mockedCalendarBlockService.deleteCalendarBlockDate.mockResolvedValue(mockResponse);

      const result = await controller.deleteCalendarBlockDate(body);
      expect(result).toEqual(mockResponse);
    });
  });
});
