import { Test, TestingModule } from '@nestjs/testing';
import { ReservationController } from '@src/modules/terry/controllers/reservation.controller';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { NotFoundException } from '@nestjs/common';
import { ReservationStatus } from '@src/common/enums/reservation';
import { CURAMA_AUTH_SERVICE, CURAMA_AUTHORIZATION_SERVICE } from '@vietnam/cnga-middleware';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { AdditionActivityService } from '@src/modules/shared/addition-activity/addition-activity.service';

describe('ReservationController', () => {
  let controller: ReservationController;
  const mockedReservationService = {
    getReservationPageCommonData: jest.fn(),
    getReservations: jest.fn(),
    getReservationDetail: jest.fn(),
    updateReservation: jest.fn(),
    createAddingDocumentUrl: jest.fn(),
    reactiveAddingDocumentUrl: jest.fn(),
    getAdditionalInformationTemplate: jest.fn(),
  };
  const mockedAuthService = {
    getOauth2Url: jest.fn(),
    signOut: jest.fn(),
    authenticate: jest.fn(),
  };
  const mockedReservationOptionService = {
    getEnableReservationOptions: jest.fn(),
  };
  const mockedAdditionActivityService = {
    getActivityByAddition: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReservationController],
      providers: [
        {
          provide: ReservationService,
          useValue: mockedReservationService,
        },
        {
          provide: ReservationOptionService,
          useValue: mockedReservationOptionService,
        },
        {
          provide: AdditionActivityService,
          useValue: mockedAdditionActivityService,
        },
        {
          provide: CURAMA_AUTH_SERVICE,
          useValue: mockedAuthService,
        },
        {
          provide: CURAMA_AUTHORIZATION_SERVICE,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<ReservationController>(ReservationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getListReservationsPage', () => {
    it('should return reservations list page data', async () => {
      const query = {} as any;
      const status = 'some status';
      const reservations = [];
      const pagination = {};

      mockedReservationService.getReservationPageCommonData.mockResolvedValueOnce({ status });
      mockedReservationService.getReservations.mockResolvedValueOnce({ reservations, pagination });

      const result = await controller.getListReservationsPage(query);
      expect(result).toBeTruthy();
    });

    it('should return reservations list page data', async () => {
      const query = { packType: 2 } as any;
      const status = 'some status';
      const reservations = [];
      const pagination = {};

      mockedReservationService.getReservationPageCommonData.mockResolvedValueOnce({ status });
      mockedReservationService.getReservations.mockResolvedValueOnce({ reservations, pagination });

      const result = await controller.getListReservationsPage(query);
      expect(result).toBeTruthy();
    });
  });

  describe('getPagingListReservation', () => {
    it('should return paginated reservations', async () => {
      const query = {} as any;
      const reservations = [];

      mockedReservationService.getReservations.mockResolvedValueOnce(reservations);

      const result = await controller.getPagingListReservation(query);

      expect(result).toEqual(reservations);
    });
  });

  describe('getReservationDetailPage', () => {
    it('should return reservation detail page data', async () => {
      const id = 'some-uuid';
      const status = 'some status';
      const reservationOptions = [
        {
          id: 'option1',
          name: 'option1',
          title: 'Option 1',
          type: 'quantity',
          hasPrice: true,
          detail: { unit: 'unit', pricePerUnit: 100 },
        },
        {
          id: 'option2',
          name: 'option2',
          title: 'Option 2',
          type: 'text',
          hasPrice: false,
          detail: { listOptions: [{ title: 'Option 2-1' }] },
        },
        {
          id: 'option3',
          name: 'extendedWarranty',
          title: 'Option 2',
          type: 'text',
          hasPrice: true,
          detail: { listOptions: [{ title: 'Option 2-1' }] },
        },
      ];
      const reservationDetail = {
        options: {
          option1: { quantity: 1 },
          option2: { value: 'Option 2-1' },
          extendedWarranty: { value: 'Option 2-1', fee: 200 },
        },
        additionalInformation: [{ id: 'additionalInformationId', deleteAt: '2022-01-01' }],
        packType: 1,
      };

      mockedReservationService.getReservationPageCommonData.mockResolvedValueOnce({ status });
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.getAdditionalInformationTemplate.mockReturnValueOnce([1, 2]);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValueOnce(reservationOptions);
      const result = await controller.getReservationDetailPage(id);

      expect(result).toBeTruthy();
    });

    it('should return reservation detail page data 2', async () => {
      const id = 'some-uuid';
      const status = 'some status';
      const reservationOptions = [
        {
          id: 'option3',
          name: 'extendedWarranty',
          title: 'Option 2',
          type: 'text',
          hasPrice: true,
          detail: { listOptions: [{ title: 'Option 2-1' }] },
        },
      ];
      const reservationDetail = {
        options: {
          option1: { quantity: 1 },
          option2: { value: 'Option 2-1' },
          extendedWarranty: { value: 'Option 2-1', fee: 0 },
        },
        additionalInformation: [{ id: 'additionalInformationId', deleteAt: '2022-01-01' }],
        packType: 1,
      };

      mockedReservationService.getReservationPageCommonData.mockResolvedValueOnce({ status });
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.getAdditionalInformationTemplate.mockReturnValueOnce([1, 2]);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValueOnce(reservationOptions);
      mockedAdditionActivityService.getActivityByAddition.mockResolvedValueOnce([]);
      const result = await controller.getReservationDetailPage(id);

      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if reservation not found', async () => {
      const id = 'some-uuid';
      const status = 'some status';
      const reservationOptions = [];

      mockedReservationService.getReservationPageCommonData.mockResolvedValueOnce({ status, reservationOptions });
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(null);

      await expect(controller.getReservationDetailPage(id)).rejects.toThrow(NotFoundException);
    });
  });

  describe('createAddingDocumentUrl', () => {
    it('should throw NotFoundException if reservation not found', async () => {
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(null);
      try {
        await controller.createAddingDocumentUrl('id', { templateId: 1 });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it('should throw BadRequestException if Invalid template id', async () => {
      const reservationDetail = { additionalInformation: [] };
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.getAdditionalInformationTemplate.mockReturnValueOnce([3]);
      try {
        await controller.createAddingDocumentUrl('id', { templateId: 1 });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should create adding document url', async () => {
      const reservationDetail = { additionalInformation: [] };
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.getAdditionalInformationTemplate.mockReturnValueOnce([1, 2]);
      mockedReservationService.createAddingDocumentUrl.mockResolvedValueOnce({});

      const result = await controller.createAddingDocumentUrl('id', { templateId: 1 });

      expect(result).toBeTruthy();
    });

    it('should throw BadRequestException if Current document template is in used', async () => {
      const reservationDetail = { additionalInformation: [{ templateType: 1, isEnable: true }] };
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.getAdditionalInformationTemplate.mockReturnValueOnce([1]);
      try {
        await controller.createAddingDocumentUrl('id', { templateId: 1 });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it('should reactive adding document url', async () => {
      const reservationDetail = { additionalInformation: [{ templateType: 1, isEnable: false }] };
      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.getAdditionalInformationTemplate.mockReturnValueOnce([1, 2]);
      mockedReservationService.reactiveAddingDocumentUrl.mockResolvedValueOnce({});

      const result = await controller.createAddingDocumentUrl('id', { templateId: 1 });

      expect(result).toBeTruthy();
    });
  });

  describe('confirmReservation', () => {
    it('should confirm reservation', async () => {
      const id = 'some-uuid';
      const reservationDetail = {};

      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.updateReservation.mockResolvedValueOnce({ status: ReservationStatus.INPROGRESS });

      const result = await controller.confirmReservation(id, {});

      expect(result).toEqual({ status: ReservationStatus.INPROGRESS });
    });

    it('should throw NotFoundException if reservation not found', async () => {
      const id = 'some-uuid';

      mockedReservationService.getReservationDetail.mockResolvedValueOnce(null);

      await expect(controller.confirmReservation(id, {})).rejects.toThrow(NotFoundException);
    });
  });

  describe('cancelReservation', () => {
    it('should cancel reservation', async () => {
      const id = 'some-uuid';
      const reservationDetail = {};

      mockedReservationService.getReservationDetail.mockResolvedValueOnce(reservationDetail);
      mockedReservationService.updateReservation.mockResolvedValueOnce({ status: ReservationStatus.CANCELED });

      const result = await controller.cancelReservation(id);

      expect(result).toEqual({ status: ReservationStatus.CANCELED });
    });

    it('should throw NotFoundException if reservation not found', async () => {
      const id = 'some-uuid';

      mockedReservationService.getReservationDetail.mockResolvedValueOnce(null);

      await expect(controller.cancelReservation(id)).rejects.toThrow(NotFoundException);
    });
  });
});
