import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationOptionService } from '../../src/modules/shared/reservation-option/reservation-option.service';
import { ReservationOptionEntity } from '../../src/entities/reservation-option.entity';
import { UpdateReservationOptionDto } from '../../src/modules/shared/reservation-option/dtos/update-reservation-option.dto';
import { ObjectCacheService } from '../../src/modules/shared/object-cache/object-cache.service';

describe('ReservationOptionService', () => {
  let service: ReservationOptionService;
  const mockedReservationOptionEntity = {
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };
  const mockedObjectCacheService = {
    get: jest.fn(),
    addOrUpdate: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReservationOptionService,
        {
          provide: getRepositoryToken(ReservationOptionEntity),
          useValue: mockedReservationOptionEntity,
        },
        {
          provide: ObjectCacheService,
          useValue: mockedObjectCacheService,
        },
      ],
    }).compile();

    service = module.get<ReservationOptionService>(ReservationOptionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should reset cache on module init', async () => {
      service.resetCache = jest.fn().mockResolvedValue(undefined);
      await service.onModuleInit();
      expect(service.resetCache).toHaveBeenCalled();
    });
  });

  describe('getEnableReservationOptions', () => {
    it('should return cached data if available', async () => {
      const cachedData = [{ id: '1', enabled: true }];
      mockedObjectCacheService.get.mockResolvedValue(cachedData);

      const result = await service.getEnableReservationOptions(1);
      expect(result).toEqual(cachedData);
    });

    it('should return reservation options', async () => {
      mockedObjectCacheService.get.mockResolvedValue(null);
      mockedReservationOptionEntity.find.mockResolvedValue([]);
      mockedObjectCacheService.addOrUpdate.mockResolvedValue(undefined);
      const result = await service.getEnableReservationOptions(1);
      expect(result).toEqual([]);
    });
  });

  describe('getAllReservationOptions', () => {
    it('should return all reservation options', async () => {
      const repositoryData = [{ id: '1', enabled: true }];
      mockedReservationOptionEntity.find.mockResolvedValue(repositoryData);

      const result = await service.getAllReservationOptions();
      expect(result).toEqual(repositoryData);
    });
  });

  describe('getReservationOptionDetail', () => {
    it('should return reservation option detail by id', async () => {
      const id = '1';
      const repositoryData = { id, enabled: true };
      mockedReservationOptionEntity.findOne.mockResolvedValue(repositoryData);

      const result = await service.getReservationOptionDetail(id);
      expect(result).toEqual(repositoryData);
      expect(mockedReservationOptionEntity.findOne).toHaveBeenCalledWith({ where: { id } });
    });
  });

  describe('updateReservationOption', () => {
    it('should update reservation option and reset cache', async () => {
      const id = '1';
      const updateDto = {
        hasPrice: true,
        type: 'type',
        enabled: true,
        detail: {},
      } as UpdateReservationOptionDto;
      mockedReservationOptionEntity.update.mockResolvedValue(undefined);
      service.resetCache = jest.fn().mockResolvedValue(undefined);

      const result = await service.updateReservationOption(id, updateDto);
      expect(result).toEqual({ data: {} });
      expect(mockedReservationOptionEntity.update).toHaveBeenCalledWith(
        { id },
        {
          hasPrice: updateDto.hasPrice,
          type: updateDto.type,
          enabled: updateDto.enabled,
          detail: updateDto.detail as any,
        },
      );
    });
  });

  describe('resetCache', () => {
    it('should delete cache', async () => {
      mockedObjectCacheService.delete.mockResolvedValue(undefined);
      await service.resetCache();
    });
  });
});
