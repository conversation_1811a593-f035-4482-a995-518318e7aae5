import { Test, TestingModule } from '@nestjs/testing';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Request } from 'express';
import { NotificationService } from '../../src/modules/shared/notification/notification.service';

describe('NotificationService', () => {
  let service: NotificationService;
  const mockedAMQPConnection = {
    publish: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: AmqpConnection,
          useValue: mockedAMQPConnection,
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmailToUser', () => {
    it('should publish email notification', async () => {
      const data = {
        customerEmail: '<EMAIL>',
        emailTemplate: 'template',
        emailSubject: 'subject',
        emailProperties: {},
      };
      const request = {
        headers: { 'x-request-id': '123' },
        terrySession: { adminId: 'admin123' },
      } as unknown as Request;
      mockedAMQPConnection.publish.mockResolvedValueOnce({});
      const result = await service.sendEmailToUser(data, request);
      expect(result).toBeDefined();
    });

    it('should return void undefined customerEmail', async () => {
      const data = {
        customerEmail: '',
        emailTemplate: 'template',
        emailSubject: 'subject',
        emailProperties: {},
      };
      const request = {
        headers: { 'x-request-id': '123' },
        terrySession: { adminId: 'admin123' },
      } as unknown as Request;
      const result = await service.sendEmailToUser(data, request);
      expect(result).toBeUndefined();
    });

    it('should log error if publish fails', async () => {
      const data = {
        customerEmail: '<EMAIL>',
        emailTemplate: 'template',
        emailSubject: 'subject',
        emailProperties: {},
      };
      const request = {
        headers: { 'x-request-id': '123' },
      } as unknown as Request;
      const error = new Error('publish failed');
      mockedAMQPConnection.publish.mockRejectedValueOnce(error);
      const result = await service.sendEmailToUser(data, request);
      expect(result).toBeUndefined();
    });
  });

  describe('pushNotificationToUser', () => {
    it('should publish push notification', async () => {
      const data = {
        userId: 'user123',
        message: 'Test message',
        reservationCode: 'res123',
      };
      const request = {
        headers: { 'x-request-id': '123' },
        terrySession: { adminId: 'admin123' },
      } as unknown as Request;
      mockedAMQPConnection.publish.mockResolvedValueOnce({});
      const result = await service.pushNotificationToUser(data, request);
      expect(result).toBeDefined();
    });

    it('should log error if publish fails', async () => {
      const data = {
        userId: 'user123',
        message: 'Test message',
        reservationCode: 'res123',
      };
      const request = {
        headers: { 'x-request-id': '123' },
        terrySession: {},
      } as unknown as Request;
      const error = new Error('publish failed');
      mockedAMQPConnection.publish.mockRejectedValueOnce(error);
      const result = await service.pushNotificationToUser(data, request);
      expect(result).toBeUndefined();
    });
  });

  describe('buildEmailPropertiesForNewReservation', () => {
    it('should return email properties', () => {
      const data = {
        reservationCode: 'res123',
        lastname: 'lastname',
        firstname: 'firstname',
        fee: '100',
        responseDate: '2021/01/01',
        reservationDetail: [
          {
            name: 'name',
            quantity: 1,
            totalFee: '100',
            fee: '100',
          },
          {
            name: 'name',
            quantity: 0,
            totalFee: '100',
            fee: '100',
          },
        ],
        deliveryContent: true,
      };

      const result = service.buildEmailPropertiesForNewReservation(data);
      expect(result).toBeTruthy();
    });
    it('should return email properties without detail', () => {
      const data: any = {
        reservationCode: 'res123',
        lastname: 'lastname',
        firstname: 'firstname',
        fee: '100',
        responseDate: '2021/01/01',
      };

      const result = service.buildEmailPropertiesForNewReservation(data);
      expect(result).toBeTruthy();
    });
    it('should return email properties detail empty', () => {
      const data = {
        reservationCode: 'res123',
        lastname: 'lastname',
        firstname: 'firstname',
        fee: '100',
        reservationDetail: [],
        responseDate: '2021/01/01',
        deliveryContent: false,
      };

      const result = service.buildEmailPropertiesForNewReservation(data);
      expect(result).toBeTruthy();
    });
  });
});
