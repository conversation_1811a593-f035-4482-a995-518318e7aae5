import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CalendarBlockService } from '../../src/modules/shared/calendar-block/calendar-block.service';
import { CalendarBlockEntity } from '../../src/entities/calendar-block.entity';
import { CalendarBlockQueryDto } from '../../src/modules/shared/calendar-block/dtos/calendar-block-query.dto';
import { SetCalendarBlockDateDto } from '../../src/modules/shared/calendar-block/dtos/set-calendar-block-date.dto';
import { DeleteCalendarBlockDateDto } from '../../src/modules/shared/calendar-block/dtos/delete-calendar-block-date.dto';

describe('CalendarBlockService', () => {
  let service: CalendarBlockService;
  const mockedCalendarBlockEntity = {
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalendarBlockService,
        {
          provide: getRepositoryToken(CalendarBlockEntity),
          useValue: mockedCalendarBlockEntity,
        },
      ],
    }).compile();

    service = module.get<CalendarBlockService>(CalendarBlockService);
  });

  describe('getCalendarBlockDates', () => {
    it('should return block dates within the specified range', async () => {
      const params: CalendarBlockQueryDto = { start: '2023-01-01', end: '2023-01-31', packType: 1 };
      mockedCalendarBlockEntity.find.mockResolvedValue([{ blockDate: '2023-01-10' }, { blockDate: '2023-01-20' }]);

      const result = await service.getCalendarBlockDates(params);
      expect(result).toEqual(['2023-01-10', '2023-01-20']);
    });
  });

  describe('setCalendarBlockDate', () => {
    it('should insert block dates and ignore duplicates', async () => {
      const data: SetCalendarBlockDateDto = { dates: ['2023-01-10', '2023-01-20'], packType: 1 };
      mockedCalendarBlockEntity.createQueryBuilder.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        orIgnore: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValueOnce({}),
      });
      const result = await service.setCalendarBlockDate(data);
      expect(result).toBeTruthy();
    });
  });

  describe('deleteCalendarBlockDate', () => {
    it('should delete block dates', async () => {
      const data: DeleteCalendarBlockDateDto = { dates: ['2023-01-10', '2023-01-20'], packType: 1 };
      const mockDeleteResult = { affected: 2 };
      mockedCalendarBlockEntity.delete.mockResolvedValue(mockDeleteResult as any);
      const result = await service.deleteCalendarBlockDate(data);
      expect(result).toEqual(mockDeleteResult);
    });
  });
});
