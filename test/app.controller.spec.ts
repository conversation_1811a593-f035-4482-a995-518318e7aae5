import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from '@src/app.controller';

describe('AppController', () => {
  let appController: AppController;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
    }).compile();

    appController = moduleRef.get<AppController>(AppController);
  });

  describe('root', () => {
    it('should return OK', () => {
      expect(appController.healthCheck()).toBeTruthy();
    });
  });
});
