import { Test, TestingModule } from '@nestjs/testing';
import { DocumentService } from '@src/modules/shared/document/document.service';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import * as crypto from 'crypto';

const s3ClientSendMock = jest.fn();
jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementationOnce(() => ({
    send: s3ClientSendMock,
  })),
  PutObjectCommand: jest.fn(),
}));
jest.mock('sharp', () => {
  return jest.fn(() => ({
    webp: jest.fn().mockReturnThis(),
    toBuffer: jest.fn().mockResolvedValue(Buffer.from('mocked buffer')),
  }));
});

describe('DocumentService', () => {
  let service: DocumentService;
  const mockUUID = crypto.randomUUID();
  const mockEnv = process.env.LABO_CONFIDENTIAL_S3_BUCKET_NAME;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DocumentService],
    }).compile();

    service = module.get<DocumentService>(DocumentService);
  });

  it('should upload a file successfully', async () => {
    const mockFile = {
      buffer: Buffer.from('mock file content'),
      mimetype: 'application/pdf',
      originalname: 'test.pdf',
    } as Express.Multer.File;

    const reservationId = 'reservation123';
    const additionId = 'addition123';
    const additionAttemptId = 'attempt123';
    const expectedS3Path = `labo-install-pack/${reservationId}/${additionId}/${additionAttemptId}/${mockUUID}.pdf`;
    jest.spyOn(crypto, 'randomUUID').mockReturnValueOnce(mockUUID);

    const result = await service.uploadFile(mockFile, reservationId, additionId, additionAttemptId);
    expect(PutObjectCommand).toHaveBeenCalledWith({
      Bucket: mockEnv,
      Key: expectedS3Path,
      Body: mockFile.buffer,
      ContentType: mockFile.mimetype,
    });
    expect(result).toBeTruthy();
  });

  it('should upload a file successfully', async () => {
    const mockFile = {
      buffer: Buffer.from('mock file content'),
      mimetype: 'image/png',
      originalname: 'test.png',
    } as Express.Multer.File;

    const reservationId = 'reservation123';
    const additionId = 'addition123';
    const additionAttemptId = 'attempt123';
    jest.spyOn(crypto, 'randomUUID').mockReturnValueOnce(mockUUID);
    try {
      await service.uploadFile(mockFile, reservationId, additionId, additionAttemptId);
    } catch (error) {
      expect(error).toBeTruthy();
    }
  });

  it('should throw an error if S3 upload fails', async () => {
    const mockFile = {
      buffer: Buffer.from('mock file content'),
      mimetype: 'image/jpg',
      originalname: 'test.jpg',
    } as Express.Multer.File;

    const reservationId = 'reservation123';
    const additionId = 'addition123';
    const additionAttemptId = 'attempt123';
    jest.spyOn(crypto, 'randomUUID').mockReturnValueOnce(mockUUID);
    s3ClientSendMock.mockImplementation(() => {
      throw new Error('error');
    });
    try {
      await service.uploadFile(mockFile, reservationId, additionId, additionAttemptId);
    } catch (error) {
      expect(error).toBeTruthy();
    }
  });
});
