import { Test, TestingModule } from '@nestjs/testing';
import { ObjectCacheService } from '../../src/modules/shared/object-cache/object-cache.service';
import { getRedisToken } from '@liaoliaots/nestjs-redis';

describe('ObjectCacheService', () => {
  let objectCacheService: ObjectCacheService;
  const mockedRedis = {
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    ttl: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ObjectCacheService, { provide: getRedisToken('default'), useValue: mockedRedis }],
    }).compile();
    objectCacheService = module.get<ObjectCacheService>(ObjectCacheService);
  });

  describe('get', () => {
    it('should return undefined when data is not found', async () => {
      const key = 'key';
      const bucket = 1;
      mockedRedis.get.mockResolvedValue(undefined);

      mockedRedis.ttl.mockResolvedValue(100);
      const result = await objectCacheService.get(key, bucket);

      expect(result).toBeUndefined();
      expect(mockedRedis.get).toHaveBeenCalledWith(`labo-install-pack:${bucket}:${key}`);
    });

    it('should return parsed data when data is found', async () => {
      const key = 'key';
      const bucket = 1;
      const data = '{"name":"test"}';
      mockedRedis.get.mockResolvedValue(data);

      const result = await objectCacheService.get(key, bucket);

      expect(result).toEqual({ name: 'test' });
      expect(mockedRedis.get).toHaveBeenCalledWith(`labo-install-pack:${bucket}:${key}`);
    });
  });

  describe('addOrUpdate', () => {
    it('should add or update data successfully', async () => {
      const key = 'key';
      const bucket = 1;
      const data = { name: 'test' };
      const dataString = JSON.stringify(data);

      await objectCacheService.addOrUpdate(key, bucket, data);

      expect(mockedRedis.set).toHaveBeenCalledWith(`labo-install-pack:${bucket}:${key}`, dataString);
    });
    it('should add or update data successfully 2', async () => {
      const key = 'key';
      const bucket = 1;
      const data = { name: 'test' };

      const result = await objectCacheService.addOrUpdate(key, bucket, data, 100);

      expect(result).toBeUndefined();
    });
  });

  describe('addOrUpdateRaw', () => {
    it('should add or update data successfully', async () => {
      const key = 'key';
      const bucket = 1;
      const data = { name: 'test' };

      await objectCacheService.addOrUpdateRaw(key, bucket, data);

      expect(mockedRedis.set).toHaveBeenCalledWith(`labo-install-pack:${bucket}:${key}`, data);
    });
    it('should add or update data successfully 2', async () => {
      const key = 'key';
      const bucket = 1;
      const data = { name: 'test' };

      const result = await objectCacheService.addOrUpdateRaw(key, bucket, data, 100);

      expect(result).toBeUndefined();
    });
  });

  describe('delete', () => {
    it('should delete data successfully', async () => {
      const key = 'key';
      const bucket = 1;

      const result = await objectCacheService.delete(key, bucket);

      expect(result).toBeUndefined();
    });
  });
});
