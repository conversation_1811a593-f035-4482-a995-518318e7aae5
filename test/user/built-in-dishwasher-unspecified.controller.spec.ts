import { Test, TestingModule } from '@nestjs/testing';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { NotFoundException } from '@nestjs/common';
import { PackType } from '@src/common/enums/pack-type';
import { ConfigurationEntity } from '@src/entities/configuration.entity';
import { Py3Service } from '@vietnam/curama-py3-api';
import { BuiltInDishwasherUnspecifiedController } from '@src/modules/user/controllers/built-in-dishwasher-unspecified.controller';

describe('BuiltInDishwasherUnspecifiedController', () => {
  let controller: BuiltInDishwasherUnspecifiedController;
  let reservationService: jest.Mocked<ReservationService>;
  let configurationService: jest.Mocked<ConfigurationService>;
  let calendarBlockService: jest.Mocked<CalendarBlockService>;
  let packageService: jest.Mocked<PackageService>;
  let reservationOptionService: jest.Mocked<ReservationOptionService>;

  const mockReservationService = {
    createReservation: jest.fn(),
  };

  const mockConfigurationService = {
    getConfiguration: jest.fn(),
  };

  const mockCalendarBlockService = {
    getCalendarBlockDates: jest.fn(),
  };

  const mockPackageService = {
    getEnablePackages: jest.fn(),
  };

  const mockReservationOptionService = {
    getEnableReservationOptions: jest.fn(),
  };

  const mockedPY3Service = {
    getUserInfo: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BuiltInDishwasherUnspecifiedController],
      providers: [
        {
          provide: ReservationService,
          useValue: mockReservationService,
        },
        {
          provide: ConfigurationService,
          useValue: mockConfigurationService,
        },
        {
          provide: CalendarBlockService,
          useValue: mockCalendarBlockService,
        },
        {
          provide: PackageService,
          useValue: mockPackageService,
        },
        {
          provide: ReservationOptionService,
          useValue: mockReservationOptionService,
        },
        {
          provide: Py3Service,
          useValue: mockedPY3Service,
        },
      ],
    }).compile();

    controller = module.get<BuiltInDishwasherUnspecifiedController>(BuiltInDishwasherUnspecifiedController);
    reservationService = module.get(ReservationService);
    configurationService = module.get(ConfigurationService);
    calendarBlockService = module.get(CalendarBlockService);
    packageService = module.get(PackageService);
    reservationOptionService = module.get(ReservationOptionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('registerUnspecified', () => {
    it('should store form data in session for unspecified reservation', async () => {
      const req: any = { session: {} };
      const body = { forms: { name: 'test' } };

      const result = await controller.registerUnspecified(req, body);

      expect(req.session.builtInDishwasherPackUnspecifiedProductReservationForm).toEqual(body);
      expect(result).toEqual({});
    });

    it('should handle errors gracefully', async () => {
      const req: any = { session: {} };
      const body = { forms: { name: 'test' } };

      // Mock an error by overriding the session property
      Object.defineProperty(req.session, 'builtInDishwasherPackUnspecifiedProductReservationForm', {
        set: () => {
          throw new Error('Session error');
        },
      });

      const result = await controller.registerUnspecified(req, body);

      expect(result).toEqual({ error: 'Session error' });
    });
  });

  describe('dishwasherReservationPageUnspecified', () => {
    const mockReq = {
      user: { clientId: 'user123' },
    };

    const mockConfig: Partial<ConfigurationEntity> = { value: '7' };
    const mockBlockedDates = ['2025-06-20', '2025-06-21'];
    const mockActivePackages: any[] = [{ id: 'pkg1', name: 'Package 1' }];
    const mockReservationOptions: any[] = [{ id: 'opt1', name: 'Option 1' }];

    beforeEach(() => {
      configurationService.getConfiguration.mockResolvedValue(mockConfig as ConfigurationEntity);
      calendarBlockService.getCalendarBlockDates.mockResolvedValue(mockBlockedDates);
      packageService.getEnablePackages.mockResolvedValue(mockActivePackages);
      reservationOptionService.getEnableReservationOptions.mockResolvedValue(mockReservationOptions);
    });

    it('should return reservation page data for unspecified type', async () => {
      const result = await controller.dishwasherReservationPageUnspecified(mockReq, {} as any);

      expect(configurationService.getConfiguration).toHaveBeenCalledWith(
        expect.any(String),
        PackType.BUILD_IN_DISHWASHER,
      );
      expect(result).toBeDefined();
    });

    it('should handle null configuration value', async () => {
      configurationService.getConfiguration.mockResolvedValue(null);

      const result = await controller.dishwasherReservationPageUnspecified(mockReq, {} as any);

      expect(result).toBeDefined();
    });
  });

  describe('reservationConfirmPageUnspecified', () => {
    const mockReq = {
      user: { clientId: 'user123' },
      session: {
        builtInDishwasherPackUnspecifiedProductReservationForm: {
          forms: { name: 'test' },
        },
      },
    };

    const mockConfig: Partial<ConfigurationEntity> = { value: '7' };
    const mockBlockedDates = ['2025-06-20', '2025-06-21'];
    const mockActivePackages: any[] = [{ id: 'pkg1', name: 'Package 1' }];
    const mockReservationOptions: any[] = [{ id: 'opt1', name: 'Option 1' }];

    beforeEach(() => {
      configurationService.getConfiguration.mockResolvedValue(mockConfig as ConfigurationEntity);
      calendarBlockService.getCalendarBlockDates.mockResolvedValue(mockBlockedDates);
      packageService.getEnablePackages.mockResolvedValue(mockActivePackages);
      reservationOptionService.getEnableReservationOptions.mockResolvedValue(mockReservationOptions);
    });

    it('should return confirm page data for unspecified type', async () => {
      const result = await controller.reservationConfirmPageUnspecified(mockReq, {} as any);

      expect(result).toBeDefined();
      expect(configurationService.getConfiguration).toHaveBeenCalledWith(
        expect.any(String),
        PackType.BUILD_IN_DISHWASHER,
      );
    });

    it('should throw NotFoundException when no form in session', async () => {
      const reqWithoutForm = {
        user: { clientId: 'user123' },
        session: {},
      };

      await expect(controller.reservationConfirmPageUnspecified(reqWithoutForm, {} as any)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle null configuration value', async () => {
      configurationService.getConfiguration.mockResolvedValue(null);

      const result = await controller.reservationConfirmPageUnspecified(mockReq, {} as any);

      expect(result).toBeDefined();
    });
  });

  describe('confirmUnspecified', () => {
    const mockReq: any = {
      user: { clientId: 'user123' },
      session: {
        builtInDishwasherPackUnspecifiedProductReservationForm: {
          forms: { name: 'test' },
        },
      },
    };

    const mockBody = {
      idempotencyKey: '12345',
      lastname: 'Test',
      firstname: 'User',
      email: '<EMAIL>',
      phoneNumber: '1234567890',
      postalcode: '1234567',
      prefecture: 'Tokyo',
      city: 'Shibuya',
      address: 'Test Address',
    };

    const mockRes = {
      redirect: jest.fn(),
    };

    beforeEach(() => {
      reservationService.createReservation.mockResolvedValue({ code: 'CRES123456' });
    });

    it('should create unspecified reservation successfully', async () => {
      await controller.confirmUnspecified(mockReq, mockRes as any, mockBody);

      expect(reservationService.createReservation).toHaveBeenCalledWith(
        expect.objectContaining({
          packType: PackType.BUILD_IN_DISHWASHER,
          userId: 'user123',
          packages: [],
          workingDate1: null,
          workingDate2: null,
          mailTemplate: expect.any(String),
          idempotencyKey: expect.any(String),
          lastname: 'Test',
          firstname: 'User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          postalcode: '1234567',
          prefecture: 'Tokyo',
          city: 'Shibuya',
          address: 'Test Address',
        }),
        mockReq,
      );

      expect(mockReq.session.builtInDishwasherPackUnspecifiedReservationCode).toBe('CRES123456');
      expect(mockRes.redirect).toHaveBeenCalledWith('/built-in-dishwasher-pack/reserve/thanks/');
    });

    it('should throw NotFoundException when no form in session', async () => {
      const reqWithoutForm: any = {
        user: { clientId: 'user123' },
        session: {},
      };

      await expect(controller.confirmUnspecified(reqWithoutForm, mockRes as any, mockBody)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('reservationThankPageUnspecified', () => {
    const mockReq: any = {
      user: { clientId: 'user123' },
      session: {
        builtInDishwasherPackUnspecifiedProductReservationForm: {
          forms: { name: 'test' },
        },
        builtInDishwasherPackUnspecifiedReservationCode: 'CRES123456',
      },
    };

    const mockActivePackages: any[] = [{ id: 'pkg1', name: 'Package 1' }];

    beforeEach(() => {
      packageService.getEnablePackages.mockResolvedValue(mockActivePackages);
    });

    it('should return thank page data for unspecified type', async () => {
      const result = await controller.reservationThankPageUnspecified(mockReq, {} as any);

      expect(packageService.getEnablePackages).toHaveBeenCalledWith(PackType.BUILD_IN_DISHWASHER);
      expect(result).toBeDefined();
      expect(mockReq.session.builtInDishwasherPackUnspecifiedProductReservationForm).toBeUndefined();
      expect(mockReq.session.builtInDishwasherPackUnspecifiedReservationCode).toBeUndefined();
    });

    it('should throw NotFoundException when no form in session', async () => {
      const reqWithoutForm: any = {
        user: { clientId: 'user123' },
        session: {},
      };

      await expect(controller.reservationThankPageUnspecified(reqWithoutForm, {} as any)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw NotFoundException when no reservation code in session', async () => {
      const reqWithoutCode: any = {
        user: { clientId: 'user123' },
        session: {
          builtInDishwasherPackUnspecifiedProductReservationForm: {
            forms: { name: 'test' },
          },
        },
      };

      await expect(controller.reservationThankPageUnspecified(reqWithoutCode, {} as any)).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
