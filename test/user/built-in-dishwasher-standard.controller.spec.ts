import { Test, TestingModule } from '@nestjs/testing';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { Py3Service } from '@vietnam/curama-py3-api';
import { BuiltInDishwasherStandardController } from '@src/modules/user/controllers/built-in-dishwasher-standard.controller';
import { BuiltInDishwasherPackageService } from '@src/modules/shared/package/built-in-dishwasher-package.service';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';

describe('BuiltInDishwasherStandardController', () => {
  let controller: BuiltInDishwasherStandardController;
  const mockedReservationService = {
    createReservation: jest.fn(),
    getOneReservation: jest.fn(),
  };
  const mockedConfigurationService = {
    getConfiguration: jest.fn(),
  };
  const mockedCalendarBlockService = {
    getCalendarBlockDates: jest.fn(),
  };
  const mockedBuiltInDishwasherPackageService = {
    getBuiltInDishwasherPackagesOrFail: jest.fn(),
  };
  const mockedReservationOptionService = {
    getEnableReservationOptions: jest.fn(),
  };
  const mockedPY3Service = {
    getUserInfo: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BuiltInDishwasherStandardController],
      providers: [
        {
          provide: ReservationService,
          useValue: mockedReservationService,
        },
        {
          provide: ConfigurationService,
          useValue: mockedConfigurationService,
        },
        {
          provide: CalendarBlockService,
          useValue: mockedCalendarBlockService,
        },
        {
          provide: BuiltInDishwasherPackageService,
          useValue: mockedBuiltInDishwasherPackageService,
        },
        {
          provide: ReservationOptionService,
          useValue: mockedReservationOptionService,
        },
        {
          provide: Py3Service,
          useValue: mockedPY3Service,
        },
      ],
    }).compile();

    controller = module.get<BuiltInDishwasherStandardController>(BuiltInDishwasherStandardController);
  });

  describe('reservationPage', () => {
    it('should return reservation page data', async () => {
      const req = { user: { email: '<EMAIL>' } };
      const configValue = { value: 5 };
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(configValue);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockResolvedValue({});

      const result = await controller.reservationPage(req, { productCode: 'package-code' });

      expect(result).toBeTruthy();
    });
    it('should return reservation page data 2', async () => {
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockResolvedValue({});

      const result = await controller.reservationPage({} as any, { productCode: 'package-code' });

      expect(result).toBeTruthy();
    });
  });

  describe('register', () => {
    it('should add form reservation into session', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockResolvedValue({});
      const result = await controller.register(req, { forms: { packages: [{ id: 'package1' }] } } as any, {
        productCode: 'package-code',
      });
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockRejectedValueOnce('');
      const result = await controller.register(req, { forms: { packages: [] } } as any, {
        productCode: 'package-code',
      });
      expect(result).toBeTruthy();
    });
  });

  describe('reservationConfirmPage', () => {
    it('should return reservation confirm page data', async () => {
      const req = {
        session: { 'builtInDishwasherPackReservationForm_package-code': {} },
        user: { email: '<EMAIL>' },
      };
      const configValue = { value: 5 };
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(configValue);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockResolvedValue({});
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await controller.reservationConfirmPage(req, { productCode: 'package-code' });

      expect(result).toBeTruthy();
    });
    it('should return reservation confirm page data 2', async () => {
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockResolvedValue({});
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await controller.reservationConfirmPage(
        {
          session: { 'builtInDishwasherPackReservationForm_package-code': {} },
        } as any,
        { productCode: 'package-code' },
      );

      expect(result).toBeTruthy();
    });
    it('should throw NotFoundException', async () => {
      try {
        await controller.reservationConfirmPage({ session: {} } as any, { productCode: 'package-code' });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('confirm', () => {
    const payload = {
      idempotencyKey: '12345',
      workingDate1: '2023-01-01',
      workingDate2: '2023-01-02',
      packages: '[{}]',
      email: '<EMAIL>',
      lastname: 'Doe',
      firstname: 'John',
      phoneNumber: '1234567890',
      postalcode: '12345',
      prefecture: 'Tokyo',
      city: 'Shinjuku',
      address: '123 Main St',
      productDeliveryType: 'home',
    };
    it('should confirm a new reservation', async () => {
      const req = {
        session: { 'builtInDishwasherPackReservationForm_package-code': {} },
        user: { email: '<EMAIL>', clientId: '123' },
      };
      mockedReservationService.createReservation.mockResolvedValue({ data: { code: '123' } });
      const result = await controller.confirm(req, { redirect: jest.fn() }, payload as any, {
        productCode: 'package-code',
      });
      expect(result).toBeUndefined();
    });
    it('should confirm a new reservation 2', async () => {
      const req = { session: { 'builtInDishwasherPackReservationForm_package-code': {} } };
      mockedReservationService.createReservation.mockResolvedValue({ data: { code: '123' } });
      const result = await controller.confirm(req, { redirect: jest.fn() }, payload as any, {
        productCode: 'package-code',
      });
      expect(result).toBeUndefined();
    });
    it('should throw error invalid session', async () => {
      try {
        await controller.confirm({ session: {} } as any, { redirect: jest.fn() }, payload as any, {
          productCode: 'package-code',
        });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('reservationThankPage', () => {
    it('should throw error 404', async () => {
      try {
        await controller.reservationThankPage(
          {
            session: {
              'builtInDishwasherPackReservationCode_package-code': '',
              'builtInDishwasherPackReservationForm_package-code': '',
            },
          },
          { productCode: 'package-code' },
        );
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error 404', async () => {
      try {
        await controller.reservationThankPage(
          {
            session: {
              'builtInDishwasherPackReservationCode_package-code': '123',
              'builtInDishwasherPackReservationForm_package-code': '',
            },
          },
          { productCode: 'package-code' },
        );
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should return page data', async () => {
      const reservationData = {
        code: '123',
        additionalInformation: [
          {
            templateType: AdditionalInfoTemplate.BuiltInDishwasher,
            verificationToken: 'verification-token',
          },
        ],
      };
      mockedReservationService.getOneReservation.mockResolvedValue(reservationData);
      mockedBuiltInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail.mockResolvedValue({});
      const result = await controller.reservationThankPage(
        {
          session: {
            'builtInDishwasherPackReservationCode_package-code': '123',
            'builtInDishwasherPackReservationForm_package-code': { forms: '123' },
          },
        },
        { productCode: 'package-code' },
      );
      expect(result).toBeTruthy();
    });
  });
});
