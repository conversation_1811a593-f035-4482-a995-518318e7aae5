import { Test, TestingModule } from '@nestjs/testing';
import { WaterHeaterController } from '../../src/modules/user/controllers/water-heater.controller';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { Py3Service } from '@vietnam/curama-py3-api';

describe('WaterHeaterController', () => {
  let waterHeaterController: WaterHeaterController;
  const mockedReservationService = {
    createReservation: jest.fn(),
  };
  const mockedConfigurationService = {
    getConfiguration: jest.fn(),
  };
  const mockedCalendarBlockService = {
    getCalendarBlockDates: jest.fn(),
  };
  const mockedPackageService = {
    getPackageList: jest.fn(),
    getEnablePackages: jest.fn(),
  };
  const mockedReservationOptionService = {
    getEnableReservationOptions: jest.fn(),
  };
  const mockedPY3Service = {
    getUserInfo: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WaterHeaterController],
      providers: [
        {
          provide: ReservationService,
          useValue: mockedReservationService,
        },
        {
          provide: ConfigurationService,
          useValue: mockedConfigurationService,
        },
        {
          provide: CalendarBlockService,
          useValue: mockedCalendarBlockService,
        },
        {
          provide: PackageService,
          useValue: mockedPackageService,
        },
        {
          provide: ReservationOptionService,
          useValue: mockedReservationOptionService,
        },
        {
          provide: Py3Service,
          useValue: mockedPY3Service,
        },
      ],
    }).compile();

    waterHeaterController = module.get<WaterHeaterController>(WaterHeaterController);
  });
  describe('register', () => {
    const packages = [{ id: 'package1', status: 1, outOfStock: false }];
    it('should add form reservation into session', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await waterHeaterController.register(req, { forms: { packages: [{ id: 'package1' }] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await waterHeaterController.register(req, { forms: { packages: [] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages 2', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await waterHeaterController.register(req, { forms: {} } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages 3', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await waterHeaterController.register(req, {} as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not package detail', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await waterHeaterController.register(req, { forms: { packages: [{ id: 'package2' }] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not package disable', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce([{ id: 'package1', status: 2, outOfStock: false }]);
      const result = await waterHeaterController.register(req, { forms: { packages: [{ id: 'package1' }] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not package out of stock', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce([{ id: 'package1', status: 1, outOfStock: true }]);
      const result = await waterHeaterController.register(req, { forms: { packages: [{ id: 'package1' }] } } as any);
      expect(result).toBeTruthy();
    });
  });
  describe('confirm', () => {
    const payload = {
      idempotencyKey: '12345',
      workingDate1: '2023-01-01',
      workingDate2: '2023-01-02',
      packages: '[{}]',
      email: '<EMAIL>',
      lastname: 'Doe',
      firstname: 'John',
      phoneNumber: '1234567890',
      postalcode: '12345',
      prefecture: 'Tokyo',
      city: 'Shinjuku',
      address: '123 Main St',
    };
    it('should confirm a new reservation', async () => {
      const req = {
        session: { waterHeaterPackReservationForm: {} },
        user: { email: '<EMAIL>', clientId: '123' },
      };
      mockedReservationService.createReservation.mockResolvedValue({ data: { code: '123' } });
      const result = await waterHeaterController.confirm(req, { redirect: jest.fn() }, payload as any);
      expect(result).toBeUndefined();
    });
    it('should confirm a new reservation 2', async () => {
      const req = { session: { waterHeaterPackReservationForm: {} } };
      mockedReservationService.createReservation.mockResolvedValue({});
      const result = await waterHeaterController.confirm(req, { redirect: jest.fn() }, payload as any);
      expect(result).toBeUndefined();
    });
    it('should throw error invalid session', async () => {
      try {
        await waterHeaterController.confirm({ session: {} } as any, { redirect: jest.fn() }, payload as any);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('heaterReservationPage', () => {
    it('should return reservation page data', async () => {
      const req = { user: { email: '<EMAIL>' } };
      const configValue = { value: 5 };
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(configValue);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await waterHeaterController.heaterReservationPage(req);

      expect(result).toBeTruthy();
    });
    it('should return reservation page data 2', async () => {
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await waterHeaterController.heaterReservationPage({} as any);

      expect(result).toBeTruthy();
    });
  });
  describe('confirmPage', () => {
    it('should return reservation confirm page data', async () => {
      const req = { session: { waterHeaterPackReservationForm: {} }, user: { email: '<EMAIL>' } };
      const configValue = { value: 5 };
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(configValue);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await waterHeaterController.reservationConfirmPage(req);

      expect(result).toBeTruthy();
    });
    it('should return reservation confirm page data 2', async () => {
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await waterHeaterController.reservationConfirmPage({
        session: { waterHeaterPackReservationForm: {} },
      } as any);

      expect(result).toBeTruthy();
    });
    it('should throw NotFoundException', async () => {
      try {
        await waterHeaterController.reservationConfirmPage({ session: {} } as any);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });
  describe('landingWaterHeaterPage', () => {
    it('should return landing page data', async () => {
      mockedPackageService.getEnablePackages.mockResolvedValue([{ id: 'package1' }]);
      const result = await waterHeaterController.landingWaterHeaterPage();
      expect(result).toBeTruthy();
    });
  });

  describe('reservationThankPage', () => {
    it('should return page data', async () => {
      mockedPackageService.getEnablePackages.mockResolvedValue([{ id: 'package1' }]);
      const result = await waterHeaterController.reservationThankPage({
        session: {
          waterHeaterPackReservationCode: '123',
          waterHeaterPackReservationForm: { forms: '123' },
        },
      });
      expect(result).toBeTruthy();
    });
    it('should throw error 404', async () => {
      try {
        await waterHeaterController.reservationThankPage({
          session: {
            waterHeaterPackReservationCode: '',
            waterHeaterPackReservationForm: '',
          },
        });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error 404', async () => {
      try {
        await waterHeaterController.reservationThankPage({
          session: {
            waterHeaterPackReservationCode: '123',
            waterHeaterPackReservationForm: '',
          },
        });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });
});
