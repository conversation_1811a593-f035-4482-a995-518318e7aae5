import { Test, TestingModule } from '@nestjs/testing';
import { AirconInstallController } from '../../src/modules/user/controllers/aircon-install.controller';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { Py3Service } from '@vietnam/curama-py3-api';
import { AirconPackageService } from '@src/modules/shared/package/aircon-package.service';

describe('AirconInstallController', () => {
  let airconInstallController: AirconInstallController;
  const mockedReservationService = {
    createReservation: jest.fn(),
  };
  const mockedConfigurationService = {
    getConfiguration: jest.fn(),
  };
  const mockedCalendarBlockService = {
    getCalendarBlockDates: jest.fn(),
  };
  const mockedPackageService = {
    getPackageList: jest.fn(),
    getEnablePackages: jest.fn(),
  };
  const mockedAirconPackageService = {
    getCategorySpecifications: jest.fn(),
    getCategoryTraits: jest.fn(),
  };
  const mockedReservationOptionService = {
    getEnableReservationOptions: jest.fn(),
  };
  const mockedPY3Service = {
    getUserInfo: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AirconInstallController],
      providers: [
        {
          provide: ReservationService,
          useValue: mockedReservationService,
        },
        {
          provide: ConfigurationService,
          useValue: mockedConfigurationService,
        },
        {
          provide: CalendarBlockService,
          useValue: mockedCalendarBlockService,
        },
        {
          provide: PackageService,
          useValue: mockedPackageService,
        },
        {
          provide: AirconPackageService,
          useValue: mockedAirconPackageService,
        },
        {
          provide: ReservationOptionService,
          useValue: mockedReservationOptionService,
        },
        {
          provide: Py3Service,
          useValue: mockedPY3Service,
        },
      ],
    }).compile();

    airconInstallController = module.get<AirconInstallController>(AirconInstallController);
  });
  describe('register', () => {
    const packages = [{ id: 'package1', status: 1, outOfStock: false }];
    it('should add form reservation into session', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await airconInstallController.register(req, { forms: { packages: [{ id: 'package1' }] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await airconInstallController.register(req, { forms: { packages: [] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages 2', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await airconInstallController.register(req, { forms: {} } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not selected packages 3', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await airconInstallController.register(req, {} as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not package detail', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(packages);
      const result = await airconInstallController.register(req, { forms: { packages: [{ id: 'package2' }] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not package disable', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce([{ id: 'package1', status: 2, outOfStock: false }]);
      const result = await airconInstallController.register(req, { forms: { packages: [{ id: 'package1' }] } } as any);
      expect(result).toBeTruthy();
    });
    it('should throw error not package out of stock', async () => {
      const req = { session: {}, user: { email: '<EMAIL>', clientId: '123' } };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce([{ id: 'package1', status: 1, outOfStock: true }]);
      const result = await airconInstallController.register(req, { forms: { packages: [{ id: 'package1' }] } } as any);
      expect(result).toBeTruthy();
    });
  });
  describe('confirm', () => {
    const payload = {
      idempotencyKey: '12345',
      workingDate1: '2023-01-01',
      workingDate2: '2023-01-02',
      packages: '[{}]',
      email: '<EMAIL>',
      lastname: 'Doe',
      firstname: 'John',
      phoneNumber: '1234567890',
      postalcode: '12345',
      prefecture: 'Tokyo',
      city: 'Shinjuku',
      address: '123 Main St',
      existingAirConditionerRemoval: '1',
      existingAirConditionerRetrieval: '1',
      indoorPipeDecorativeCover: '1',
      outdoorPipeDecorativeCover: '1',
      outdoorUnitMountWithBrackets: '1',
      twoLevelOutdoorUnitWithBrackets: '1',
      productDeliveryType: '事前配送',
      extendedWarranty: '加入しない',
    };
    it('should confirm a new reservation', async () => {
      const req = {
        session: { airconInstallPackReservationForm: {} },
        user: { email: '<EMAIL>', clientId: '123' },
      };
      mockedReservationService.createReservation.mockResolvedValue({ data: { code: '123' } });
      const result = await airconInstallController.confirm(req, { redirect: jest.fn() }, payload as any);
      expect(result).toBeUndefined();
    });
    it('should confirm a new reservation 2', async () => {
      const req = { session: { airconInstallPackReservationForm: {} } };
      mockedReservationService.createReservation.mockResolvedValue({ data: { code: '123' } });
      const result = await airconInstallController.confirm(req, { redirect: jest.fn() }, payload as any);
      expect(result).toBeUndefined();
    });
    it('should throw error invalid session', async () => {
      try {
        await airconInstallController.confirm({ session: {} } as any, { redirect: jest.fn() }, payload as any);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('airconReservationPage', () => {
    it('should return reservation page data', async () => {
      const req = { user: { email: '<EMAIL>' } };
      const configValue = { value: 5 };
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(configValue);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await airconInstallController.airconReservationPage(req);

      expect(result).toBeTruthy();
    });
    it('should return reservation page data 2', async () => {
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await airconInstallController.airconReservationPage({} as any);

      expect(result).toBeTruthy();
    });
  });
  describe('confirmPage', () => {
    it('should return reservation confirm page data', async () => {
      const req = { session: { airconInstallPackReservationForm: {} }, user: { email: '<EMAIL>' } };
      const configValue = { value: 5 };
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(configValue);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await airconInstallController.reservationConfirmPage(req);

      expect(result).toBeTruthy();
    });
    it('should return reservation confirm page data 2', async () => {
      const blockedDates = ['2023-01-01', '2023-01-02'];
      const activePackages = [{ id: 'package1' }];
      const reservationOptions = [{ id: 'option1' }];

      mockedConfigurationService.getConfiguration.mockResolvedValue(null);
      mockedCalendarBlockService.getCalendarBlockDates.mockResolvedValue(blockedDates);
      mockedPackageService.getEnablePackages.mockResolvedValue(activePackages);
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValue(reservationOptions);

      const result = await airconInstallController.reservationConfirmPage({
        session: { airconInstallPackReservationForm: {} },
      } as any);

      expect(result).toBeTruthy();
    });
    it('should throw NotFoundException', async () => {
      try {
        await airconInstallController.reservationConfirmPage({ session: {} } as any);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });
  describe('landingAirconInstallPage', () => {
    it('should return landing page data', async () => {
      mockedPackageService.getEnablePackages.mockResolvedValue([{ id: 'package1' }]);
      mockedAirconPackageService.getCategorySpecifications.mockReturnValue({ categoryTraits: [], productsJsonLd: '' });
      const result = await airconInstallController.landingAirconInstallPage();
      expect(result).toBeTruthy();
    });
  });

  describe('reservationThankPage', () => {
    it('should return page data', async () => {
      mockedPackageService.getEnablePackages.mockResolvedValue([{ id: 'package1' }]);
      const result = await airconInstallController.reservationThankPage({
        session: {
          airconInstallPackReservationCode: '123',
          airconInstallPackReservationForm: { forms: '123' },
        },
      });
      expect(result).toBeTruthy();
    });
    it('should throw error 404', async () => {
      try {
        await airconInstallController.reservationThankPage({
          session: {
            airconInstallPackReservationCode: '',
            airconInstallPackReservationForm: '',
          },
        });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error 404', async () => {
      try {
        await airconInstallController.reservationThankPage({
          session: {
            airconInstallPackReservationCode: '123',
            airconInstallPackReservationForm: '',
          },
        });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('landingAirconPackageDetailPage', () => {
    process.env.APP_URL = 'http://localhost:3000';
    it('should throw error NotFoundException not found category traits', async () => {
      mockedAirconPackageService.getCategoryTraits.mockReturnValue([{ packages: [{ modelId: 'model1' }] }]);
      try {
        await airconInstallController.landingAirconPackageDetailPage({ modelId: 'series2' });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should throw error NotFoundException not found package purchase data', async () => {
      mockedAirconPackageService.getCategoryTraits.mockReturnValue([{ packages: [{ id: 'id1', modelId: 'model1' }] }]);
      mockedPackageService.getEnablePackages.mockResolvedValue([{ id: 'id2' }]);
      try {
        await airconInstallController.landingAirconPackageDetailPage({ modelId: 'model1' });
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
    it('should return package detail page', async () => {
      mockedAirconPackageService.getCategoryTraits.mockReturnValue([
        { seoDetailDescription: '', packages: [{ id: 'id1', modelId: 'model1', tatamiSize: 6 }] },
      ]);
      mockedPackageService.getEnablePackages.mockResolvedValue([{ id: 'id1', fee: 100 }]);
      const result = await airconInstallController.landingAirconPackageDetailPage({ modelId: 'model1' });
      expect(result).toBeTruthy();
    });
  });
});
