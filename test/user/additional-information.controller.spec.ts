import { Test, TestingModule } from '@nestjs/testing';
import { AdditionalInformationService } from '@src/modules/shared/additional-information/additional-information.service';
import { AdditionalInfoStatus } from '@src/common/enums/additional-information';
import { AdditionalInformationController } from '@src/modules/user/controllers/additional-information.controller';
import { Py3Service } from '@vietnam/curama-py3-api';

describe('AdditionalInformationController', () => {
  let controller: AdditionalInformationController;
  const mockedAdditionalInformationService = {
    getAdditionalInformationOrFail: jest.fn(),
    uploadAdditionalInformation: jest.fn(),
  };
  const mockedPY3Service = {
    getUserInfo: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdditionalInformationController],
      providers: [
        {
          provide: AdditionalInformationService,
          useValue: mockedAdditionalInformationService,
        },
        {
          provide: Py3Service,
          useValue: mockedPY3Service,
        },
      ],
    }).compile();

    controller = module.get<AdditionalInformationController>(AdditionalInformationController);
  });

  describe('uploadPage', () => {
    it('should return additional information', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.uploadPage({ user: { clientId: 1 } }, {}, { token: 'token' });
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException when reservation additional information not found', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 2,
        },
        isEnable: true,
        templateType: 1,
      });
      try {
        await controller.uploadPage({ user: { clientId: 1 } }, {}, { token: 'token' });
      } catch (error) {
        expect(error.message).toBe('Reservation additional information not found');
      }
    });

    it('should throw NotFoundException when reservation additional information not found 2', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 1,
        },
        isEnable: false,
        templateType: 1,
        deletedAt: new Date(),
      });
      try {
        await controller.uploadPage({ user: { clientId: 1 } }, {}, { token: 'token' });
      } catch (error) {
        expect(error.message).toBe('Reservation additional information not found');
      }
    });

    it('should redirect to thank page when additional information is not enable', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.UPLOADED,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.uploadPage(
        { user: { clientId: 1 } },
        { redirect: jest.fn() },
        { token: 'token' },
      );
      expect(result).toBeUndefined();
    });
  });

  describe('uploadAdditionalInformation', () => {
    it('should return additional information', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.uploadAdditionalInformation({ user: { clientId: 1 } }, {
        verificationToken: 'token',
      } as any);
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException when reservation additional information not found', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 2,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.uploadAdditionalInformation({ user: { clientId: 1 } }, {
        verificationToken: 'token',
      } as any);
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException when reservation additional information not found 2', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 1,
        },
        isEnable: false,
        templateType: 1,
      });
      const result = await controller.uploadAdditionalInformation({ user: { clientId: 1 } }, {
        verificationToken: 'token',
      } as any);
      expect(result).toBeTruthy();
    });

    it('should throw BadRequestException when additional information already uploaded', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.UPLOADED,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.uploadAdditionalInformation({ user: { clientId: 1 } }, {
        verificationToken: 'token',
      } as any);
      expect(result).toBeTruthy();
    });
  });

  describe('confirmAdditionalInformation', () => {
    it('should upload additional information', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const files = {
        identityFiles: [{ originalname: 'file1.png', mimetype: 'image/png' }],
        additionalFiles: [{ originalname: 'file2.png', mimetype: 'image/png' }],
      } as any;
      const body = { verificationToken: 'token' } as any;
      const result = await controller.confirmAdditionalInformation({ user: { clientId: 1 } }, files, body);
      expect(result).toBeUndefined();
    });

    it('should throw BadRequestException when reservation additional information already uploaded', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.UPLOADED,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const files = {
        identityFiles: [{ originalname: 'file1.png', mimetype: 'image/png' }],
        additionalFiles: [{ originalname: 'file2.png', mimetype: 'image/png' }],
      } as any;
      const body = { verificationToken: 'token' } as any;
      try {
        await controller.confirmAdditionalInformation({ user: { clientId: 1 } }, files, body);
      } catch (error) {
        expect(error.message).toBe('Reservation additional information already uploaded');
      }
    });

    it('should throw NotFoundException when reservation additional information not found', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 2,
        },
        isEnable: true,
        templateType: 1,
      });
      const files = {
        identityFiles: [{ originalname: 'file1.png', mimetype: 'image/png' }],
        additionalFiles: [{ originalname: 'file2.png', mimetype: 'image/png' }],
      } as any;
      const body = { verificationToken: 'token' } as any;
      try {
        await controller.confirmAdditionalInformation({ user: { clientId: 1 } }, files, body);
      } catch (error) {
        expect(error.message).toBe('Reservation additional information not found');
      }
    });
  });

  describe('thankPage', () => {
    it('should return thank page', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.UPLOADED,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.thankPage({ user: { clientId: 1 } }, {}, { token: 'token' });
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException when reservation additional information not found', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.UPLOADED,
        reservation: {
          userId: 1,
        },
        isEnable: false,
        templateType: 1,
      });
      try {
        await controller.thankPage({ user: { clientId: 1 } }, {}, { token: 'token' });
      } catch (error) {
        expect(error.message).toBe('Reservation additional information not found');
      }
    });
    it('should throw NotFoundException when reservation additional information is not enable', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.UPLOADED,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      try {
        await controller.thankPage({ user: { clientId: 1 } }, {}, { token: 'token' });
      } catch (error) {
        expect(error.message).toBe('Reservation additional information not found');
      }
    });
    it('should redirect to upload page when reservation additional information is not uploaded yet', async () => {
      mockedAdditionalInformationService.getAdditionalInformationOrFail.mockResolvedValue({
        id: 1,
        status: AdditionalInfoStatus.AWAITING_REUPLOAD,
        reservation: {
          userId: 1,
        },
        isEnable: true,
        templateType: 1,
      });
      const result = await controller.thankPage({ user: { clientId: 1 } }, { redirect: jest.fn() }, { token: 'token' });
      expect(result).toBeUndefined();
    });
  });
});
