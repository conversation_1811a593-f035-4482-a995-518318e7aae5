import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdditionalInformationService } from '@src/modules/shared/additional-information/additional-information.service';
import { AdditionalInformationEntity } from '@src/entities/additional-information.entity';
import { DataSource } from 'typeorm';
import { SpreadsheetService } from '@src/modules/shared/spreadsheet/spreadsheet.service';
import { DocumentService } from '@src/modules/shared/document/document.service';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';

describe('AdditionalInformationService', () => {
  let service: AdditionalInformationService;
  const mockedAdditionalInformationEntity = {
    findOne: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
  };
  const mockedDataSource = {
    transaction: jest.fn(),
  };
  const mockedDocumentService = {
    uploadFile: jest.fn(),
  };
  const mockedSpreadsheetService = {
    saveAdditionalInformationOnGoogleSheet: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdditionalInformationService,
        {
          provide: getRepositoryToken(AdditionalInformationEntity),
          useValue: mockedAdditionalInformationEntity,
        },
        {
          provide: DocumentService,
          useValue: mockedDocumentService,
        },
        {
          provide: SpreadsheetService,
          useValue: mockedSpreadsheetService,
        },
        {
          provide: DataSource,
          useValue: mockedDataSource,
        },
      ],
    }).compile();

    service = module.get<AdditionalInformationService>(AdditionalInformationService);
  });

  describe('getAdditionalInformationOrFail', () => {
    it('should return additional information if found', async () => {
      const mockAdditionalInformation = { id: '1', name: 'Test Info' };
      mockedAdditionalInformationEntity.findOne.mockResolvedValue(mockAdditionalInformation);

      const result = await service.getAdditionalInformationOrFail({ id: '1' });

      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if additional information is not found', async () => {
      mockedAdditionalInformationEntity.findOne.mockResolvedValue(null);

      try {
        await service.getAdditionalInformationOrFail({});
      } catch (error) {
        expect(error.message).toBeTruthy();
      }
    });
  });

  describe('uploadAdditionalInformation', () => {
    const mockEntityManager = {
      update: jest.fn().mockReturnValue({}),
      create: jest.fn().mockReturnValue({}),
      save: jest.fn().mockReturnValue({}),
    };

    it('should upload additional information', async () => {
      const userId = '1';
      const files = [{ path: 'path' }];
      const data = {
        verificationToken: 'token',
        bankName: 'bank',
        branchName: 'branch',
        accountType: 'type',
        accountNumber: 'number',
        accountHolderNameKana: 'name',
      };
      const mockAdditionalInformation = {
        id: '1',
        reservation: { userId: '1' },
        templateType: AdditionalInfoTemplate.HotWaterEnergySaving2025,
      };
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });
      service.getAdditionalInformationOrFail = jest.fn().mockResolvedValue(mockAdditionalInformation);
      mockedDocumentService.uploadFile.mockResolvedValue({});
      mockedSpreadsheetService.saveAdditionalInformationOnGoogleSheet.mockResolvedValue({});

      const result = await service.uploadAdditionalInformation(userId, files as any, data as any, 1);

      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if user not match', async () => {
      const userId = '2';
      const files = [{ path: 'path' }];
      const data = { verificationToken: 'token' };
      const mockAdditionalInformation = { id: '1', reservation: { userId: '1' } };
      mockedAdditionalInformationEntity.findOne.mockResolvedValue(null);
      service.getAdditionalInformationOrFail = jest.fn().mockResolvedValue(mockAdditionalInformation);
      mockedDocumentService.uploadFile.mockResolvedValue({});
      mockedSpreadsheetService.saveAdditionalInformationOnGoogleSheet.mockResolvedValue({});

      const result = await service.uploadAdditionalInformation(userId, files as any, data as any, 1);
      expect(result).toBeTruthy();
    });

    it('should throw NotFoundException if additional information is not found', async () => {
      const userId = '1';
      const files = [{ path: 'path' }];
      const data = { verificationToken: 'token' };
      const mockAdditionalInformation = { id: '1', reservation: { userId } };
      mockedAdditionalInformationEntity.findOne.mockResolvedValue(null);
      service.getAdditionalInformationOrFail = jest.fn().mockResolvedValue(mockAdditionalInformation);
      mockedDocumentService.uploadFile.mockResolvedValue({});
      mockedSpreadsheetService.saveAdditionalInformationOnGoogleSheet.mockResolvedValue({});

      const result = await service.uploadAdditionalInformation(userId, files as any, data as any, 1);
      expect(result).toBeTruthy();
    });
  });

  describe('refreshUploadUrl', () => {
    const mockEntityManager = {
      update: jest.fn().mockReturnValue({}),
      save: jest.fn().mockReturnValue({}),
    };

    it('should refresh additional information', async () => {
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await service.refreshUploadUrl('id');
      expect(result).toBeTruthy();
    });

    it('should refresh error message if delete fails', async () => {
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        throw new Error('Delete failed');
      });
      const result = await service.refreshUploadUrl('id');
      expect(result).toBeTruthy();
    });
  });

  describe('deleteAdditionalInformation', () => {
    const mockEntityManager = {
      update: jest.fn().mockReturnValue({}),
      save: jest.fn().mockReturnValue({}),
    };

    it('should delete additional information', async () => {
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await service.deleteAdditionalInformation('id');
      expect(result).toBeTruthy();
    });

    it('should return error message if delete fails', async () => {
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        throw new Error('Delete failed');
      });
      const result = await service.deleteAdditionalInformation('id');
      expect(result).toBeTruthy();
    });
  });
});
