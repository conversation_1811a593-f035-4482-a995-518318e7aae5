import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationService } from '../../src/modules/shared/reservation/reservation.service';
import { ReservationEntity } from '../../src/entities/reservation.entity';
import { ReservationOptionService } from '../../src/modules/shared/reservation-option/reservation-option.service';
import { PackageService } from '../../src/modules/shared/package/package.service';
import { SpreadsheetService } from '../../src/modules/shared/spreadsheet/spreadsheet.service';
import { DataSource } from 'typeorm';
import { BadRequestException } from '@nestjs/common';
import { NotificationService } from '@src/modules/shared/notification/notification.service';
import { ConfigurationService } from '../../src/modules/shared/configuration/configuration.service';
import { ObjectCacheService } from '../../src/modules/shared/object-cache/object-cache.service';

describe('ReservationService', () => {
  let service: ReservationService;
  const mockedReservationRepository = {
    createQueryBuilder: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };

  const mockedReservationOptionService = {
    getEnableReservationOptions: jest.fn(),
  };

  const mockedPackageService = {
    getPackageList: jest.fn(),
    getEnablePackages: jest.fn(),
  };

  const mockedSpreadsheetService = {
    saveReservationDataOnGoogleSheet: jest.fn(),
  };
  const mockedNotificationService = {
    sendEmailToUser: jest.fn(),
    buildEmailPropertiesForNewReservation: jest.fn(),
  };
  const mockedConfigurationService = {
    generateRandomCode: jest.fn(),
  };
  const mockedDataSource = {
    transaction: jest.fn(),
    query: jest.fn(),
  };
  const mockedObjectCacheService = {
    get: jest.fn(),
    addOrUpdate: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReservationService,
        { provide: getRepositoryToken(ReservationEntity), useValue: mockedReservationRepository },
        { provide: ObjectCacheService, useValue: mockedObjectCacheService },
        { provide: ReservationOptionService, useValue: mockedReservationOptionService },
        { provide: PackageService, useValue: mockedPackageService },
        { provide: SpreadsheetService, useValue: mockedSpreadsheetService },
        { provide: DataSource, useValue: mockedDataSource },
        { provide: NotificationService, useValue: mockedNotificationService },
        { provide: ConfigurationService, useValue: mockedConfigurationService },
      ],
    }).compile();

    service = module.get<ReservationService>(ReservationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getReservationPageCommonData', () => {
    it('should return common data for reservation page', async () => {
      const result = await service.getReservationPageCommonData();
      expect(result).toBeTruthy();
    });
  });

  describe('getAdditionalInformationTemplate', () => {
    it('should return additional information template', async () => {
      const mockTemplateId = 1;
      const result = service.getAdditionalInformationTemplate(mockTemplateId);
      expect(result).toBeTruthy();
    });
    it('should return additional information template 2', async () => {
      const mockTemplateId = 3;
      const result = service.getAdditionalInformationTemplate(mockTemplateId);
      expect(result).toBeTruthy();
    });
  });

  describe('getReservations', () => {
    it('should return paginated reservations', async () => {
      const mockPaginateDto = {
        perPage: 10,
        pageNumber: 1,
        offset: 0,
        status: 'Pending',
        packType: 1,
        reservationCode: 'CERS123456',
      };
      const mockReservations = [{ id: '1', code: 'CERS123456' }];
      const mockTotal = 1;
      mockedReservationRepository.getManyAndCount.mockResolvedValue([mockReservations, mockTotal]);

      const result = await service.getReservations(mockPaginateDto as any);

      expect(result).toEqual({
        reservations: mockReservations,
        pagination: {
          perPage: 10,
          pageNumber: 1,
          total: mockTotal,
        },
      });
    });
  });

  describe('getReservationDetail', () => {
    it('should return reservation detail', async () => {
      const mockReservation = { id: '1', code: 'CERS123456' };
      mockedReservationRepository.findOne.mockResolvedValue(mockReservation);

      const result = await service.getReservationDetail('1');

      expect(result).toEqual(mockReservation);
    });
  });

  describe('createReservation', () => {
    const mockRequest: any = {
      headers: {},
    };
    it('should create a reservation', async () => {
      const mockBody = { userId: '1', packages: [], packType: 3, productDeliveryType: '事前配送' };
      const mockReservationData = { id: '1', code: 'CERS123456' };
      const mockReservationPackageData = [];
      service.validateIdempotencyKey = jest.fn().mockResolvedValue(undefined);
      service.createReservationData = jest.fn().mockResolvedValue({
        reservationData: mockReservationData,
        reservationPackageData: mockReservationPackageData,
        reservationDetailForEmail: [],
      });
      mockedConfigurationService.generateRandomCode.mockResolvedValue('CERS123456');
      mockedSpreadsheetService.saveReservationDataOnGoogleSheet.mockResolvedValue(undefined);
      mockedNotificationService.sendEmailToUser.mockResolvedValue({});
      mockedDataSource.transaction.mockImplementation(async (cb) => cb({ insert: jest.fn() }));

      const result = await service.createReservation(mockBody as any, mockRequest);

      expect(result).toEqual({ code: 'CERS123456' });
    });

    it('should create a reservation 2', async () => {
      const mockBody = { userId: '1', packages: [], packType: 2 };
      const mockReservationData = { id: '1', code: 'CERS123456' };
      const mockReservationPackageData = [];
      service.createReservationData = jest.fn().mockResolvedValue({
        reservationData: mockReservationData,
        reservationPackageData: mockReservationPackageData,
        reservationDetailForEmail: [],
      });
      service.validateIdempotencyKey = jest.fn().mockResolvedValue(undefined);
      mockedConfigurationService.generateRandomCode.mockResolvedValue('CERS123456');
      mockedSpreadsheetService.saveReservationDataOnGoogleSheet.mockResolvedValue(undefined);
      mockedNotificationService.sendEmailToUser.mockResolvedValue({});
      mockedDataSource.transaction.mockImplementation(async (cb) => cb({ insert: jest.fn() }));

      const result = await service.createReservation(mockBody as any, mockRequest);

      expect(result).toEqual({ code: 'CERS123456' });
    });
  });

  describe('validateIdempotencyKey', () => {
    it('should throw error if IdempotencyKey existed', async () => {
      const cachedData = 'locked';
      mockedObjectCacheService.get.mockResolvedValue(cachedData);
      try {
        await service.validateIdempotencyKey(1, 'key123');
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it('should return reservation options', async () => {
      mockedObjectCacheService.get.mockResolvedValue(null);
      mockedObjectCacheService.addOrUpdate.mockResolvedValue(undefined);
      const result = await service.validateIdempotencyKey(1, 'key123');
      expect(result).toBeUndefined();
    });
  });

  describe('updateReservation', () => {
    it('should update a reservation', async () => {
      const mockUpdateDto = { status: 'Confirmed', invoiceUrl: 'http://example.com' };
      mockedReservationRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.updateReservation('1', mockUpdateDto as any);

      expect(result).toEqual({ affected: 1 });
    });

    it('should handle errors', async () => {
      const mockUpdateDto = { status: 'Confirmed', invoiceUrl: 'http://example.com' };
      mockedReservationRepository.update.mockImplementation(async () => {
        throw new Error('Test Error');
      });

      const result = await service.updateReservation('1', mockUpdateDto as any);

      expect(result).toEqual({ error: 'Test Error' });
    });
  });

  describe('createReservationData', () => {
    it('should create reservation data', async () => {
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        existingAirConditionerRetrieval: 1,
        hasDedicatedAirConditionerOutlet: 'あり',
        productDeliveryType: '事前配送（+0円）',
        packType: 1,
      };
      service.processPackages = jest.fn().mockResolvedValueOnce({});
      service.processOptions = jest.fn().mockResolvedValueOnce({});
      const result = await service.createReservationData(body as any);
      expect(result).toBeTruthy();
    });
    it('should create reservation data 2', async () => {
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        existingAirConditionerRetrieval: 1,
        hasDedicatedAirConditionerOutlet: 'あり',
        productDeliveryType: '事前配送（+0円）',
        packType: 2,
      };
      service.processPackages = jest.fn().mockResolvedValueOnce({});
      service.processOptions = jest.fn().mockResolvedValueOnce({});
      const result = await service.createReservationData(body as any);
      expect(result).toBeTruthy();
    });
  });

  describe('processPackages', () => {
    it('should create process packages', async () => {
      const mockPackages = [{ id: '1', name: 'Package 1', fee: 100 }];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        packType: 2,
      };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(mockPackages);
      mockedConfigurationService.generateRandomCode.mockResolvedValue('CERS123456');
      const result = await service.processPackages(body as any, 'CERS123456');
      expect(result).toBeTruthy();
    });

    it('should throw error if invalid package', async () => {
      const mockPackages = [{ id: '1', name: 'Package 1', fee: 100 }];
      const body = {
        packages: [{ id: '2', quantity: 1 }],
      };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(mockPackages);
      try {
        await service.processPackages(body as any, 'CERS123456');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toBeTruthy();
      }
    });

    it('should throw error if package disable', async () => {
      const mockPackages = [{ id: '1', status: 2, name: 'Package 1', fee: 100 }];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
      };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(mockPackages);
      try {
        await service.processPackages(body as any, 'CERS123456');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toBeTruthy();
      }
    });

    it('should throw error if package out of stock', async () => {
      const mockPackages = [{ id: '1', status: 1, outOfStock: true, name: 'Package 1', fee: 100 }];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
      };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(mockPackages);
      try {
        await service.processPackages(body as any, 'CERS123456');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toBeTruthy();
      }
    });

    it('should throw error if duplicate package', async () => {
      const mockPackages = [{ id: '2', name: 'Package 1', fee: 100 }];
      const body = {
        packages: [
          { id: '2', quantity: 1 },
          { id: '2', quantity: 1 },
        ],
      };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(mockPackages);
      try {
        await service.processPackages(body as any, 'CERS123456');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toBeTruthy();
      }
    });

    it('should throw error if invalid exceed quantity', async () => {
      const mockPackages = [{ id: '1', name: 'Package 1', fee: 100 }];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        existingAirConditionerRetrieval: 2,
      };
      mockedPackageService.getEnablePackages.mockResolvedValueOnce(mockPackages);
      try {
        await service.processPackages(body as any, 'CERS123456');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toBeTruthy();
      }
    });
  });

  describe('processOptions', () => {
    it('should process options 1', async () => {
      const mockReservationOptions = [
        {
          id: '1',
          name: 'existingAirConditionerRetrieval',
          type: 'quantity',
          hasPrice: true,
          detail: { maxQuantity: 1, pricePerUnit: 100 },
        },
        {
          id: '2',
          name: 'hasDedicatedAirConditionerOutlet',
          type: 'text',
          hasPrice: false,
          detail: { listOptions: [{ title: 'あり' }] },
        },
        {
          id: '3',
          name: 'productDeliveryType',
          type: 'text',
          hasPrice: true,
          detail: { listOptions: [{ title: '事前配送（+0円）', price: 100 }] },
        },
        {
          id: '4',
          name: 'extendedWarranty',
          type: 'text',
          hasPrice: true,
          detail: { listOptions: [{ title: '加入する', price: 100 }] },
        },
      ];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        existingAirConditionerRetrieval: 1,
        hasDedicatedAirConditionerOutlet: 'あり',
        productDeliveryType: '事前配送（+0円）',
        extendedWarranty: '加入する',
        packType: 1,
      };
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValueOnce(mockReservationOptions);
      const result = await service.processOptions(body as any, [], 0);
      expect(result).toBeTruthy();
    });

    it('should process options 1', async () => {
      const mockReservationOptions = [
        {
          id: '1',
          name: 'existingAirConditionerRetrieval',
          type: 'quantity',
          hasPrice: true,
          detail: { maxQuantity: 1, pricePerUnit: 100 },
        },
        {
          id: '2',
          name: 'hasDedicatedAirConditionerOutlet',
          type: 'text',
          hasPrice: false,
          detail: { listOptions: [{ title: 'あり' }] },
        },
        {
          id: '3',
          name: 'testOption',
          type: 'text',
          hasPrice: true,
          detail: { listOptions: [{ title: 'test', price: 100 }] },
        },
      ];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        existingAirConditionerRetrieval: 1,
        hasDedicatedAirConditionerOutlet: 'あり',
        testOption: 'test',
        packType: 1,
      };
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValueOnce(mockReservationOptions);
      const result = await service.processOptions(body as any, [], 0);
      expect(result).toBeTruthy();
    });

    it('should throw error if invalid negative quantity', async () => {
      const mockReservationOptions = [
        {
          id: '1',
          name: 'existingAirConditionerRetrieval',
          type: 'quantity',
          hasPrice: true,
          detail: { maxQuantity: 1, pricePerUnit: 100 },
        },
      ];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        existingAirConditionerRetrieval: -2,
      };
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValueOnce(mockReservationOptions);
      try {
        await service.processOptions(body as any, [], 0);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });

    it('should throw error if invalid option', async () => {
      const mockReservationOptions = [
        {
          id: '3',
          name: 'productDeliveryType',
          type: 'text',
          hasPrice: true,
          detail: { listOptions: [{ title: '事前配送（+0円）', price: 100 }] },
        },
      ];
      const body = {
        packages: [{ id: '1', quantity: 1 }],
        productDeliveryType: 'invalidOption',
      };
      mockedReservationOptionService.getEnableReservationOptions.mockResolvedValueOnce(mockReservationOptions);
      try {
        await service.processOptions(body as any, [], 0);
      } catch (error) {
        expect(error).toBeTruthy();
      }
    });
  });

  describe('createAddingDocumentUrl', () => {
    const mockEntityManager = {
      insert: jest.fn().mockReturnValue({}),
    };
    it('should create adding document url', async () => {
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await service.createAddingDocumentUrl('CERS123456', 1);
      expect(result).toBeTruthy();
    });

    it('should handle errors', async () => {
      mockedDataSource.transaction.mockImplementation(async () => {
        throw new Error('Test Error');
      });
      const result = await service.createAddingDocumentUrl('CERS123456', 1);
      expect(result).toEqual({ error: 'Test Error' });
    });
  });

  describe('reactiveAddingDocumentUrl', () => {
    const mockEntityManager = {
      insert: jest.fn().mockReturnValue({}),
      update: jest.fn().mockReturnValue({}),
    };
    it('should reactive adding document url', async () => {
      mockedDataSource.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });
      const result = await service.reactiveAddingDocumentUrl('id');
      expect(result).toBeTruthy();
    });

    it('should handle errors', async () => {
      mockedDataSource.transaction.mockImplementation(async () => {
        throw new Error('Test Error');
      });
      const result = await service.reactiveAddingDocumentUrl('id');
      expect(result).toEqual({ error: 'Test Error' });
    });
  });
});
