!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).dayjs_plugin_localizedFormat=t()}(this,function(){"use strict";var i={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(e,t,o){var t=t.prototype,n=t.format;o.en.formats=i,t.format=function(e){void 0===e&&(e="YYYY-MM-DDTHH:mm:ssZ");var r,t=this.$locale().formats,e=(r=void 0===t?{}:t,e.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(e,t,o){var n=o&&o.toUpperCase();return t||r[o]||i[o]||r[n].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(e,t,o){return t||o.slice(1)})}));return n.call(this,e)}}});