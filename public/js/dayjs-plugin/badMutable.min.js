!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs_plugin_badMutable=i()}(this,function(){"use strict";return function(t,i){i=i.prototype;i.$g=function(t,i,n){return this.$utils().u(t)?this[i]:this.$set(n,t)},i.set=function(t,i){return this.$set(t,i)};var n=i.startOf;i.startOf=function(t,i){return this.$d=n.bind(this)(t,i).toDate(),this.init(),this};var e=i.add;i.add=function(t,i){return this.$d=e.bind(this)(t,i).toDate(),this.init(),this};var s=i.locale;i.locale=function(t,i){return t?(this.$L=s.bind(this)(t,i).$L,this):this.$L};var o=i.daysInMonth;i.daysInMonth=function(){return o.bind(this.clone())()};var r=i.isSame;i.isSame=function(t,i){return r.bind(this.clone())(t,i)};var u=i.isBefore;i.isBefore=function(t,i){return u.bind(this.clone())(t,i)};var f=i.isAfter;i.isAfter=function(t,i){return f.bind(this.clone())(t,i)}}});
