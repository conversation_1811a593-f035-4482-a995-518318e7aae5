!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).dayjs_plugin_arraySupport=t()}(this,function(){"use strict";return function(e,t,n){var t=t.prototype,o=t.parse;t.parse=function(e){e.date=function(e){var t=e.date,e=e.utc;return Array.isArray(t)?e?t.length?new Date(Date.UTC.apply(null,t)):new Date:1===t.length?n(String(t[0])).toDate():new(Function.prototype.bind.apply(Date,[null].concat(t))):t}.bind(this)(e),o.bind(this)(e)}}});