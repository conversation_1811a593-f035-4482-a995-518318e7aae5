!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isoWeek=t()}(this,function(){"use strict";var n="day";return function(e,t,s){function a(e){return e.add(4-e.isoWeekday(),n)}t=t.prototype;t.isoWeekYear=function(){return a(this).year()},t.isoWeek=function(e){if(!this.$utils().u(e))return this.add(7*(e-this.isoWeek()),n);var t=a(this),i=(i=this.isoWeekYear(),i=4-(e=(this.$u?s.utc:s)().year(i).startOf("year")).isoWeekday(),4<e.isoWeekday()&&(i+=7),e.add(i,n));return t.diff(i,"week")+1},t.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var d=t.startOf;t.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return"isoweek"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):d.bind(this)(e,t)}}});