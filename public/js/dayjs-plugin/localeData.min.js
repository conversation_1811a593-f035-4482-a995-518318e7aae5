!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(n="undefined"!=typeof globalThis?globalThis:n||self).dayjs_plugin_localeData=e()}(this,function(){"use strict";return function(n,e,t){function r(n,e,t,r,o){var n=n.name?n:n.$locale(),e=i(n[e]),t=i(n[t]),u=e||t.map(function(n){return n.substr(0,r)});if(!o)return u;var a=n.weekStart;return u.map(function(n,e){return u[(e+(a||0))%7]})}function o(){return t.Ls[t.locale()]}function u(n,e){return n.formats[e]||n.formats[e.toUpperCase()].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(n,e,t){return e||t.slice(1)})}var e=e.prototype,i=function(n){return n&&(n.indexOf?n:n.s)};e.localeData=function(){return function(){var e=this;return{months:function(n){return n?n.format("MMMM"):r(e,"months")},monthsShort:function(n){return n?n.format("MMM"):r(e,"monthsShort","months",3)},firstDayOfWeek:function(){return e.$locale().weekStart||0},weekdays:function(n){return n?n.format("dddd"):r(e,"weekdays")},weekdaysMin:function(n){return n?n.format("dd"):r(e,"weekdaysMin","weekdays",2)},weekdaysShort:function(n){return n?n.format("ddd"):r(e,"weekdaysShort","weekdays",3)},longDateFormat:function(n){return u(e.$locale(),n)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}}.bind(this)()},t.localeData=function(){var e=o();return{firstDayOfWeek:function(){return e.weekStart||0},weekdays:function(){return t.weekdays()},weekdaysShort:function(){return t.weekdaysShort()},weekdaysMin:function(){return t.weekdaysMin()},months:function(){return t.months()},monthsShort:function(){return t.monthsShort()},longDateFormat:function(n){return u(e,n)},meridiem:e.meridiem,ordinal:e.ordinal}},t.months=function(){return r(o(),"months")},t.monthsShort=function(){return r(o(),"monthsShort","months",3)},t.weekdays=function(n){return r(o(),"weekdays",null,null,n)},t.weekdaysShort=function(n){return r(o(),"weekdaysShort","weekdays",3,n)},t.weekdaysMin=function(n){return r(o(),"weekdaysMin","weekdays",2,n)}}});