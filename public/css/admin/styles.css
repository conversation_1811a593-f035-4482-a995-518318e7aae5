[v-cloak] {
  display: none;
}

.hidden {
  display: none;
}

.loading-mask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  display: grid;
  place-items: center;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
}

.error-msg {
  position: relative;
  color: red;
  text-align: start;
  margin-top: -10px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.tranparent-block {
  opacity: 0.4;
}

a:hover {
  text-decoration: none;
  cursor: pointer;
}
.cur_p {
  cursor: pointer;
}
.flex-center {
  display: flex;
  justify-content: center;
}

.disable-click {
  pointer-events: none;
}

.panel-body {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.pr-0 {
  padding-right: 0px;
}

.pl-15 {
  padding-left: 15px;
}

.pr-15 {
  padding-right: 15px;
}

.m-0 {
  margin: 0;
}

.mt-10 {
  margin-top: 10px;
}
.mb-5 {
  margin-bottom: 5px;
}
.mb-20 {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.ml-20 {
  margin-left: 20px;
}
.mr-5 {
  margin-right: 5px;
}
.mr-8 {
  margin-right: 8px;
}
.mr-10 {
  margin-right: 10px;
}
.mr-20 {
  margin-right: 20px;
}

.ml-10 {
  margin-left: 10px;
}
.col-gre0 {
  color: #CCCCCC;
}
.col-red0 {
  color: #FF0000;
}
.col-blu0 {
  color: #42BFFB;
}
.gap-5 {
  gap: 5px;
}

.gap-md-5 {
  gap: 5px;
}

.min-w-65 {
  min-width: 65px;
}
.w-80 {
  width: 80px;
}
.w-100 {
  width: 100px;
}

.w-250 {
  width: 250px !important;
}

.w-auto {
  width: auto;
}

.f-s_25 {
  font-size: 25px;
}
.f-w_bold {
  font-weight: bold;
}

.f-w_400 {
  font-weight: 400;
}

form[name='promotionServiceFilterForm'] select,
form[name='promotionServiceFilterForm'] input,
form[name='spaceFilterForm'] select,
form[name='spaceFilterForm'] input {
  width: 100% !important;
  margin: 5px 0;
  border: 1px solid #bebebe;
  border-radius: 5px;
}

form[name='spacePromotionServiceForm'] select,
form[name='spacePromotionServiceForm'] input,
form[name='spaceDetailForm'] select,
form[name='spaceDetailForm'] input {
  margin: 5px 0;
  border: 1px solid #bebebe;
  border-radius: 5px;
}

.field-label {
  width: 20%;
}

.input-field,
.filter-daterange,
.filter-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-label {
  justify-content: flex-end;
  display: flex;
  align-items: center;
}

.input-control {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.filter-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
}

.input-label {
  text-align: right;
}

.badge-custom {
  display: inline-block;
  width: 100px;
  padding: 2px 8px;
  text-align: center;
  border-radius: 5px;
  border-style: solid;
  font-size: 12px;
}

.input-label {
  justify-content: flex-start;
  display: flex;
  align-items: center;
}

.input-control {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
.items-center {
  display: flex;
  align-items: center;
}
.router-control {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.option-detail-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.packageDetailForm {
  padding: 10px 50px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.modal-form {
  padding: 10px 20px;
}

.modal-form-button {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

.modal-form-button-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.modal-document-footer {
  position: absolute;
  bottom: 10px;
  right: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
.modal-form-button-center button,
.modal-form-button button {
  width: 100px;
}

.dayoff-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.align-start {
  align-items: start;
}
.block-detail {
  display: grid;
  grid-template-columns: 5fr 5fr;
  grid-template-rows: auto auto;
  grid-template-areas:
    'area1 area2'
    'area1 area3'
    'area1 area4';
  grid-gap: 20px;
}
.block-info,
.block-action,
.block-confirm,
.block-additional-info {
  border: 1px solid #bebebe;
  padding: 10px;
}
.block-info {
  grid-area: area1;
}
.block-confirm {
  grid-area: area2;
}
.block-action {
  grid-area: area3;
}
.block-additional-info {
  grid-area: area4;
}
.additional-info-content {
  padding: 10px;
  border: 1px solid #ddd;
  border-width: 0 1px 1px 1px;
  overflow: auto;
  max-height: 300px;
}
.block-confirm-button {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

@media screen and (max-width: 768px) {
  .input-control {
    flex-direction: column;
    align-items: start;
  }
}

.no-border {
  border: 0px !important;
}

.overflow-anywhere {
  overflow-wrap: anywhere;
}

.text-left {
  text-align: left;
}

.export-packages {
  max-height: 65vh;
  overflow: auto;
  margin-bottom: 20px;
}
.modal-content-custom {
  text-align: center;
  font-weight: 700;
  padding: 10px;
}
.addition-attempt {
  display: flex;
  align-items: center;
  gap: 10px;
}
.attempt-timestamp {
  font-weight: 700;
  min-width: 200px;
}
.document-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 5px;
}
.document-thumbnail-container {
  display: inline-block;
  margin: 3px;
}
.deleted-thumbnail {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 5px;
  margin: 3px;
  background-color: #bebebe;
  display: inline-block;
  color: red;
}
.show-more-document-container {
  display: inline-block;
  margin-top: 3px;
}
.show-more-document {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 5px;
  background-color: #cacaca;
  gap: 3px;
}
.show-more-document span {
  background-color: #b5b5b5;
  border-radius: 50%;
  display: inline-block;
  height: 8px;
  width: 8px;
}
.deleted-thumbnail-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.deleted-thumbnail-content p {
  transform: rotate(-45deg);
  font-weight: 800;
  font-size: 11px;
  margin: 0;
}
.document-image {
  margin: 10px;
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
}
.documents-container {
  margin-top: 10px;
  margin-bottom: 10px;
  gap: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: 70vh;
  overflow: auto;
}
.document-preview-container {
  object-fit: contain;
  max-width: 170px;
  padding: 10px;
  width: 70%;
}
.document-preview {
  width: 100%;
  height: auto;
  min-width: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.document-filename {
  width: 100%;
  word-wrap: break-word;
  text-align: center;
}
.selected-document {
  border: 2px solid #ddd;
  border-radius: 5px;
  width: 90%;
}
.deleted-tab {
  all: unset;
  background-color: #ddd !important;
  color: #555 !important;
  border: 1px solid #ddd;
  padding: 10px 15px;
  display: block;
}
.template-title {
  background-color: #e5e5e5;
  padding: 2px 4px;
  border-radius: 4px;
}
.deleted-tab.deleted-tab-highlight {
  background-color: #aaa !important;
}
.deleted-tab:hover {
  background-color: #9c9a9a !important;
}
.attempt-modal {
  height: 90vh;
}
.overflow-url {
  text-overflow: ellipsis;
  overflow: hidden;
  width: 30vw;
  white-space: nowrap;
}
.addition-time {
  white-space: nowrap;
}
@media screen and (max-width: 991px) {
  .documents-container {
    flex-direction: row;
    position: absolute;
    bottom: 60px;
    width: 90%;
  }
}
.additional-info-container {
  position: relative;
}
.overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, .5);
  backdrop-filter: blur(10px);
}
#the-canvas {
  border: 1px solid black;
  direction: ltr;
}
