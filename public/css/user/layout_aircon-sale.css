@charset "UTF-8";
/*　変数設定のみを行う
**************************/
[v-cloak] {
  display: none !important;
}
html {
  scroll-behavior: smooth;
}
.carousel {
  background: #eee;
}

.carousel-cell {
  background: #fff;
  border: 1px solid #767676;
  border-radius: 5px;
}

.main-carousel.is-expanded .carousel-cell {
  height: auto;
}

.table-scroll {
  overflow-x: auto;
}
.table-scroll table th,
.table-scroll table td {
  white-space: nowrap;
}

.bc-01 {
  background-color: #74cdff;
}

.bc-02 {
  background: url(/aircon-install-pack/image/user/lp-aircon-install/bg_top.jpg);
  background-size: cover;
}

.bc-03 {
  background-color: #479cdd;
}

.bc-04 {
  background-color: #f4faff;
}

.bac-p_r-c {
  background-position: right center;
}
.bac-p_l-c {
  background-position: left center;
}
.bac-p_l-b {
  background-position: left bottom;
}
.bac-p_r-b {
  background-position: left bottom;
}

.wid_27 {
  width: 27px;
}
.wid_28 {
  width: 28px;
}
.wid_130 {
  width: 130px;
}
.wid_540 {
  width: 540px;
}
.wid_4p5vw {
  width: 4.5vw;
}
.wid_30vw {
  width: 30vw;
}
.wid_110p {
  width: 110%;
}
.hei_30p {
  height: 30%;
}
.hei_27 {
  height: 27px;
}
.hei_28 {
  height: 28px;
}
.hei_110 {
  height: 110px;
}
.hei_130 {
  height: 130px;
}
.hei_140 {
  height: 140px;
}
.hei_4p5vw {
  height: 4.5vw;
}
.hei_30vw {
  height: 30vw;
}
.hei_110p {
  height: 110%;
}
.bor-b_1bla05 {
  border-bottom: 1px solid #ddd;
}

.bor-t_1bla05 {
  border-top: 1px solid #ddd;
}

.bor-r_1bla05 {
  border-right: 1px solid #ddd;
}
.bot_-5 {
  bottom: -5px;
}
.bot_-12 {
  bottom: -12px;
}
.bot_0 {
  bottom: 0px;
}
.bot_5vw {
  bottom: 5vw;
}
.bot_16 {
  bottom: 16px;
}
.top_-65 {
  top: -65px;
}
.rig_-12 {
  right: -12px;
}
.rig_-18 {
  right: -18px;
}
.rig_-65 {
  right: -65px;
}
.rig_5vw {
  right: 5vw;
}
.ico-lowprice {
  height: auto;
  left: calc(50% - 38px);
  position: absolute;
  top: -20px;
  width: 76px;
}
.mar-t_p5vw {
  margin-top: 0.5vw;
}
.mar-t_2 {
  margin-top: 2px;
}
.mar-b_-30 {
  margin-bottom: -30px;
}
.mar-b_3 {
  margin-bottom: 3px;
}
.mar-b_12 {
  margin-bottom: 12px;
}
.mar-b_100 {
  margin-bottom: 100px;
}
.mar-b_4vw {
  margin-bottom: 4vw;
}
.mar-l_2 {
  margin-left: 2px;
}
.mar-l_2p5vw {
  margin-left: 2.5vw;
}
.mar-r_12 {
  margin-right: 12px;
}

.pad_2vw {
  padding: 2vw;
}
.pad_5vw {
  padding: 5vw;
}
.pad_5p6vw {
  padding: 5.6vw;
}
.pad-t_30 {
  padding-top: 30px;
}
.pad-t_2p5vw {
  padding-top: 2.5vw;
}
.pad-t_5p6vw {
  padding-top: 5.6vw;
}
.pad-t_65 {
  padding-top: 65px;
}
.pad-t_2p5vw {
  padding-top: 2vw;
}
.pad-b_14 {
  padding-bottom: 14px;
}
.pad-b_20 {
  padding-bottom: 20px;
}
.pad-b_30 {
  padding-bottom: 30px;
}
.pad-b_2p5vw {
  padding-bottom: 2.5vw;
}
.pad-b_5p6vw {
  padding-bottom: 5.6vw;
}
.pad-l_26 {
  padding-left: 26px;
}
.pad-l_30 {
  padding-left: 30px;
}
.pad-l_5vw {
  padding-left: 5vw;
}
.pad-l_5p6vw {
  padding-left: 5.6vw;
}
.pad-r_26 {
  padding-right: 26px;
}
.pad-r_30 {
  padding-right: 30px;
}
.pad-r_5vw {
  padding-right: 5vw;
}
.pad-r_5p6vw {
  padding-right: 5.6vw;
}
.pad-b_2p5vw {
  padding-bottom: 2vw;
}
.pad_16 {
  padding: 16px;
}
.num-tag {
  background: #fa6218;
  border-radius: 30px;
  color: #fff;
  padding: 4px 36px;
}

.white_card {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
}

.bor-t_1white {
  border-top: 1px solid #fff;
}

.bor-r_1white {
  border-right: 1px solid #fff;
}

.bor-b_bla05 {
  border-bottom: 1px solid #dddddd;
}

.bor-r_bla05 {
  border-right: 1px solid #dddddd;
}
.bor-r_40 {
  border-radius: 40px;
}
.bor-r_8vw {
  border-radius: 8vw;
}
.wid_trisect {
  width: calc(100% / 2);
}
.max-w_320 {
  max-width: 320px;
}
.max-w_460 {
  max-width: 460px;
}
.max-w_80p {
  max-width: 80%;
}
.bac-c_blu12 {
  background-color: #2b91df;
}

.bac-c_gre01 {
  background-color: #449463;
}
.bac_gra-gre01 {
  background: linear-gradient(180deg, #E9EAE4 0%, rgba(239, 243, 242, 0) 100%);
}

.bor-r_1_dot_bla05 {
  border-right: 1px dotted #ddd;
}

.bor-r_1_das_bla05 {
  border-right: 1px dashed #ddd;
}

.bor-b_1_das_bla05 {
  border-bottom: 1px dashed #ddd;
}
.bor-b_1white {
  border-bottom: 1px solid #fff;
}
.bor_3_gre10 {
  border: 3px solid #449462;
}

.col_ora08 {
  color: #fb8146;
}
.col_blu08 {
  color: #2b91df;
}
.m_btn_ora10[disabled] {
  color: #fff;
}

.wid_1_3p {
  width: calc(100% / 3);
}
.fon-w_500 {
  font-weight: 500;
}
.fon-w_800 {
  font-weight: 800;
}
.fon-w_900 {
  font-weight: 900;
}
.fon-s_8 {
  font-size: 8px;
}
.fon-s_17 {
  font-size: 17px;
}
.fon-s_23 {
  font-size: 23px;
}
.fon-s_28 {
  font-size: 28px;
}
.fon-s_32 {
  font-size: 32px;
}
.fon-s_34 {
  font-size: 34px;
}
.fon-s_36 {
  font-size: 36px;
}
.fon-s_38 {
  font-size: 38px;
}
.fon-s_42 {
  font-size: 42px;
}
.fon-s_46 {
  font-size: 46px;
}
.fon-s_43 {
  font-size: 43px;
}
.fon-s_50 {
  font-size: 50px;
}
.fon-s_58 {
  font-size: 58px;
}
.fon-s_2p5vw {
  font-size: 2.5vw;
}
.fon-s_3vw {
  font-size: 3vw;
}
.fon-s_3p2vw {
  font-size: 3.2vw;
}
.fon-s_4vw {
  font-size: 4vw;
}
.fon-s_4p2vw {
  font-size: 4.2vw;
}
.fon-s_5vw {
  font-size: 5vw;
}
.fon-s_7p2vw {
  font-size: 7.2vw;
}
.fon-s_7vw {
  font-size: 7vw;
}
.fon-s_8vw {
  font-size: 8vw;
}
.fon-s_9vw {
  font-size: 9vw;
}
.fon-s_10vw {
  font-size: 10vw;
}
.min-h_130 {
  min-height: 130px;
}
.min-h_170 {
  min-height: 170px;
}
.min-h_320 {
  min-height: 320px;
}
.min-h_400 {
  min-height: 400px;
}
.min-h_420 {
  min-height: 420px;
}
.min-h_77vw {
  min-height: 77vw;
}
.lin-h_26 {
  line-height: 26px;
}
.lin-h_32 {
  line-height: 32px;
}
.lin-h_3vw {
  line-height: 3vw;
}
.lin-h_5vw {
  line-height: 5vw;
}
.let-s_0_15 {
  letter-spacing: 0.15rem;
}
.let-s_0_1 {
  letter-spacing: 0.1rem;
}
.m_box-shadow_2-2-p2 {
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}
.m_box-shadow_-2-2-p2 {
  box-shadow: 0 -2px 2px rgba(0, 0, 0, 0.2);
}
/* 対象エリア枠 */
.area-box {
  align-items: center;
  background-color: #1466a4;
  border-radius: 4px;
  color: #fff;
  display: flex;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 24px;
}

.area-box_ttl {
  border-radius: 4px 0 0 4px;
  padding: 12px;
  position: relative;
  width: 90px;
}

.area-box_ttl:after {
  border-bottom: 8px solid transparent;
  border-left: 8px solid #1466a4;
  border-top: 8px solid transparent;
  content: '';
  margin-top: -8px;
  position: absolute;
  right: -8px;
  top: 50%;
}

.area-box_txt {
  background-color: #1e7bc2;
  border-radius: 0 4px 4px 0;
  flex: 1;
  padding: 12px 12px 12px 18px;
}

/* スペックアコーディオン */
.acd-spec-label {
  border: 1px solid #ddd;
  border-radius: 25px;
  margin: 0 auto;
  padding: 10px 25px;
  text-align: center;
}

.acd-spec-label .acd-spec-txt {
  padding-right: 15px;
  position: relative;
}

.acd-spec-label .acd-spec-icon,
.acd-spec-label .acd-spec-icon::after {
  background: currentColor;
  border-radius: 10px;
  box-sizing: border-box;
  display: block;
}

.acd-spec-label .acd-spec-icon {
  background-color: #222;
  height: 1px;
  position: absolute;
  right: -5px;
  top: 50%;
  transform: translate(0, -50%) scale(0.9);
  width: 11px;
}

.acd-spec-label .acd-spec-icon::after {
  background-color: #222;
  content: '';
  height: 11px;
  left: 5px;
  position: absolute;
  top: -5px;
  width: 1px;
}

.acd-spec-check:checked + .acd-spec-label .acd-spec-icon {
  background: currentColor;
  background-color: #222;
  border-radius: 10px;
  box-sizing: border-box;
  display: block;
  height: 1px;
  position: absolute;
  transform: scale(0.9);
  width: 11px;
}
#spec-compare-table:checked ~ .compare-table-btn-wrap .acd-spec-label .acd-spec-icon {
  background: currentColor;
  background-color: #222;
  border-radius: 10px;
  box-sizing: border-box;
  display: block;
  height: 1px;
  position: absolute;
  transform: scale(0.9);
  width: 11px;
}

.compare-table-hidden {
  display: none;
  margin-top: 32px;
}

#spec-compare-table:checked ~ .scrollable .compare-table-container .compare-table-hidden {
  display: block;
}

#spec-compare-table:checked ~ .compare-table-btn-wrap .acd-spec-label .acd-spec-icon::after {
  display: none;
}

.acd-spec-check:checked + .acd-spec-label .acd-spec-icon::after {
  display: none;
}

.acd-spec-content {
  display: none;
}

.acd-spec-check:checked + .acd-spec-label + .acd-spec-content {
  display: block;
}
/* 口コミアコーディオン */
.voice-wrap {
  position: relative;
}

.voice-wrap *,
.voice-wrap *:before,
.voice-wrap *:after {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.voice-wrap label {
  background: rgba(255, 255, 255, 0.7);
  bottom: 0;
  cursor: pointer;
  height: 18px;
  margin: 0;
  position: absolute;
  text-align: center;
  width: 100%;
  z-index: 1;
}

.voice-wrap input:checked + label {
  background: inherit;
  /* 開いた時にグラデーションを消す */
}

.voice-wrap label:after {
  bottom: -15px;
  color: #479cdd;
  content: 'もっとみる';
  font-size: 12px;
  font-weight: 700;
  position: absolute;
  transform: translate(-50%, 0);
  width: 100%;
  z-index: 2;
}

.voice-wrap input {
  display: none;
}

.voice-wrap .voice-content {
  height: 62px;
  overflow: hidden;
  transition: all 0.5s;
  white-space: normal;
}

.voice-wrap input:checked + label:after {
  content: '閉じる';
}

.voice-wrap input:checked ~ .voice-content {
  height: auto;
  padding-bottom: 16px;
  transition: all 0.5s;
}

/* カルーセル内容 */
.contains {
  aspect-ratio: 5 / 4;
  border-radius: 8px 8px 0 0;
  list-style: none;
  overflow: hidden;
  padding: 0;
  position: relative;
  width: 100%;
}

/* スライド切り換え用ラジオボタンは常に非表示 */
.slide_select {
  display: none;
}

/* 各スライド */
.slide {
  aspect-ratio: 5 / 4;
  opacity: 0;
  position: absolute;
  width: 100%;
}

/* 前へ次へボタン */
.scroll_button {
  border-color: #b5b5b5;
  border-style: solid;
  border-width: 4px 4px 0 0;
  cursor: pointer;
  display: block;
  height: 15px;
  margin-top: -7.5px;
  position: absolute;
  top: 50%;
  width: 15px;
  z-index: 3;
}

/* ホバー時にボタンを強調 */
.scroll_button:hover {
  opacity: 1;
}

/* 前へボタン */
.scroll_prev {
  left: 15px;
  transform: rotate(-135deg);
}

/* 次へボタン */
.scroll_next {
  right: 15px;
  transform: rotate(45deg);
}

/* スライド移動ボタンエリア */
.move_controler {
  bottom: 8px;
  position: absolute;
  text-align: center;
  width: 100%;
}

/* スライド移動の各ボタン */
.button_move {
  border-radius: 100%;
  cursor: pointer;
  display: inline-block;
  height: 6px;
  margin: 0 2px;
  opacity: 0.5;
  width: 6px;
  z-index: 2;
}

/* ホバー時はやや明るくする */
.button_move:hover {
  opacity: 0.75;
}

/* スライド移動ボタンの色 */
.button_move {
  background-color: #b5b5b5;
}

/* 1番目のスライド選択時 */
.slide_select:nth-of-type(1):checked ~ .slide:nth-of-type(1) {
  opacity: 1;
}

.slide_select:nth-of-type(1):checked ~ .move_controler .button_move:nth-of-type(1) {
  opacity: 1;
}

/* 2番目のスライド選択時 */
.slide_select:nth-of-type(2):checked ~ .slide:nth-of-type(2) {
  opacity: 1;
}

.slide_select:nth-of-type(2):checked ~ .move_controler .button_move:nth-of-type(2) {
  opacity: 1;
}

/* 3番目のスライド選択時 */
.slide_select:nth-of-type(3):checked ~ .slide:nth-of-type(3) {
  opacity: 1;
}

.slide_select:nth-of-type(3):checked ~ .move_controler .button_move:nth-of-type(3) {
  opacity: 1;
}

/* 4番目のスライド選択時 */
.slide_select:nth-of-type(4):checked ~ .slide:nth-of-type(4) {
  opacity: 1;
}

.slide_select:nth-of-type(4):checked ~ .move_controler .button_move:nth-of-type(4) {
  opacity: 1;
}

/* 5番目のスライド選択時 */
.slide_select:nth-of-type(5):checked ~ .slide:nth-of-type(5) {
  opacity: 1;
}

.slide_select:nth-of-type(5):checked ~ .move_controler .button_move:nth-of-type(5) {
  opacity: 1;
}

/* 6番目のスライド選択時 */
.slide_select:nth-of-type(6):checked ~ .slide:nth-of-type(6) {
  opacity: 1;
}

.slide_select:nth-of-type(6):checked ~ .move_controler .button_move:nth-of-type(6) {
  opacity: 1;
}

.aircon-sale_stop {
  display: flex;
  justify-content: center;
  position: relative;
}
.aircon-sale_stop:before {
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  content: '';
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  z-index: 300;
}
.aircon-sale_stop .txt-end {
  background-image: none;
  color: #fff;
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.6;
  margin: 0 auto;
  position: absolute;
  text-align: center;
  top: 90px;
  vertical-align: middle;
  white-space: nowrap;
  z-index: 400;
}

.infor-wrap {
  position: relative;
}
.infor-wrap *,
.infor-wrap *:before,
.infor-wrap *:after {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.infor-wrap label {
  background: rgba(255, 255, 255, 0.7);
  bottom: -55px;
  cursor: pointer;
  margin: 0;
  position: absolute;
  text-align: center;
  z-index: 1;
  transform: translate(-50%, 0);
  left: 50%;
  width: max-content;
}

.infor-wrap label:after {
  bottom: -15px;
  position: absolute;
  transform: translate(-50%, 0);
  width: 100%;
  z-index: 2;
}

.infor-wrap .infor-content {
  overflow: hidden;
  white-space: normal;
  margin-bottom: 65px;
  -webkit-mask-image: -webkit-gradient(linear, left 65%, left bottom, from(black), to(rgba(0, 0, 0, 0)));
}
.aircon-infor-content {
  height: 150px;
}
.dishwasher-infor-content {
  height: 120px;
}
.heater-infor-content {
  height: 108px;
}

.aircon-infor-content-highlight {
  background: linear-gradient(transparent 50%, #f8df66 0%);
  font-weight: 700;
}
.aircon-infor-header-highlight {
  background: linear-gradient(transparent 75%, #f8df66 0%);
}

.infor-wrap input:checked ~ .infor-content {
  height: auto;
  padding-bottom: 16px;
  -webkit-mask-image: none;
}

.banner-strength {
  background-color: #fdf9d2;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.banner-strength-dishwasher {
  background-color: #f5f5f7;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.banner-strength-img {
  width: 100%;
  height: 50%;
  display: flex;
  justify-content: center;
}

#lp-header {
  background-repeat: no-repeat;
  background-size: cover;
}

.intro-bubble {
  padding: 4px 12px;
  width: fit-content;
  font-weight: 700;
  background-color: #fa6218;
  color: #fff;
  border-radius: 6px;
  position: relative;
  z-index: 1;
}

.intro-bubble::after {
  content: '';
  width: 14px;
  height: 14px;
  position: absolute;
  bottom: 0;
  left: 50%;
  background: inherit;
  transform: translate(-50%, 0) skew(-45deg, 45deg);
  border-radius: 2px;
  z-index: -1;
}

.intro-text {
  font-family: 'M PLUS 1', sans-serif;
  font-optical-sizing: auto;
  font-weight: 900;
  font-style: normal;
}

.intro-direction {
  width: 100%;
  line-height: 1;
  color: #222;
  display: flex;
  position: relative;
  align-self: center;
  align-items: center;
  justify-content: center;
  border: 3px solid #fff;
  border-radius: 8px;
  padding: 10px 0 14px;
  font-weight: 700;
  background-color: rgba(255, 255, 255, 0.8);
}
.intro-direction-aircon {
  width: 100%;
  line-height: 1;
  color: #222;
  display: flex;
  position: relative;
  align-self: center;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #222;
  font-weight: 700;
  background-color: rgba(255, 255, 255, 0.8);
}

.intro-direction-price {
  border-bottom: 2px solid #f91919;
}

.intro-text-dishwasher {
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  letter-spacing: 0.1rem;
  color: #ffffff;
}

.intro-description {
  color: #ffffff;
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.6);
}
.category-card {
  position: relative;
  overflow: hidden;
}
.badge-ribbon {
  position: absolute;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  text-align: center;
  color: #222;
  font-size: 12px;
  font-weight: bold;
  transform: rotate(45deg);
  z-index: 10;
}
.badge-ribbon.grey {
  background: #f5f5f7;
}
.badge-ribbon.yellow {
  background: #f8e143;
}
.badge-ribbon.pink {
  background: #fee7dc;
}
.fon-f_noto-serif {
  font-family: 'Noto Serif JP', serif;
}
.fon-f_noto-san {
  font-family: 'Noto Sans JP', serif;
}

.m_acd-hidden {
  display: none;
}
.m_acd-check:checked + .m_acd-title .m_acd-hidden {
  display: block;
}

.m_acd-block {
  display: block;
}
.m_acd-check:checked + .m_acd-title .m_acd-block {
  display: none;
}

.scrollable {
  width: 100%;
  padding-bottom: 10px;
}

.scrollable table {
  border-collapse: collapse;
}

.compare-table-container {
  min-width: 832px;
}

.compare-table {
  min-width: 800px;
  text-align: left;
  font-weight: normal;
}

.compare-table th {
  width: 100px;
  padding: 8px;
  font-weight: normal;
}

.compare-table td {
  width: 200px;
  padding: 8px;
}

.stickyNavigatorSp {
  z-index: 100;
  width: 100%;
  transition: .4s ease;
}
.stickyNavigatorPc {
  width: 100%;
  z-index: 100;
  transition: .4s ease;
}
@media screen and (min-width: 781px) {
  .stickyNavigatorPc {
    width: 540px;
  }
}
