[v-cloak] {
  display: none !important;
}
.bor_1_red10 {
  border: 1px solid #f91919 !important;
}
.btn-disabled {
  background: #ccc;
  box-shadow: none;
  border: none;
  color: #fff;
  pointer-events: none;
}
.truncated {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 24px !important;
}
.ver-a_t_t {
  vertical-align: text-top;
}
.h_56 {
  height: 56px;
}
.confirm-policy {
  text-align: center;
  display: block;
  @media screen and (max-width: 380px) {
    text-align: left;
  }
}
.m_acd-check:checked + .m_acd-wrapper + .m_acd-content {
  display: block;
}
.package-name {
  width: 65%;
  @media screen and (max-width: 350px) {
    width: 60%;
  }
}
.package-num {
  width: 23%;
  @media screen and (max-width: 350px) {
    width: 27%;
  }
}
.confirm-notes {
  list-style-type: "・";
}
.mar-t_32 {
  margin-top: 32px;
}
.bor-r_5 {
  border-radius: 5px;
}
button.close {
  -webkit-appearance: none;
  padding: 0;
  cursor: pointer;
  background: 0 0;
  border: 0;
}
.close {
  float: right;
  font-size: 21px;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: .2;
  margin-left: 5px;
}
.attachment-preview {
  width: 80px;
  height: 80px;
  border-radius: 6px;
}
.mar-r_48 {
  margin-right: 48px;
}
.max-w_460 {
  max-width: 460px;
}
