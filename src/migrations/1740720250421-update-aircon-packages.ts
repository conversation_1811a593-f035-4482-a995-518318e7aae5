import { MigrationInterface, QueryRunner } from 'typeorm';
import { PackageStatus } from '../common/enums/package';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { CODE_PREFIX } from '../common/enums/configuration';
import { PackType } from '../common/enums/pack-type';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const UPDATE_PACKAGES = [
  {
    code: `${CODE_PREFIX.PACKAGE}00005`,
    newName: 'パナソニック エオリア Fシリーズ / 6畳用（2024）',
    oldName: 'パナソニック エオリア Fシリーズ / 6畳用',
  },
];
const DISABLE_PACKAGES = [{ id: 'dc0c8dc0-7f40-4b17-bc7a-7c9eda01aad4', sort: 1 }];
const PACKAGES_DATA = [
  `('053d99dc-9aca-4b58-ab99-7120002d428d', '${CODE_PREFIX.PACKAGE}00030', 'パナソニック エオリア Fシリーズ / 6畳用', 68120, 1, 1, ${PackType.AIRCON_INSTALL})`,
];

export class UpdateAirconPackages1740720250421 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const updatePackageQuery = UPDATE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET "name" = '${item.newName}' WHERE code = '${item.code}'`,
    );
    const updateDisablePackageQuery = `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${
      PackageStatus.DISABLE
    }, sort = 10000 WHERE id IN (${DISABLE_PACKAGES.map((item) => `'${item.id}'`).join(', ')})`;
    const insertNewPackageQuery = `INSERT INTO ${schema}."${
      ENTITIES_ENUM.Packages
    }" ("id", code, "name", fee, sort, status, pack_type) VALUES
      ${PACKAGES_DATA.join(', ')};
    `;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 31);`;

    await Promise.all([
      queryRunner.query(updatePackageQuery.join(';')),
      queryRunner.query(updateDisablePackageQuery),
      queryRunner.query(insertNewPackageQuery),
      queryRunner.query(updateSequenceQuery),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const deletePackagesQuery = `DELETE FROM ${schema}."${ENTITIES_ENUM.Packages}" WHERE id IN (${PACKAGES_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const updateDisablePackageQuery = DISABLE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${PackageStatus.ENABLE}, sort = ${item.sort} WHERE id = '${item.id}'`,
    );
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 30);`;
    const updatePackageQuery = UPDATE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET "name" = '${item.oldName}' WHERE code = '${item.code}'`,
    );
    await Promise.all([
      queryRunner.query(deletePackagesQuery),
      queryRunner.query(updateDisablePackageQuery.join(';')),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(updatePackageQuery.join(';')),
    ]);
  }
}
