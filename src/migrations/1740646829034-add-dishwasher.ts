import { MigrationInterface, QueryRunner } from 'typeorm';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';
import { CODE_PREFIX, ConfigKeyEnum } from '../common/enums/configuration';
import { ReservationDeliveryType } from '../common/enums/reservation-options';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const PACKAGES_DATA = [
  `('ca365dd0-f35d-4476-b5de-9783569f2af9', '${CODE_PREFIX.PACKAGE}00027', 'ベーシックタイプ', 69800, 1, 1, ${PackType.DISHWASHER})`,
  `('49371527-1bc1-4999-8c59-53fb0f8323ec', '${CODE_PREFIX.PACKAGE}00028', 'UV除菌タイプ', 85800, 2, 1, ${PackType.DISHWASHER})`,
  `('154f1eb4-8f51-49da-b256-62cddf41f170', '${CODE_PREFIX.PACKAGE}00029', 'オートオープンタイプ', 85800, 3, 1, ${PackType.DISHWASHER})`,
];

const RESERVATION_OPTIONS_DATA = [
  `('84c592b5-5409-4c7a-9f72-b795e01f1970', 'productDeliveryType', '商品の配送種別', '${CODE_PREFIX.RESERVATION_OPTION}008', 'text', true, '{"listOptions": [{"price": 0, "title": "${ReservationDeliveryType.PRE_DELIVERY}"}, {"price": 1100, "title": "${ReservationDeliveryType.SAME_DAY}"}]}', true, 1, ${PackType.DISHWASHER})`,
];

export class AddDishwasher1740646829034 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const dropUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.ReservationOptions}" DROP CONSTRAINT IF EXISTS "UQ_ReservationOptions_name"`,
    ];
    await Promise.all([queryRunner.query(dropUniqueConstraintQuery.join(';'))]);

    const addUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.ReservationOptions}" ADD CONSTRAINT "UQ_ReservationOptions_name" UNIQUE ("name", "pack_type")`,
    ];

    const insertNewPackageQuery = `INSERT INTO ${schema}."Packages" ("id", code, "name", fee, sort, status, pack_type) VALUES
      ${PACKAGES_DATA.join(', ')};
    `;

    const insertReservationOptionsQuery = `INSERT INTO ${schema}."ReservationOptions" ("id", "name", title, code, "type", has_price, detail, enabled, sort, "pack_type") VALUES
      ${RESERVATION_OPTIONS_DATA.join(', ')};
    `;

    const insertConfigurationsQuery = `INSERT INTO ${schema}."Configurations" ("id", config_key, value, pack_type) VALUES ('4755e2da-599f-44fb-a5d4-de144d483189', '${ConfigKeyEnum.CALENDAR_BLOCK_RANGE}', '14', ${PackType.DISHWASHER});`;

    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 30);`;

    await Promise.all([
      queryRunner.query(addUniqueConstraintQuery.join(';')),
      queryRunner.query(insertNewPackageQuery),
      queryRunner.query(insertReservationOptionsQuery),
      queryRunner.query(insertConfigurationsQuery),
      queryRunner.query(updateSequenceQuery),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const deletePackagesQuery = `DELETE FROM ${schema}."Packages" WHERE id IN (${PACKAGES_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const deleteReservationOptionsQuery = `DELETE FROM ${schema}."ReservationOptions" WHERE id IN (${RESERVATION_OPTIONS_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const dropUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.ReservationOptions}" DROP CONSTRAINT IF EXISTS "UQ_ReservationOptions_name"`,
    ];
    const deleteConfigurationsQuery = `DELETE FROM ${schema}."Configurations" WHERE id = '4755e2da-599f-44fb-a5d4-de144d483189';`;

    await Promise.all([
      queryRunner.query(dropUniqueConstraintQuery.join(';')),
      queryRunner.query(deletePackagesQuery),
      queryRunner.query(deleteReservationOptionsQuery),
      queryRunner.query(deleteConfigurationsQuery),
    ]);
    const addUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.ReservationOptions}" ADD CONSTRAINT "UQ_ReservationOptions_name" UNIQUE ("name")`,
    ];
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 27);`;
    await Promise.all([queryRunner.query(addUniqueConstraintQuery.join(';')), queryRunner.query(updateSequenceQuery)]);
  }
}
