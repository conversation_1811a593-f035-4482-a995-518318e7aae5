import { MigrationInterface, QueryRunner } from 'typeorm';
import { CODE_PREFIX } from '../common/enums/configuration';
import { PackageStatus } from '../common/enums/package';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const EXISTS_PACKAGES = [
  { id: 'c49a7c29-b2f9-4116-8193-431d45a698f1', name: '最新モデル格安パック / 6畳用', fee: 62480, sort: 1 },
  { id: 'c4e467d3-c7ad-4490-8479-cf79271f4cbd', name: '最新モデル格安パック / 8畳用', fee: 77000, sort: 2 },
  { id: '1f8693a9-89fe-4d91-9f9e-20a2f5094959', name: '三菱電機 霧ヶ峰 GVシリーズ / 6畳用', fee: 67480, sort: 3 },
  { id: '71250df3-48ea-4410-99ba-e5004058392b', name: '三菱電機 霧ヶ峰 GVシリーズ / 8畳用', fee: 82000, sort: 4 },
  { id: 'dc0c8dc0-7f40-4b17-bc7a-7c9eda01aad4', name: 'パナソニック エオリア Fシリーズ / 6畳用', fee: 67480, sort: 5 },
  { id: 'c31912f3-ef3f-4c68-8795-e33350163fed', name: 'パナソニック エオリア Fシリーズ / 8畳用', fee: 82000, sort: 6 },
  { id: '04342604-6c16-42ce-9e42-dc08e99ae546', name: '日立 白くまくん AJシリーズ / 6畳用', fee: 67480, sort: 7 },
  { id: '9e88c751-2945-46dd-a3be-dac894764f76', name: '日立 白くまくん AJシリーズ / 8畳用', fee: 82000, sort: 8 },
  { id: '52b6ee22-262d-49b0-96f4-7e7dd8e687da', name: '三菱電機 霧ヶ峰 BXVシリーズ / 6畳用', fee: 114400, sort: 9 },
  { id: '788b5881-975c-4e58-9522-49d26c22f521', name: '三菱電機 霧ヶ峰 BXVシリーズ / 8畳用', fee: 123400, sort: 10 },
  { id: 'c2bf3881-9976-4bb1-b271-8dc9438179e2', name: '日立 白くまくん V/VLシリーズ / 6畳用', fee: 114000, sort: 11 },
  { id: '746be1ea-05f9-4f4a-bfaa-9897b8fbedc5', name: '日立 白くまくん V/VLシリーズ / 8畳用', fee: 123500, sort: 12 },
  {
    id: '48d0949d-b51e-4541-93a5-71607e7399df',
    name: 'パナソニック エオリア EXシリーズ / 6畳用',
    fee: 129600,
    sort: 13,
  },
  {
    id: '7d7c49e6-cc8b-4a4e-9b9c-b702aafc4199',
    name: 'パナソニック エオリア EXシリーズ / 8畳用',
    fee: 143300,
    sort: 14,
  },
  { id: 'd61ac552-944e-4b96-9d8c-a4c1d6bdd391', name: 'ダイキン CXシリーズ / 6畳用', fee: 113100, sort: 15 },
  { id: '58e06f1c-6bd3-4fe3-9543-d8ec746b474a', name: 'ダイキン CXシリーズ / 8畳用', fee: 120120, sort: 16 },
];

const UPDATE_PACKAGES = [
  { id: 'dc0c8dc0-7f40-4b17-bc7a-7c9eda01aad4', fee: 68120, sort: 1 },
  { id: '71250df3-48ea-4410-99ba-e5004058392b', fee: 76610, sort: 2 },
];

const NEW_PACKAGES = [
  `('9412a507-ec12-4ab4-bce6-b3608a25e6cd', '${CODE_PREFIX.PACKAGE}00017', '三菱電機 霧ヶ峰 GVシリーズ / 10畳用', 87210, 3, ${PackageStatus.ENABLE})`,
  `('1b4b803c-ce78-466d-b996-a615e551f769', '${CODE_PREFIX.PACKAGE}00018', '三菱電機 霧ヶ峰 GVシリーズ / 12畳用', 109610, 4, ${PackageStatus.ENABLE})`,
  `('899f85d9-e894-4f9a-a99c-a9351d591d78', '${CODE_PREFIX.PACKAGE}00019', '三菱電機 霧ヶ峰 GVシリーズ / 14畳用', 118350, 5, ${PackageStatus.ENABLE})`,
  `('6559da8c-14df-4f9e-bc0b-5b0b526e77a0', '${CODE_PREFIX.PACKAGE}00020', 'パナソニック エオリア Jシリーズ / 6畳用', 95960, 6, ${PackageStatus.ENABLE})`,
  `('e0070aa0-1a5b-403e-b524-be79f5c72188', '${CODE_PREFIX.PACKAGE}00021', 'パナソニック エオリア Jシリーズ / 8畳用', 106030, 7, ${PackageStatus.ENABLE})`,
  `('fa99a7d6-0ef8-4b25-bbb9-b038974bac29', '${CODE_PREFIX.PACKAGE}00022', 'パナソニック エオリア Jシリーズ / 10畳用', 113450, 8, ${PackageStatus.ENABLE})`,
  `('bec8edbb-6faf-4c80-a1d6-78f8aa90ff48', '${CODE_PREFIX.PACKAGE}00023', 'ダイキン AXシリーズ / 12畳用', 234300, 9, ${PackageStatus.ENABLE})`,
  `('521e330b-6571-4631-b461-a862549c84e6', '${CODE_PREFIX.PACKAGE}00024', 'ダイキン AXシリーズ / 14畳用', 246400, 10, ${PackageStatus.ENABLE})`,
];

const EXISTS_RESERVATION_OPTIONS = [
  { id: '777f3714-a5b1-4f44-94be-6affd566843a', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 6050}' },
  { id: '8aeba4c8-b1ab-431d-89b0-c8a87120fe01', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 4400}' },
  { id: '64d4533e-8071-41e4-8a3a-b66f927edf86', detail: '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 7040}' },
  {
    id: 'fa31514b-21c8-42b9-a51e-4eab64aa56a0',
    detail: '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 12100}',
  },
  { id: 'f68aa6a3-3840-45db-a530-6811c77ada4c', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 15070}' },
  { id: 'bd1f1bf8-c39f-499d-8bef-6dc1643a7743', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 19800}' },
  {
    id: '1c3464a9-2c65-4dcd-80be-c34e2f47209e',
    detail: '{"listOptions": [{"price": 0, "title": "事前配送"}, {"price": 3300, "title": "当日持込"}]}',
  },
];

const UPDATE_RESERVATION_OPTIONS = [
  { id: '777f3714-a5b1-4f44-94be-6affd566843a', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 6600}' },
  { id: '8aeba4c8-b1ab-431d-89b0-c8a87120fe01', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 4950}' },
  { id: '64d4533e-8071-41e4-8a3a-b66f927edf86', detail: '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 8800}' },
  {
    id: 'fa31514b-21c8-42b9-a51e-4eab64aa56a0',
    detail: '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 14850}',
  },
  { id: 'f68aa6a3-3840-45db-a530-6811c77ada4c', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 17600}' },
  { id: 'bd1f1bf8-c39f-499d-8bef-6dc1643a7743', detail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 25300}' },
  {
    id: '1c3464a9-2c65-4dcd-80be-c34e2f47209e',
    detail: '{"listOptions": [{"price": 0, "title": "事前配送"}, {"price": 3300, "title": "当日持込"}]}',
  },
];

export class UpdatePackages1731047889428 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const updatePackageIds = UPDATE_PACKAGES.map((item) => item.id);
    const updateDisablePackageQuery = `UPDATE ${schema}."Packages" SET status = ${
      PackageStatus.DISABLE
    }, sort = 10000 WHERE id IN (${EXISTS_PACKAGES.filter((item) => !updatePackageIds.includes(item.id))
      .map((item) => `'${item.id}'`)
      .join(', ')})`;
    const updatePackageQuery = [];
    for (const item of UPDATE_PACKAGES) {
      updatePackageQuery.push(
        `UPDATE ${schema}."Packages" SET fee = ${item.fee}, sort = ${item.sort} WHERE id = '${item.id}'`,
      );
    }
    const insertNewPackageQuery = `INSERT INTO ${schema}."Packages" ("id", code, "name", fee, sort, status) VALUES 
      ${NEW_PACKAGES.join(', ')};
    `;
    const updatePackageTableQuery = `ALTER TABLE ${schema}."Packages" ADD COLUMN IF NOT EXISTS "out_of_stock" boolean DEFAULT false;`;

    const updateReservationOptionsQuery = [];
    for (const item of UPDATE_RESERVATION_OPTIONS) {
      updateReservationOptionsQuery.push(
        `UPDATE ${schema}."ReservationOptions" SET detail = '${item.detail}' WHERE id = '${item.id}'`,
      );
    }

    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 25);`;
    await Promise.all([
      queryRunner.query(updatePackageQuery.join(';')),
      queryRunner.query(updateDisablePackageQuery),
      queryRunner.query(insertNewPackageQuery),
      queryRunner.query(updatePackageTableQuery),
      queryRunner.query(updateReservationOptionsQuery.join(';')),
      queryRunner.query(updateSequenceQuery),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const deleteNewPackageQuery = `DELETE FROM ${schema}."Packages" WHERE id IN (${NEW_PACKAGES.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const updateExistsPackageQuery = [];
    for (const item of EXISTS_PACKAGES) {
      updateExistsPackageQuery.push(
        `UPDATE ${schema}."Packages" SET status = ${PackageStatus.ENABLE}, name = '${item.name}', sort = ${item.sort}, fee = ${item.fee} WHERE id = '${item.id}'`,
      );
    }
    const updatePackageTableQuery = `ALTER TABLE ${schema}."Packages" DROP COLUMN IF EXISTS "out_of_stock";`;

    const updateReservationOptionsQuery = [];
    for (const item of EXISTS_RESERVATION_OPTIONS) {
      updateReservationOptionsQuery.push(
        `UPDATE ${schema}."ReservationOptions" SET detail = '${item.detail}' WHERE id = '${item.id}'`,
      );
    }

    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 17);`;
    await Promise.all([
      queryRunner.query(deleteNewPackageQuery),
      queryRunner.query(updateExistsPackageQuery.join(';')),
      queryRunner.query(updatePackageTableQuery),
      queryRunner.query(updateReservationOptionsQuery.join(';')),
      queryRunner.query(updateSequenceQuery),
    ]);
  }
}
