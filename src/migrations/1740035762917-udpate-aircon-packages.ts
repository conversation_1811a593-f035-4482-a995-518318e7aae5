import { MigrationInterface, QueryRunner } from 'typeorm';
import { PackageStatus } from '../common/enums/package';
import { ENTITIES_ENUM } from '../common/enums/entities';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const UPDATE_PACKAGES = [
  { id: '6559da8c-14df-4f9e-bc0b-5b0b526e77a0', sort: 6 },
  { id: 'e0070aa0-1a5b-403e-b524-be79f5c72188', sort: 7 },
  { id: 'fa99a7d6-0ef8-4b25-bbb9-b038974bac29', sort: 8 },
];

export class UdpateAirconPackages1740035762917 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const updateDisablePackageQuery = `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${
      PackageStatus.DISABLE
    }, sort = 10000 WHERE id IN (${UPDATE_PACKAGES.map((item) => `'${item.id}'`).join(', ')})`;
    await queryRunner.query(updateDisablePackageQuery);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const updateDisablePackageQuery = UPDATE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${PackageStatus.ENABLE}, sort = ${item.sort} WHERE id = '${item.id}'`,
    );
    await queryRunner.query(updateDisablePackageQuery.join(';'));
  }
}
