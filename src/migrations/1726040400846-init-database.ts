import { MigrationInterface, QueryRunner } from 'typeorm';
import { CODE_PREFIX, ConfigKeyEnum } from '../common/enums/configuration';

const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;
const dbName = process.env.DATABASE_NAME ? `"${process.env.DATABASE_NAME}"` : `"labo-install-pack"`;
const tz = process.env.TZ ? `"${process.env.TZ}"` : `"Asia/Tokyo"`;

const PACKAGES_DATA = [
  `('c49a7c29-b2f9-4116-8193-431d45a698f1', '${CODE_PREFIX.PACKAGE}00001', '最新モデル格安パック / 6畳用', 62480, 1, 1)`,
  `('c4e467d3-c7ad-4490-8479-cf79271f4cbd', '${CODE_PREFIX.PACKAGE}00002', '最新モデル格安パック / 8畳用', 77000, 2, 1)`,
  `('1f8693a9-89fe-4d91-9f9e-20a2f5094959', '${CODE_PREFIX.PACKAGE}00003', '三菱電機 霧ヶ峰 GVシリーズ / 6畳用', 67480, 3, 1)`,
  `('71250df3-48ea-4410-99ba-e5004058392b', '${CODE_PREFIX.PACKAGE}00004', '三菱電機 霧ヶ峰 GVシリーズ / 8畳用', 82000, 4, 1)`,
  `('dc0c8dc0-7f40-4b17-bc7a-7c9eda01aad4', '${CODE_PREFIX.PACKAGE}00005', 'パナソニック エオリア Fシリーズ / 6畳用', 67480, 5, 1)`,
  `('c31912f3-ef3f-4c68-8795-e33350163fed', '${CODE_PREFIX.PACKAGE}00006', 'パナソニック エオリア Fシリーズ / 8畳用', 82000, 6, 1)`,
  `('04342604-6c16-42ce-9e42-dc08e99ae546', '${CODE_PREFIX.PACKAGE}00007', '日立 白くまくん AJシリーズ / 6畳用', 67480, 7, 1)`,
  `('9e88c751-2945-46dd-a3be-dac894764f76', '${CODE_PREFIX.PACKAGE}00008', '日立 白くまくん AJシリーズ / 8畳用', 82000, 8, 1)`,
  `('52b6ee22-262d-49b0-96f4-7e7dd8e687da', '${CODE_PREFIX.PACKAGE}00009', '三菱電機 霧ヶ峰 BXVシリーズ / 6畳用', 114400, 9, 1)`,
  `('788b5881-975c-4e58-9522-49d26c22f521', '${CODE_PREFIX.PACKAGE}00010', '三菱電機 霧ヶ峰 BXVシリーズ / 8畳用', 123400, 10, 1)`,
  `('c2bf3881-9976-4bb1-b271-8dc9438179e2', '${CODE_PREFIX.PACKAGE}00011', '日立 白くまくん V/VLシリーズ / 6畳用', 114000, 11, 1)`,
  `('746be1ea-05f9-4f4a-bfaa-9897b8fbedc5', '${CODE_PREFIX.PACKAGE}00012', '日立 白くまくん V/VLシリーズ / 8畳用', 123500, 12, 1)`,
  `('48d0949d-b51e-4541-93a5-71607e7399df', '${CODE_PREFIX.PACKAGE}00013', 'パナソニック エオリア EXシリーズ / 6畳用', 129600, 13, 1)`,
  `('7d7c49e6-cc8b-4a4e-9b9c-b702aafc4199', '${CODE_PREFIX.PACKAGE}00014', 'パナソニック エオリア EXシリーズ / 8畳用', 143300, 14, 1)`,
  `('d61ac552-944e-4b96-9d8c-a4c1d6bdd391', '${CODE_PREFIX.PACKAGE}00015', 'ダイキン CXシリーズ / 6畳用', 113100, 15, 1)`,
  `('58e06f1c-6bd3-4fe3-9543-d8ec746b474a', '${CODE_PREFIX.PACKAGE}00016', 'ダイキン CXシリーズ / 8畳用', 120120, 16, 1)`,
];

const RESERVATION_OPTIONS_DATA = [
  `('777f3714-a5b1-4f44-94be-6affd566843a', 'existingAirConditionerRemoval', '既設エアコン取り外し', '${CODE_PREFIX.RESERVATION_OPTION}001', 'quantity', true, '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 6050}', true, 1)`,
  `('8aeba4c8-b1ab-431d-89b0-c8a87120fe01', 'existingAirConditionerRetrieval', '既設エアコン回収', '${CODE_PREFIX.RESERVATION_OPTION}002', 'quantity', true, '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 4400}', true, 2)`,
  `('64d4533e-8071-41e4-8a3a-b66f927edf86', 'outdoorPipeDecorativeCover', '室外配管化粧カバー', '${CODE_PREFIX.RESERVATION_OPTION}003', 'quantity', true, '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 7040}', true, 3)`,
  `('fa31514b-21c8-42b9-a51e-4eab64aa56a0', 'indoorPipeDecorativeCover', '室内配管化粧カバー', '${CODE_PREFIX.RESERVATION_OPTION}004', 'quantity', true, '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 12100}', true, 4)`,
  `('f68aa6a3-3840-45db-a530-6811c77ada4c', 'outdoorUnitMountWithBrackets', '室外機の屋根・ 壁面・天吊り置き｜金具込み', '${CODE_PREFIX.RESERVATION_OPTION}005', 'quantity', true, '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 15070}', true, 5)`,
  `('bd1f1bf8-c39f-499d-8bef-6dc1643a7743', 'twoLevelOutdoorUnitWithBrackets', '室外機の二段置き｜金具込み', '${CODE_PREFIX.RESERVATION_OPTION}006', 'quantity', true, '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 19800}', true, 6)`,
  `('1c3464a9-2c65-4dcd-80be-c34e2f47209e', 'productDeliveryType', '商品の配送種別', '${CODE_PREFIX.RESERVATION_OPTION}007', 'text', true, '{"listOptions": [{"price": 0, "title": "事前配送"}, {"price": 3300, "title": "当日持込"}]}', true, 7)`,
];

export class InitDatabase1726040400846 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER DATABASE ${dbName} SET timezone TO ${tz};

      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      CREATE TABLE IF NOT EXISTS ${schema}."CalendarBlocks" (
        block_date date NOT NULL,
        CONSTRAINT "PK_CalendarBlocks" PRIMARY KEY (block_date)
      );
      CREATE INDEX IF NOT EXISTS "IDX_CalendarBlocks_BlockDate" ON ${schema}."CalendarBlocks" USING btree (block_date);

      CREATE TABLE IF NOT EXISTS ${schema}."Configurations" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        config_key varchar(100) NOT NULL,
        value varchar NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_Configurations" PRIMARY KEY (id),
        CONSTRAINT "UQ_Configurations_config_key" UNIQUE (config_key)
      );

      CREATE TABLE IF NOT EXISTS ${schema}."Packages" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        code varchar(50) NOT NULL,
        "name" varchar(255) NOT NULL,
        fee int4 NOT NULL,
      	sort int2 DEFAULT '1'::smallint NOT NULL,
        status int2 NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_Packages" PRIMARY KEY (id),
        CONSTRAINT "UQ_Packages_code" UNIQUE (code),
        CONSTRAINT "UQ_Packages_name" UNIQUE (name)
      );

      CREATE TABLE IF NOT EXISTS ${schema}."ReservationOptions" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        code varchar(50) NOT NULL,
        title varchar(255) NOT NULL,
        "type" varchar(50) NOT NULL,
        "name" varchar(255) NOT NULL,
        has_price bool NOT NULL,
        detail jsonb NOT NULL,
        enabled bool NOT NULL,
      	sort int2 DEFAULT '1'::smallint NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_ReservationOptions" PRIMARY KEY (id),
        CONSTRAINT "UQ_ReservationOptions_code" UNIQUE (code),
        CONSTRAINT "UQ_ReservationOptions_name" UNIQUE (name)
      );

      CREATE TABLE IF NOT EXISTS ${schema}."Reservations" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        code varchar(50) NOT NULL,
        user_id uuid NOT NULL,
        status int2 DEFAULT '1'::smallint NOT NULL,
        working_date_1 date NOT NULL,
        working_date_2 date NULL,
        email varchar(255) NOT NULL,
        last_name varchar(255) NOT NULL,
        first_name varchar(255) NOT NULL,
        phone_number varchar(255) NOT NULL,
        postal_code varchar(255) NOT NULL,
        prefecture varchar(255) NOT NULL,
        city varchar(255) NOT NULL,
        address varchar(255) NOT NULL,
        "options" jsonb NULL,
        total_fee int4 NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_Reservations" PRIMARY KEY (id),
        CONSTRAINT "UQ_Reservations_code" UNIQUE (code)
      );

      CREATE TABLE IF NOT EXISTS ${schema}."Reservations_Packages" (
        package_id uuid NOT NULL,
        reservation_id uuid NOT NULL,
        quantity int4 NOT NULL,
        CONSTRAINT "PK_Reservations_Packages" PRIMARY KEY (package_id, reservation_id),
        CONSTRAINT "FK_Reservations_Packages_Packages" FOREIGN KEY (package_id) REFERENCES ${schema}."Packages"(id),
        CONSTRAINT "FK_Reservations_Packages_Reservations" FOREIGN KEY (reservation_id) REFERENCES ${schema}."Reservations"(id)
      );
      CREATE INDEX IF NOT EXISTS "IDX_Reservations_PackageId" ON ${schema}."Reservations_Packages" USING btree (package_id);

      CREATE SEQUENCE IF NOT EXISTS ${schema}."package_code_sequence_seq";

      CREATE OR REPLACE FUNCTION ${schema}.generate_code(target VARCHAR, field VARCHAR, prefix VARCHAR, max INT) RETURNS VARCHAR
      AS $$
        DECLARE
          count INT;
          max_error_count INT;
          error_count INT;
          generate_max BIGINT;
          code VARCHAR;
          tmp_rnd NUMERIC;
        BEGIN
          count = 1;
          max_error_count = 100;
          error_count = 0;

          WHILE count >= 1 LOOP
            IF error_count >= max_error_count THEN
              RAISE 'FAILURE GENERATE UNIQUE CODE.';
            END IF;
            generate_max = (10^max) - 1;

            tmp_rnd = round((random() * (generate_max - 0))::numeric, 0) + 0;
            code = prefix || lpad(tmp_rnd::VARCHAR, max, '0');

            EXECUTE 'SELECT count(*) FROM ' || target || ' WHERE '|| field || '=''' || code || ''';'
            INTO count;
            error_count = error_count + 1;
          END LOOP;
          RETURN code;
        END;
      $$ LANGUAGE plpgsql;

      INSERT INTO ${schema}."Configurations" ("id", config_key, value)
      VALUES
        ('395632b7-dff6-4796-8607-c89eb649e59f', '${ConfigKeyEnum.CALENDAR_BLOCK_RANGE}', '14');
    `);

    const insertPackageQuery = `INSERT INTO ${schema}."Packages" ("id", code, "name", fee, sort, status) VALUES 
      ${PACKAGES_DATA.join(', ')};
    `;
    const insertReservationOptionQuery = `INSERT INTO ${schema}."ReservationOptions" ("id", "name", title, code, "type", has_price, detail, enabled, sort) VALUES 
      ${RESERVATION_OPTIONS_DATA.join(', ')};
    `;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', ${PACKAGES_DATA.length + 1});`;
    await Promise.all([
      queryRunner.query(insertPackageQuery),
      queryRunner.query(insertReservationOptionQuery),
      queryRunner.query(updateSequenceQuery),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const tables = await queryRunner.query(
      `select tablename from pg_tables where schemaname = '${process.env.DATABASE_SCHEMA}' and tablename != 'migrations';`,
    );
    const downQueries = tables.map((e) =>
      queryRunner.query(`drop table if exists ${schema}."${e.tablename}" cascade;`),
    );
    const dropSequences = [queryRunner.query(`drop sequence if exists ${schema}.package_code_sequence_seq;`)];
    const dropFunctions = [
      queryRunner.query(
        `drop function if exists ${schema}.generate_code(target VARCHAR, field VARCHAR, prefix VARCHAR, max INT);`,
      ),
    ];
    await Promise.all([...downQueries, ...dropSequences, ...dropFunctions]);
  }
}
