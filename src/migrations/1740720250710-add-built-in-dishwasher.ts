import { MigrationInterface, QueryRunner } from 'typeorm';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { CODE_PREFIX } from '../common/enums/configuration';
import { PackType } from '../common/enums/pack-type';
import { ReservationDeliveryType, ReservationExtendedWarrantyType } from '../common/enums/reservation-options';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const PACKAGES_DATA = [
  `('65377ff5-801a-4702-a4eb-3c8b67080c90', '${CODE_PREFIX.PACKAGE}00071', 'パナソニック NP-45MD9S シルバー', 162260, 1, 1, ${PackType.BUILD_IN_DISHWASHER}, '{"fields": [{"type": "color", "isRequired": false, "items": [{"code": "PNL001", "value": "ビューティホワイト", "price": 9900}, {"code": "PNL002", "value": "ベージュ", "price": 9900}, {"code": "PNL003", "value": "ダークグレー", "price": 9900}]}]}')`,
  `('da2a4df0-97f0-4a0a-95e3-3819350a2ada', '${CODE_PREFIX.PACKAGE}00072', 'パナソニック NP-45MS9S シルバー', 147100, 1, 1, ${PackType.BUILD_IN_DISHWASHER}, '{"fields": [{"type": "color", "isRequired": false, "items": [{"code": "PNL004", "value": "ビューティーホワイト", "price": 6300}, {"code": "PNL005", "value": "ベージュ", "price": 6300}, {"code": "PNL006", "value": "ダークグレー", "price": 6300}]}]}')`,
  `('85e36b98-4a95-4166-90e8-d39131340654', '${CODE_PREFIX.PACKAGE}00073', 'リンナイ RSW-F403C-B ブラック（ツヤ消し）', 170640, 1, 1, ${PackType.BUILD_IN_DISHWASHER}, null)`,
  `('c46629b0-dc2e-48a0-a840-8bc691c88264', '${CODE_PREFIX.PACKAGE}00074', 'リンナイ RSW-F403C-SV グレー（光沢）', 176600, 1, 1, ${PackType.BUILD_IN_DISHWASHER}, null)`,
];

const RESERVATION_OPTIONS_DATA = [
  `('a40cc663-fe03-4301-ad4b-4b219a0a410e', 'extendedWarranty', '延長保証', '${CODE_PREFIX.RESERVATION_OPTION}010', 'text', true, '{"listOptions": [{"price": 0, "title": "${ReservationExtendedWarrantyType.NOT_ENROLLED}"}, {"price": 7480, "title": "${ReservationExtendedWarrantyType.ENROLLED}"}]}', true, 1, ${PackType.BUILD_IN_DISHWASHER})`,
  `('45fe79f7-c399-4550-a9b1-b09bd70aefad', 'productDeliveryType', '商品の配送種別', '${CODE_PREFIX.RESERVATION_OPTION}011', 'text', true, '{"listOptions": [{"price": 0, "title": "${ReservationDeliveryType.PRE_DELIVERY}"}, {"price": 1100, "title": "${ReservationDeliveryType.SAME_DAY}"}]}', true, 1, ${PackType.BUILD_IN_DISHWASHER})`,
];

export class AddBuiltInDishwasher1740720250710 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Packages}" ADD COLUMN IF NOT EXISTS "options" jsonb NULL`,
    );

    const insertPackageQuery = `INSERT INTO ${schema}."${
      ENTITIES_ENUM.Packages
    }" ("id", code, "name", fee, sort, status, pack_type, "options") VALUES
      ${PACKAGES_DATA.join(', ')};
    `;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 75);`;

    const insertReservationOptionsQuery = `INSERT INTO ${schema}."${
      ENTITIES_ENUM.ReservationOptions
    }" ("id", "name", title, code, "type", has_price, detail, enabled, sort, "pack_type") VALUES
          ${RESERVATION_OPTIONS_DATA.join(', ')};
        `;

    const updateTableQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations_Packages}" ADD COLUMN IF NOT EXISTS "pack_options" jsonb NULL;`;

    const updateReservationQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations}" ALTER COLUMN "working_date_1" DROP NOT NULL`;

    await Promise.all([
      queryRunner.query(insertPackageQuery),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(insertReservationOptionsQuery),
      queryRunner.query(updateTableQuery),
      queryRunner.query(updateReservationQuery),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const deletePackagesQuery = `DELETE FROM ${schema}."${ENTITIES_ENUM.Packages}" WHERE id IN (${PACKAGES_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 71);`;
    const dropColumnQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Packages}" DROP COLUMN IF EXISTS "options";`;

    const deleteReservationOptionsQuery = `DELETE FROM ${schema}."ReservationOptions" WHERE id IN (${RESERVATION_OPTIONS_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;

    const dropTableQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations_Packages}" DROP COLUMN IF EXISTS "pack_options";`;

    const revertReservationQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations}" ALTER COLUMN "working_date_1" SET NOT NULL`;

    await Promise.all([
      queryRunner.query(deletePackagesQuery),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(dropColumnQuery),
      queryRunner.query(deleteReservationOptionsQuery),
      queryRunner.query(dropTableQuery),
      queryRunner.query(revertReservationQuery),
    ]);
  }
}
