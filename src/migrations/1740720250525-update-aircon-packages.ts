import { MigrationInterface, QueryRunner } from 'typeorm';
import { PackageStatus } from '../common/enums/package';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { CODE_PREFIX } from '../common/enums/configuration';
import { PackType } from '../common/enums/pack-type';
import { ReservationExtendedWarrantyType } from '../common/enums/reservation-options';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const DISABLE_PACKAGES = [
  {
    id: '053d99dc-9aca-4b58-ab99-7120002d428d',
    oldName: 'パナソニック エオリア Fシリーズ / 6畳用',
    newName: 'パナソニック エオリア Fシリーズ / 6畳用（2024）（2）',
    sort: 1,
  },
  {
    id: 'c31912f3-ef3f-4c68-8795-e33350163fed',
    oldName: 'パナソニック エオリア Fシリーズ / 8畳用',
    newName: 'パナソニック エオリア Fシリーズ / 8畳用（2024）',
    sort: 2,
  },
  {
    id: '9412a507-ec12-4ab4-bce6-b3608a25e6cd',
    oldName: '三菱電機 霧ヶ峰 GVシリーズ / 10畳用',
    newName: '三菱電機 霧ヶ峰 GVシリーズ / 10畳用（2024）',
    sort: 3,
  },
  {
    id: '1b4b803c-ce78-466d-b996-a615e551f769',
    oldName: '三菱電機 霧ヶ峰 GVシリーズ / 12畳用',
    newName: '三菱電機 霧ヶ峰 GVシリーズ / 12畳用（2024）',
    sort: 4,
  },
  {
    id: '899f85d9-e894-4f9a-a99c-a9351d591d78',
    oldName: '三菱電機 霧ヶ峰 GVシリーズ / 14畳用',
    newName: '三菱電機 霧ヶ峰 GVシリーズ / 14畳用（2024）',
    sort: 5,
  },
  {
    id: 'bec8edbb-6faf-4c80-a1d6-78f8aa90ff48',
    oldName: 'ダイキン AXシリーズ / 12畳用',
    newName: 'ダイキン AXシリーズ / 12畳用（2024）',
    sort: 9,
  },
  {
    id: '521e330b-6571-4631-b461-a862549c84e6',
    oldName: 'ダイキン AXシリーズ / 14畳用',
    newName: 'ダイキン AXシリーズ / 14畳用（2024）',
    sort: 10,
  },
  {
    id: 'c2bf3881-9976-4bb1-b271-8dc9438179e2',
    oldName: '日立 白くまくん V/VLシリーズ / 6畳用',
    newName: '日立 白くまくん V/VLシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: '746be1ea-05f9-4f4a-bfaa-9897b8fbedc5',
    oldName: '日立 白くまくん V/VLシリーズ / 8畳用',
    newName: '日立 白くまくん V/VLシリーズ / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: '48d0949d-b51e-4541-93a5-71607e7399df',
    oldName: 'パナソニック エオリア EXシリーズ / 6畳用',
    newName: 'パナソニック エオリア EXシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: '7d7c49e6-cc8b-4a4e-9b9c-b702aafc4199',
    oldName: 'パナソニック エオリア EXシリーズ / 8畳用',
    newName: 'パナソニック エオリア EXシリーズ / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: 'd61ac552-944e-4b96-9d8c-a4c1d6bdd391',
    oldName: 'ダイキン CXシリーズ / 6畳用',
    newName: 'ダイキン CXシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: 'c49a7c29-b2f9-4116-8193-431d45a698f1',
    oldName: '最新モデル格安パック / 6畳用',
    newName: '最新モデル格安パック / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: '6559da8c-14df-4f9e-bc0b-5b0b526e77a0',
    oldName: 'パナソニック エオリア Jシリーズ / 6畳用',
    newName: 'パナソニック エオリア Jシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: 'e0070aa0-1a5b-403e-b524-be79f5c72188',
    oldName: 'パナソニック エオリア Jシリーズ / 8畳用',
    newName: 'パナソニック エオリア Jシリーズ / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: 'fa99a7d6-0ef8-4b25-bbb9-b038974bac29',
    oldName: 'パナソニック エオリア Jシリーズ / 10畳用',
    newName: 'パナソニック エオリア Jシリーズ / 10畳用（2024）',
    sort: 10000,
  },
  {
    id: '71250df3-48ea-4410-99ba-e5004058392b',
    oldName: '三菱電機 霧ヶ峰 GVシリーズ / 8畳用',
    newName: '三菱電機 霧ヶ峰 GVシリーズ / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: '58e06f1c-6bd3-4fe3-9543-d8ec746b474a',
    oldName: 'ダイキン CXシリーズ / 8畳用',
    newName: 'ダイキン CXシリーズ / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: 'c4e467d3-c7ad-4490-8479-cf79271f4cbd',
    oldName: '最新モデル格安パック / 8畳用',
    newName: '最新モデル格安パック / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: '1f8693a9-89fe-4d91-9f9e-20a2f5094959',
    oldName: '三菱電機 霧ヶ峰 GVシリーズ / 6畳用',
    newName: '三菱電機 霧ヶ峰 GVシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: '04342604-6c16-42ce-9e42-dc08e99ae546',
    oldName: '日立 白くまくん AJシリーズ / 6畳用',
    newName: '日立 白くまくん AJシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: '9e88c751-2945-46dd-a3be-dac894764f76',
    oldName: '日立 白くまくん AJシリーズ / 8畳用',
    newName: '日立 白くまくん AJシリーズ / 8畳用（2024）',
    sort: 10000,
  },
  {
    id: '52b6ee22-262d-49b0-96f4-7e7dd8e687da',
    oldName: '三菱電機 霧ヶ峰 BXVシリーズ / 6畳用',
    newName: '三菱電機 霧ヶ峰 BXVシリーズ / 6畳用（2024）',
    sort: 10000,
  },
  {
    id: '788b5881-975c-4e58-9522-49d26c22f521',
    oldName: '三菱電機 霧ヶ峰 BXVシリーズ / 8畳用',
    newName: '三菱電機 霧ヶ峰 BXVシリーズ / 8畳用（2024）',
    sort: 10000,
  },
];
const PACKAGES_DATA = [
  `('2eb6afa2-4ad4-42da-9660-929b7997eb17', '${CODE_PREFIX.PACKAGE}00031', '三菱電機 GVシリーズ 6畳用', 74330, 1, 1, ${PackType.AIRCON_INSTALL})`,
  `('2b97ee88-d5a9-4194-a1e9-13e15f268218', '${CODE_PREFIX.PACKAGE}00032', '三菱電機 GVシリーズ 8畳用', 84230, 2, 1, ${PackType.AIRCON_INSTALL})`,
  `('d281ba2b-d526-43c3-b70a-6525295824ad', '${CODE_PREFIX.PACKAGE}00033', '三菱電機 GVシリーズ 10畳用', 96800, 3, 1, ${PackType.AIRCON_INSTALL})`,
  `('626d5825-bfbf-4e1c-8e4f-6d4aedbd8457', '${CODE_PREFIX.PACKAGE}00034', '三菱電機 GVシリーズ 12畳用', 123360, 4, 1, ${PackType.AIRCON_INSTALL})`,
  `('8bfd179c-0e2c-4300-8e3a-740b6bf82172', '${CODE_PREFIX.PACKAGE}00035', '三菱電機 GVシリーズ 14畳用', 133730, 5, 1, ${PackType.AIRCON_INSTALL})`,
  `('0fd68bb6-1217-4985-a981-a54d0bbbfb41', '${CODE_PREFIX.PACKAGE}00036', '三菱電機 GVシリーズ 18畳用', 176000, 6, 1, ${PackType.AIRCON_INSTALL})`,
  `('32546eb6-d9d3-471f-bc02-7818735e9054', '${CODE_PREFIX.PACKAGE}00037', 'パナソニック Fシリーズ 6畳用', 75750, 7, 1, ${PackType.AIRCON_INSTALL})`,
  `('b41d9d42-00f1-4420-8c6f-b5853d506d42', '${CODE_PREFIX.PACKAGE}00038', 'パナソニック Fシリーズ 8畳用', 94600, 8, 1, ${PackType.AIRCON_INSTALL})`,
  `('b98002de-0b08-4d6a-a7f5-2955bd52dc91', '${CODE_PREFIX.PACKAGE}00039', 'パナソニック Fシリーズ 10畳用', 106390, 9, 1, ${PackType.AIRCON_INSTALL})`,
  `('39ded770-f776-47f3-b547-f6b911ca5231', '${CODE_PREFIX.PACKAGE}00040', 'パナソニック Fシリーズ 12畳用', 130120, 10, 1, ${PackType.AIRCON_INSTALL})`,
  `('564de8f9-a248-4979-96c6-e5565c5b3fc4', '${CODE_PREFIX.PACKAGE}00041', 'パナソニック Fシリーズ 14畳用', 141900, 11, 1, ${PackType.AIRCON_INSTALL})`,
  `('c59c13e4-26d6-455b-9d5e-d24bcd2b71c7', '${CODE_PREFIX.PACKAGE}00042', 'パナソニック Fシリーズ 18畳用', 184650, 12, 1, ${PackType.AIRCON_INSTALL})`,
  `('20c053b1-bfdc-4e1e-9b63-21515ccde1cb', '${CODE_PREFIX.PACKAGE}00043', 'パナソニック Jシリーズ 6畳用', 103820, 13, 1, ${PackType.AIRCON_INSTALL})`,
  `('64c82fe2-5ad5-495f-9790-8e05e42f9ff0', '${CODE_PREFIX.PACKAGE}00044', 'パナソニック Jシリーズ 8畳用', 112070, 14, 1, ${PackType.AIRCON_INSTALL})`,
  `('abda6d9f-b345-4268-a1f5-a201112b6490', '${CODE_PREFIX.PACKAGE}00045', 'パナソニック Jシリーズ 10畳用', 120320, 15, 1, ${PackType.AIRCON_INSTALL})`,
  `('3325bec6-b86f-462b-8269-435bf08d86f1', '${CODE_PREFIX.PACKAGE}00046', 'パナソニック Jシリーズ 12畳用', 138880, 16, 1, ${PackType.AIRCON_INSTALL})`,
  `('6627bc20-6a83-4160-8e2d-c571c95bd75f', '${CODE_PREFIX.PACKAGE}00047', 'パナソニック Jシリーズ 14畳用', 149190, 17, 1, ${PackType.AIRCON_INSTALL})`,
  `('e90f4e89-dc77-4d12-ba94-6c7a788f5d8c', '${CODE_PREFIX.PACKAGE}00048', 'パナソニック Jシリーズ 18畳用', 186320, 18, 1, ${PackType.AIRCON_INSTALL})`,
  `('aa22bb55-824b-4d4e-8268-2675e2acd615', '${CODE_PREFIX.PACKAGE}00049', '三菱電機 AXVシリーズ 6畳用', 123050, 19, 1, ${PackType.AIRCON_INSTALL})`,
  `('3248820a-cd3a-49fc-84e0-4c38c5b9896b', '${CODE_PREFIX.PACKAGE}00050', '三菱電機 AXVシリーズ 8畳用', 129760, 20, 1, ${PackType.AIRCON_INSTALL})`,
  `('2c5ecd04-eddc-4158-a3ab-c95c1adb0a66', '${CODE_PREFIX.PACKAGE}00051', '三菱電機 AXVシリーズ 10畳用', 142410, 21, 1, ${PackType.AIRCON_INSTALL})`,
  `('a8f74597-da01-4003-b590-13708ab67881', '${CODE_PREFIX.PACKAGE}00052', '三菱電機 AXVシリーズ 12畳用', 159190, 22, 1, ${PackType.AIRCON_INSTALL})`,
  `('10b83391-ad58-48a4-922c-03be4bfa4bbf', '${CODE_PREFIX.PACKAGE}00053', '三菱電機 AXVシリーズ 14畳用', 169260, 23, 1, ${PackType.AIRCON_INSTALL})`,
  `('5be4e226-94c8-44e1-a776-6d6e7d1d91c3', '${CODE_PREFIX.PACKAGE}00054', '三菱電機 AXVシリーズ 18畳用', 192760, 24, 1, ${PackType.AIRCON_INSTALL})`,
  `('a242ab2a-0cd6-46e3-9ef9-df1d2f153860', '${CODE_PREFIX.PACKAGE}00055', 'パナソニック EXシリーズ 6畳用', 148920, 25, 1, ${PackType.AIRCON_INSTALL})`,
  `('a2581150-c745-406c-a7ba-520c22aa6bb3', '${CODE_PREFIX.PACKAGE}00056', 'パナソニック EXシリーズ 8畳用', 165140, 26, 1, ${PackType.AIRCON_INSTALL})`,
  `('1956fb99-42a3-4439-8262-0ebdd2d331fd', '${CODE_PREFIX.PACKAGE}00057', 'パナソニック EXシリーズ 10畳用', 187140, 27, 1, ${PackType.AIRCON_INSTALL})`,
  `('cb5d9a69-cbce-4a0b-82e1-74aebd66409e', '${CODE_PREFIX.PACKAGE}00058', 'パナソニック EXシリーズ 12畳用', 197320, 28, 1, ${PackType.AIRCON_INSTALL})`,
  `('164924f9-7b02-49e0-b7bf-ebc49903ded6', '${CODE_PREFIX.PACKAGE}00059', 'パナソニック EXシリーズ 14畳用', 206120, 29, 1, ${PackType.AIRCON_INSTALL})`,
  `('06ee5e35-2062-494a-adde-6c7c9afaea2a', '${CODE_PREFIX.PACKAGE}00060', 'パナソニック EXシリーズ 18畳用', 236920, 30, 1, ${PackType.AIRCON_INSTALL})`,
  `('dd6ec121-2a26-489c-9802-8e9bf186b0a4', '${CODE_PREFIX.PACKAGE}00061', 'パナソニック EXシリーズ 20畳用', 265520, 31, 1, ${PackType.AIRCON_INSTALL})`,
  `('916bf3f0-d3f4-48dd-9d93-6d1e19d39ece', '${CODE_PREFIX.PACKAGE}00062', 'パナソニック EXシリーズ 23畳用', 283120, 32, 1, ${PackType.AIRCON_INSTALL})`,
  `('0fb29725-a187-4a3f-ad1e-8ecf09404a69', '${CODE_PREFIX.PACKAGE}00063', 'ダイキン CXシリーズ 6畳用', 155730, 33, 1, ${PackType.AIRCON_INSTALL})`,
  `('3eb34b86-6a51-4826-b296-83ff91a6f57c', '${CODE_PREFIX.PACKAGE}00064', 'ダイキン CXシリーズ 8畳用', 165950, 34, 1, ${PackType.AIRCON_INSTALL})`,
  `('1896fa29-e026-4519-8d09-7decd114380f', '${CODE_PREFIX.PACKAGE}00065', 'ダイキン CXシリーズ 10畳用', 177730, 35, 1, ${PackType.AIRCON_INSTALL})`,
  `('f03dd33b-557e-41f1-8f14-44c772a65409', '${CODE_PREFIX.PACKAGE}00066', 'ダイキン CXシリーズ 12畳用', 195020, 36, 1, ${PackType.AIRCON_INSTALL})`,
  `('2909a4e7-de93-4ae1-8deb-60f7c2f88225', '${CODE_PREFIX.PACKAGE}00067', 'ダイキン CXシリーズ 14畳用', 206800, 37, 1, ${PackType.AIRCON_INSTALL})`,
  `('d89a0529-ba98-4c9c-85b8-0dcaea4f71f8', '${CODE_PREFIX.PACKAGE}00068', 'ダイキン CXシリーズ 18畳用', 246090, 38, 1, ${PackType.AIRCON_INSTALL})`,
  `('469bcbc3-459f-488a-bddc-e0eb02527fe3', '${CODE_PREFIX.PACKAGE}00069', 'ダイキン CXシリーズ 20畳用', 284120, 39, 1, ${PackType.AIRCON_INSTALL})`,
  `('0e8ca9de-8fae-403a-9423-53bb9e195758', '${CODE_PREFIX.PACKAGE}00070', 'ダイキン CXシリーズ 23畳用', 311620, 40, 1, ${PackType.AIRCON_INSTALL})`,
];

const RESERVATION_OPTIONS_DATA = [
  `('04a96f43-ab75-4004-85e0-5f710458f8a1', 'extendedWarranty', '延長保証', '${CODE_PREFIX.RESERVATION_OPTION}009', 'text', true, '{"listOptions": [{"price": 0, "title": "${ReservationExtendedWarrantyType.NOT_ENROLLED}"}, {"price": 4460, "title": "${ReservationExtendedWarrantyType.ENROLLED}"}]}', true, 7, ${PackType.AIRCON_INSTALL})`,
];
const UPDATE_RESERVATION_OPTIONS_DATA = [
  {
    id: `64d4533e-8071-41e4-8a3a-b66f927edf86`,
    newDetail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 8800}',
    oldDetail: '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 8800}',
  },
  {
    id: `fa31514b-21c8-42b9-a51e-4eab64aa56a0`,
    newDetail: '{"unit": "台", "maxQuantity": 10, "pricePerUnit": 14850}',
    oldDetail: '{"unit": "セット", "maxQuantity": 10, "pricePerUnit": 14850}',
  },
];
const UPDATE_RESERVATION_OPTIONS_SORT = [
  {
    id: `1c3464a9-2c65-4dcd-80be-c34e2f47209e`,
    newSort: 8,
    oldSort: 7,
  },
];

export class UpdateAirconPackages1740720250525 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const updateDisablePackageQuery = DISABLE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${PackageStatus.DISABLE}, sort = 10000, name = '${item.newName}' WHERE id = '${item.id}'`,
    );
    const insertNewPackageQuery = `INSERT INTO ${schema}."${
      ENTITIES_ENUM.Packages
    }" ("id", code, "name", fee, sort, status, pack_type) VALUES
      ${PACKAGES_DATA.join(', ')};
    `;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 71);`;

    const insertReservationOptionsQuery = `INSERT INTO ${schema}."${
      ENTITIES_ENUM.ReservationOptions
    }" ("id", "name", title, code, "type", has_price, detail, enabled, sort, "pack_type") VALUES
      ${RESERVATION_OPTIONS_DATA.join(', ')};
    `;

    const updateReservationOptionsQuery = UPDATE_RESERVATION_OPTIONS_DATA.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.ReservationOptions}" SET detail = '${item.newDetail}' WHERE id = '${item.id}'`,
    );

    const updateReservationOptionsSortQuery = UPDATE_RESERVATION_OPTIONS_SORT.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.ReservationOptions}" SET sort = ${item.newSort} WHERE id = '${item.id}'`,
    );

    const updateTableQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations_Packages}" ADD COLUMN IF NOT EXISTS "pack_price" int4 NOT NULL DEFAULT 0;`;

    await Promise.all([
      queryRunner.query(updateDisablePackageQuery.join(';')),
      queryRunner.query(insertNewPackageQuery),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(insertReservationOptionsQuery),
      queryRunner.query(updateReservationOptionsQuery.join(';')),
      queryRunner.query(updateReservationOptionsSortQuery.join(';')),
      queryRunner.query(updateTableQuery),
    ]);

    const updateMigrateDataQuery = `UPDATE ${schema}."${ENTITIES_ENUM.Reservations_Packages}" SET "pack_price" = p.fee
      FROM ${schema}."${ENTITIES_ENUM.Packages}" p
      WHERE ${schema}."${ENTITIES_ENUM.Reservations_Packages}".package_id = p.id`;
    await queryRunner.query(updateMigrateDataQuery);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const deletePackagesQuery = `DELETE FROM ${schema}."${ENTITIES_ENUM.Packages}" WHERE id IN (${PACKAGES_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const updateDisablePackageQuery = DISABLE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${PackageStatus.ENABLE}, sort = ${item.sort}, name = '${item.oldName}' WHERE id = '${item.id}'`,
    );
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 31);`;

    const deleteReservationOptionsQuery = `DELETE FROM ${schema}."ReservationOptions" WHERE id IN (${RESERVATION_OPTIONS_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;

    const updateReservationOptionsQuery = UPDATE_RESERVATION_OPTIONS_DATA.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.ReservationOptions}" SET detail = '${item.oldDetail}' WHERE id = '${item.id}'`,
    );

    const updateReservationOptionsSortQuery = UPDATE_RESERVATION_OPTIONS_SORT.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.ReservationOptions}" SET sort = ${item.oldSort} WHERE id = '${item.id}'`,
    );

    const dropTableQuery = `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations_Packages}" DROP COLUMN IF EXISTS "pack_price";`;

    await Promise.all([
      queryRunner.query(deletePackagesQuery),
      queryRunner.query(updateDisablePackageQuery.join(';')),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(deleteReservationOptionsQuery),
      queryRunner.query(updateReservationOptionsQuery.join(';')),
      queryRunner.query(updateReservationOptionsSortQuery.join(';')),
      queryRunner.query(dropTableQuery),
    ]);
  }
}
