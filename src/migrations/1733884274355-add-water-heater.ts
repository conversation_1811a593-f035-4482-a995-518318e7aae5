import { MigrationInterface, QueryRunner } from 'typeorm';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';
import { CODE_PREFIX, ConfigKeyEnum } from '../common/enums/configuration';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const PACKAGES_DATA = [
  `('363f8d91-cb0d-41ad-9932-e0a3c9aeb966', '${CODE_PREFIX.PACKAGE}00025', 'コロナ【高圧力タイプ / 一般地用】370L', 460900, 1, 1, ${PackType.WATER_HEATER})`,
  `('37f761df-164f-428c-a08f-9a4eee5a707a', '${CODE_PREFIX.PACKAGE}00026', 'コロナ【高圧力タイプ / 一般地用】460L', 488510, 2, 1, ${PackType.WATER_HEATER})`,
];

export class AddWaterHeater1733884274355 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const updateTableQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.CalendarBlocks}" ADD COLUMN IF NOT EXISTS "pack_type" int2 NOT NULL DEFAULT ${PackType.AIRCON_INSTALL}`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Configurations}" ADD COLUMN IF NOT EXISTS "pack_type" int2 NOT NULL DEFAULT ${PackType.AIRCON_INSTALL}`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.ReservationOptions}" ADD COLUMN IF NOT EXISTS "pack_type" int2 NOT NULL DEFAULT ${PackType.AIRCON_INSTALL}`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations}" ADD COLUMN IF NOT EXISTS "pack_type" int2 NOT NULL DEFAULT ${PackType.AIRCON_INSTALL}`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations}" ADD COLUMN IF NOT EXISTS "total_discount" int4 NOT NULL DEFAULT 0`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Packages}" ADD COLUMN IF NOT EXISTS "pack_type" int2 NOT NULL DEFAULT ${PackType.AIRCON_INSTALL}`,
    ];
    const dropUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Configurations}" DROP CONSTRAINT IF EXISTS "UQ_Configurations_config_key"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.CalendarBlocks}" DROP CONSTRAINT IF EXISTS "PK_CalendarBlocks"`,
    ];
    const dropIndexQuery = [`DROP INDEX IF EXISTS ${schema}."IDX_CalendarBlocks_BlockDate"`];
    await Promise.all([
      queryRunner.query(updateTableQuery.join(';')),
      queryRunner.query(dropUniqueConstraintQuery.join(';')),
      queryRunner.query(dropIndexQuery.join(';')),
    ]);

    const insertNewPackageQuery = `INSERT INTO ${schema}."Packages" ("id", code, "name", fee, sort, status, pack_type) VALUES 
      ${PACKAGES_DATA.join(', ')};
    `;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 27);`;

    const addUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Configurations}" ADD CONSTRAINT "UQ_Configurations_config_key" UNIQUE ("config_key", "pack_type")`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.CalendarBlocks}" ADD CONSTRAINT "PK_CalendarBlocks" PRIMARY KEY ("block_date", "pack_type")`,
    ];
    const insertConfigurationsQuery = `INSERT INTO ${schema}."Configurations" ("id", config_key, value, pack_type) VALUES ('315a2f5d-6ac0-4378-8e27-3e4ef676d7f7', '${ConfigKeyEnum.CALENDAR_BLOCK_RANGE}', '14', 2);`;
    await Promise.all([
      queryRunner.query(addUniqueConstraintQuery.join(';')),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(insertConfigurationsQuery),
      queryRunner.query(insertNewPackageQuery),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const updateTableQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.CalendarBlocks}" DROP COLUMN IF EXISTS "pack_type"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Configurations}" DROP COLUMN IF EXISTS "pack_type"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.ReservationOptions}" DROP COLUMN IF EXISTS "pack_type"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations}" DROP COLUMN IF EXISTS "pack_type"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Reservations}" DROP COLUMN IF EXISTS "total_discount"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Packages}" DROP COLUMN IF EXISTS "pack_type"`,
    ];
    const deleteConfigurationsQuery = `DELETE FROM ${schema}."Configurations" WHERE id = '315a2f5d-6ac0-4378-8e27-3e4ef676d7f7';`;
    const dropUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Configurations}" DROP CONSTRAINT IF EXISTS "UQ_Configurations_config_key"`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.CalendarBlocks}" DROP CONSTRAINT IF EXISTS "PK_CalendarBlocks"`,
    ];
    await Promise.all([
      queryRunner.query(updateTableQuery.join(';')),
      queryRunner.query(deleteConfigurationsQuery),
      queryRunner.query(dropUniqueConstraintQuery.join(';')),
    ]);

    const deletePackagesQuery = `DELETE FROM ${schema}."Packages" WHERE id IN (${PACKAGES_DATA.map(
      (item) => `'${item.split("'")[1]}'`,
    ).join(', ')})`;
    const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', 25);`;
    const addUniqueConstraintQuery = [
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.Configurations}" ADD CONSTRAINT "UQ_Configurations_config_key" UNIQUE ("config_key")`,
      `ALTER TABLE ${schema}."${ENTITIES_ENUM.CalendarBlocks}" ADD CONSTRAINT "PK_CalendarBlocks" PRIMARY KEY ("block_date")`,
    ];
    const addIndexQuery = [
      `CREATE INDEX IF NOT EXISTS "IDX_CalendarBlocks_BlockDate" ON ${schema}."${ENTITIES_ENUM.CalendarBlocks}" ("block_date")`,
    ];
    await Promise.all([
      queryRunner.query(addUniqueConstraintQuery.join(';')),
      queryRunner.query(deletePackagesQuery),
      queryRunner.query(updateSequenceQuery),
      queryRunner.query(addIndexQuery.join(';')),
    ]);
  }
}
