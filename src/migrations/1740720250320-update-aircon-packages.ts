import { MigrationInterface, QueryRunner } from 'typeorm';
import { PackageStatus } from '../common/enums/package';
import { ENTITIES_ENUM } from '../common/enums/entities';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

const DISABLE_PACKAGES = [{ id: '71250df3-48ea-4410-99ba-e5004058392b', sort: 2 }];
const ENABLE_PACKAGES = [{ id: 'c31912f3-ef3f-4c68-8795-e33350163fed', sort: 2, fee: 83400 }];

export class UpdateAirconPackages1740720250320 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const updateDisablePackageQuery = `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${
      PackageStatus.DISABLE
    }, sort = 10000 WHERE id IN (${DISABLE_PACKAGES.map((item) => `'${item.id}'`).join(', ')})`;
    const updateEnablePackageQuery = ENABLE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${PackageStatus.ENABLE},fee = ${item.fee}, sort = ${item.sort} WHERE id = '${item.id}'`,
    );
    await Promise.all([
      queryRunner.query(updateDisablePackageQuery),
      queryRunner.query(updateEnablePackageQuery.join(';')),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const updateEnablePackageQuery = `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${
      PackageStatus.DISABLE
    }, sort = 10000 WHERE id IN (${ENABLE_PACKAGES.map((item) => `'${item.id}'`).join(', ')})`;
    const updateDisablePackageQuery = DISABLE_PACKAGES.map(
      (item) =>
        `UPDATE ${schema}."${ENTITIES_ENUM.Packages}" SET status = ${PackageStatus.ENABLE}, sort = ${item.sort} WHERE id = '${item.id}'`,
    );
    await Promise.all([
      queryRunner.query(updateDisablePackageQuery.join(';')),
      queryRunner.query(updateEnablePackageQuery),
    ]);
  }
}
