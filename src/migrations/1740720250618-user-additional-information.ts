import { MigrationInterface, QueryRunner } from 'typeorm';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { AdditionalInfoStatus } from '../common/enums/additional-information';
const schema = process.env.DATABASE_SCHEMA ? `"${process.env.DATABASE_SCHEMA}"` : `"public"`;

export class UserAdditionalInformation1740720250618 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS ${schema}."${ENTITIES_ENUM.AdditionalInformation}" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        template_type int2 NOT NULL,
        status int2 DEFAULT ${AdditionalInfoStatus.NOT_UPLOADED} NOT NULL,
        verification_token varchar(64) NOT NULL,
        is_enable bool DEFAULT true NOT NULL,
        reservation_id uuid NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_${ENTITIES_ENUM.AdditionalInformation}" PRIMARY KEY (id),
        CONSTRAINT "FK_${ENTITIES_ENUM.AdditionalInformation}_${ENTITIES_ENUM.Reservations}" FOREIGN KEY (reservation_id) REFERENCES ${schema}."${ENTITIES_ENUM.Reservations}"(id)
      );
      CREATE INDEX IF NOT EXISTS "IDX_${ENTITIES_ENUM.AdditionalInformation}_${ENTITIES_ENUM.Reservations}Id" ON ${schema}."${ENTITIES_ENUM.AdditionalInformation}" USING btree (reservation_id);

      CREATE TABLE IF NOT EXISTS ${schema}."${ENTITIES_ENUM.AdditionActivities}" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        "type" int2 NOT NULL,
        addition_attempt_id uuid NULL,
        additional_information_id uuid NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_${ENTITIES_ENUM.AdditionActivities}" PRIMARY KEY (id),
        CONSTRAINT "FK_${ENTITIES_ENUM.AdditionActivities}_${ENTITIES_ENUM.AdditionalInformation}" FOREIGN KEY (additional_information_id) REFERENCES ${schema}."${ENTITIES_ENUM.AdditionalInformation}"(id)
      );
      CREATE INDEX IF NOT EXISTS "IDX_${ENTITIES_ENUM.AdditionActivities}_${ENTITIES_ENUM.AdditionalInformation}Id" ON ${schema}."${ENTITIES_ENUM.AdditionActivities}" USING btree (additional_information_id);
      CREATE INDEX IF NOT EXISTS "IDX_${ENTITIES_ENUM.AdditionActivities}_${ENTITIES_ENUM.AdditionAttempts}Id" ON ${schema}."${ENTITIES_ENUM.AdditionActivities}" USING btree (addition_attempt_id);

      CREATE TABLE IF NOT EXISTS ${schema}."${ENTITIES_ENUM.AdditionAttempts}" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        "detail" jsonb NULL,
        is_deleted bool DEFAULT false NOT NULL,
        additional_information_id uuid NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_${ENTITIES_ENUM.AdditionAttempts}" PRIMARY KEY (id),
        CONSTRAINT "FK_${ENTITIES_ENUM.AdditionAttempts}_${ENTITIES_ENUM.AdditionalInformation}" FOREIGN KEY (additional_information_id) REFERENCES ${schema}."${ENTITIES_ENUM.AdditionalInformation}"(id)
      );
      CREATE INDEX IF NOT EXISTS "IDX_${ENTITIES_ENUM.AdditionAttempts}_${ENTITIES_ENUM.AdditionActivities}Id" ON ${schema}."${ENTITIES_ENUM.AdditionAttempts}" USING btree (additional_information_id);

      CREATE TABLE IF NOT EXISTS ${schema}."${ENTITIES_ENUM.Documents}" (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        "path" varchar NOT NULL,
        "name" varchar(255) NOT NULL,
        document_type int2 NOT NULL,
        addition_attempt_id uuid NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "PK_${ENTITIES_ENUM.Documents}" PRIMARY KEY (id),
        CONSTRAINT "FK_${ENTITIES_ENUM.Documents}_${ENTITIES_ENUM.AdditionAttempts}" FOREIGN KEY (addition_attempt_id) REFERENCES ${schema}."${ENTITIES_ENUM.AdditionAttempts}"(id)
      );
      CREATE INDEX IF NOT EXISTS "IDX_${ENTITIES_ENUM.Documents}_${ENTITIES_ENUM.AdditionAttempts}Id" ON ${schema}."${ENTITIES_ENUM.Documents}" USING btree (addition_attempt_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE IF EXISTS ${schema}."${ENTITIES_ENUM.Documents}";
      DROP TABLE IF EXISTS ${schema}."${ENTITIES_ENUM.AdditionAttempts}";
      DROP TABLE IF EXISTS ${schema}."${ENTITIES_ENUM.AdditionalInformation}";
      DROP TABLE IF EXISTS ${schema}."${ENTITIES_ENUM.AdditionActivities}";
    `);
  }
}
