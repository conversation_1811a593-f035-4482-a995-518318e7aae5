import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { LABO_INSTALL_PACK_REDIRECT_URL_KEY, REDIRECT_TO_USER } from '../constants';

@Injectable()
export class AuthenticationRedirectGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    if (!request.isAuthenticated()) {
      if (!request.session) {
        request.session = {};
      }
      request.session[LABO_INSTALL_PACK_REDIRECT_URL_KEY] = request.url;
      request.session[REDIRECT_TO_USER] = request.url;
      throw new UnauthorizedException();
    }
    return true;
  }
}
