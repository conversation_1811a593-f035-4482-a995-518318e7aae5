const TERRY_ROUTE_PREFIX = 'admin/labo-install-pack';
const GENERATE_TERRY_ROUTE = (path: string) => `/${TERRY_ROUTE_PREFIX}/${path}`.replace(/\/\//g, '/');
export const routes = {
  terry: {
    reservation: {
      reservationListPage: GENERATE_TERRY_ROUTE('reservations'),
      pagingReservation: GENERATE_TERRY_ROUTE('api/reservations'),
      reservationDetailPage: GENERATE_TERRY_ROUTE('reservations/:id'),
      createAddingDocumentUrl: GENERATE_TERRY_ROUTE('api/reservations/:id/create-url'),
      confirmReservation: GENERATE_TERRY_ROUTE('api/reservations/:id/confirm'),
      cancelReservation: GENERATE_TERRY_ROUTE('api/reservations/:id/cancel'),
    },
    packages: {
      packageListPage: GENERATE_TERRY_ROUTE('packages'),
      pagingPackage: GENERATE_TERRY_ROUTE('api/packages'),
      allPackages: GENERATE_TERRY_ROUTE('api/packages/all'),
      newPackagePage: GENERATE_TERRY_ROUTE('packages/new'),
      packageDetailPage: GENERATE_TERRY_ROUTE('packages/:id'),
      createPackage: GENERATE_TERRY_ROUTE('api/packages'),
      updatePackage: GENERATE_TERRY_ROUTE('api/packages/:id'),
      deletePackage: GENERATE_TERRY_ROUTE('api/packages/:id'),
      exportPackage: GENERATE_TERRY_ROUTE('api/packages/export'),
      importPackage: GENERATE_TERRY_ROUTE('api/packages/import'),
    },
    calendarBlock: {
      calendarBlockPage: GENERATE_TERRY_ROUTE('calendar-blocks'),
      getCalendarBlock: GENERATE_TERRY_ROUTE('api/calendar-blocks'),
      setCalendarBlock: GENERATE_TERRY_ROUTE('api/calendar-blocks'),
      deleteCalendarBlock: GENERATE_TERRY_ROUTE('api/calendar-blocks'),
    },
    configuration: {
      updateConfiguration: GENERATE_TERRY_ROUTE('api/configurations/:id'),
    },
    reservationOptions: {
      reservationOptionsPage: GENERATE_TERRY_ROUTE('reservation-options'),
      reservationOptionDetailPage: GENERATE_TERRY_ROUTE('reservation-options/:id'),
      updateReservationOption: GENERATE_TERRY_ROUTE('reservation-options/:id'),
    },
    additionalInformation: {
      refreshUploadUrl: GENERATE_TERRY_ROUTE('api/additional-information/:id/refresh'),
      deleteAdditionalInformation: GENERATE_TERRY_ROUTE('api/additional-information/:id'),
      getAdditionalInfoActivities: GENERATE_TERRY_ROUTE('api/additional-information/:id/activities'),
      getAdditionalInfoAttemptDetail: GENERATE_TERRY_ROUTE(
        'api/additional-information/:additionId/addition-attempts/:attemptId',
      ),
    },
  },
  user: {
    airconInstallPack: {
      landingPage: '/aircon-install-pack/',
      packageDetailPage: '/aircon-install-pack/:modelId/',
      newReservationPage: '/aircon-install-pack/reserve/',
      reservationConfirmPage: '/aircon-install-pack/reserve/confirm/',
      reservationThankPage: '/aircon-install-pack/reserve/thanks/',
    },
    waterHeaterPack: {
      landingPage: '/water-heater-pack/',
      newReservationPage: '/water-heater-pack/reserve/',
      reservationConfirmPage: '/water-heater-pack/reserve/confirm/',
      reservationThankPage: '/water-heater-pack/reserve/thanks/',
    },
    dishwasherPack: {
      landingPage: '/countertop-dishwasher-pack/',
      newReservationPage: '/countertop-dishwasher-pack/reserve/',
      reservationConfirmPage: '/countertop-dishwasher-pack/reserve/confirm/',
      reservationThankPage: '/countertop-dishwasher-pack/reserve/thanks/',
    },
    builtInDishwasherPack: {
      standard: {
        generic: '/built-in-dishwasher-pack/reserve/',
        newReservationPage: '/built-in-dishwasher-pack/reserve/:productCode/',
        reservationConfirmPage: '/built-in-dishwasher-pack/reserve/:productCode/confirm/',
        reservationThankPage: '/built-in-dishwasher-pack/reserve/:productCode/thanks/',
      },
      unspecified: {
        newReservationPage: '/built-in-dishwasher-pack/reserve/',
        reservationConfirmPage: '/built-in-dishwasher-pack/reserve/confirm/',
        reservationThankPage: '/built-in-dishwasher-pack/reserve/thanks/',
      },
    },
    additionalInformation: {
      uploadPage: '/additional-information/',
      confirmPage: '/additional-information/confirm/',
      thankPage: '/additional-information/thank/',
    },
  },
};
