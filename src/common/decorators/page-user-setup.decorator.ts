import { applyDecorators, UseInterceptors } from '@nestjs/common';
import { PageUserSetupInterceptor } from '../interceptors/page-user-setup.interceptor';
import { PageUserInfo } from '../types/page-info.type';
import { PageInfoDecorator } from './page-admin-info.decorator';

export const PageUserSetup = (pageInfo: PageUserInfo) =>
  applyDecorators(PageInfoDecorator(pageInfo), UseInterceptors(PageUserSetupInterceptor));
