import { applyDecorators, UseInterceptors } from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { LIMIT_5_MB } from '../constants';

interface FileField {
  name: string;
  fileType: string[];
  maxCount?: number;
  fileSize?: number;
}

export const MultiInputFiles = (fields: FileField[], fileInterceptors = []) => {
  return applyDecorators(
    UseInterceptors(
      FileFieldsInterceptor(
        fields.map((field) => ({
          name: field.name,
          maxCount: field.maxCount || 1,
        })),
        {
          limits: {
            files: fields.reduce((acc, field) => acc + (field.maxCount || 1), 0),
            fileSize: LIMIT_5_MB,
          },
          fileFilter: (req, file, callback) => {
            const field = fields.find((f) => f.name === file.fieldname);
            if (!field) {
              return callback(new Error('Invalid field name'), false);
            }
            const fileTypePattern = new RegExp(`^(${field.fileType.join('|')})$`);
            if (!fileTypePattern.test(file.mimetype)) {
              return callback(new Error('Invalid file type'), false);
            }
            callback(null, true);
          },
        },
      ),
      ...fileInterceptors,
    ),
  );
};
