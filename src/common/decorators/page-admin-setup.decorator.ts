import { applyDecorators, UseInterceptors } from '@nestjs/common';
import { PageInfoDecorator } from './page-admin-info.decorator';
import { TerryConfigInterceptor } from '../interceptors/terry-config.interceptor';
import { PageAdminInfo } from '../types/page-info.type';

export const PageAdminSetup = (pageInfo: PageAdminInfo) =>
  applyDecorators(PageInfoDecorator(pageInfo), UseInterceptors(TerryConfigInterceptor));
