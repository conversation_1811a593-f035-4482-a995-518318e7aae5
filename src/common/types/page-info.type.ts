export type PageAdminInfo = {
  title?: string;
  activeTab?: string;
  activeAdminTab?: string;
  activeAdminPage?: string;
};
export type PageUserMetaType = {
  meta_description?: string;
  meta_ogTitle?: string;
  meta_ogDescription?: string;
  meta_ogImage?: string;
  link_canonical?: string;
  link_prev?: string;
  link_next?: string;
};

export type PageUserInfo = {
  title?: string;
  activeTab?: string;
  meta?: PageUserMetaType;
  lightPage?: boolean;
  pageSpeed?: boolean;
  useLightHeader?: boolean;
  usingApp?: boolean;
  criticalCss?: boolean;
  contentClass?: string[];
  urlSuffix?: string;
  stationSlug?: string;
  usingCalendarApp?: boolean;
};
