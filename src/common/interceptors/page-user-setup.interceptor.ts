import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { map, Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';
import { PageUserInfo } from '../types/page-info.type';

@Injectable()
export class PageUserSetupInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    return next.handle().pipe(
      map((data) => {
        const pageInfo = this.reflector.get<PageUserInfo>('PageInfo', context.getHandler());
        data['meta'] = { ...pageInfo.meta, ...data['meta'] };

        if (!data['title']) {
          data['title'] = pageInfo.title;
        }

        data['pageSpeed'] = pageInfo.pageSpeed;
        data['useLightHeader'] = pageInfo.useLightHeader;
        data['usingApp'] = request.isUserApp();
        data['criticalcss'] = pageInfo.criticalCss;
        data['lightPage'] = pageInfo.lightPage;
        data['contentClass'] = pageInfo.contentClass;
        data['usingCalendarApp'] = pageInfo.usingCalendarApp;
        return data;
      }),
    );
  }
}
