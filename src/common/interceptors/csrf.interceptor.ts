// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { catchError, map, Observable, throwError } from 'rxjs';
import { Request } from 'express';
import * as Tokens from 'csrf';

export class CsrfInterceptor implements NestInterceptor {
  async intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> | Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    await this.createCsrf(request);
    return next.handle().pipe(
      catchError((err) => throwError(() => err)),
      map((data) => {
        return { _csrf: request.session?._csrf, ...data };
      }),
    );
  }

  async createCsrf(req: Request) {
    const csrfToken = new Tokens({});

    if (!req.session) {
      return Promise.resolve();
    }

    if (!req.session.csrfSecret) {
      req.session.csrfSecret = await csrfToken.secret();
    }

    if (!req.session._csrf) {
      req.session._csrf = csrfToken.create(req.session.csrfSecret);
    }

    return Promise.resolve();
  }
}
