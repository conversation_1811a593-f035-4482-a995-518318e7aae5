import { CallH<PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { catchError, map, Observable, throwError } from 'rxjs';
import { Reflector } from '@nestjs/core';

@Injectable()
export class TerryConfigInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  async intercept(context: ExecutionContext, next: CallHandler<any>): Promise<Observable<any>> {
    return next.handle().pipe(
      map((data) => {
        if (!data) {
          data = {};
        }

        const pageInfo = this.reflector.get('PageInfo', context.getHandler());
        data['activePage'] = pageInfo.activeAdminPage;
        data['activeTab'] = pageInfo.activeAdminTab;
        return data;
      }),
      catchError((err) => throwError(() => err)),
    );
  }
}
