import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { map, Observable } from 'rxjs';

export class RequestExtensions {
  static isUserAppExtension(req: Request): { (): boolean } {
    const userAppRegex = /(UserApp)/;
    return () => {
      const ua = req.headers['user-agent'];
      return userAppRegex.test(ua);
    };
  }

  static isMobileExtension(req: Request): { (): boolean } {
    const mobileRegex = /(iPhone|Android)/;
    return () => {
      const ua = req.headers['user-agent'];
      return mobileRegex.test(ua);
    };
  }

  static isMobileOrTabletExtension(req: Request): { (): boolean } {
    const mobileOrTabletRegex = /(iPhone|iPad|Android)/;
    return () => {
      const ua = req.headers['user-agent'];
      return mobileOrTabletRegex.test(ua);
    };
  }
}
export class UserRequestInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    request.isMobile = RequestExtensions.isMobileExtension(request);
    request.isMobileOrTablet = RequestExtensions.isMobileOrTabletExtension(request);
    request.isUserApp = RequestExtensions.isUserAppExtension(request);
    return next.handle().pipe(
      map((data) => {
        data['req'] = request;
        data['res'] = response;
        return data;
      }),
    );
  }
}
