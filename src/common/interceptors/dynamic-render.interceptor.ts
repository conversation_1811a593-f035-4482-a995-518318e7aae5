import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Response } from 'express';
import { Reflector } from '@nestjs/core';
import { DYNAMIC_RENDER_METADATA_KEY } from '@common/decorators/dynamic-render.decorator';

@Injectable()
export class DynamicRenderInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const isDynamicRender = this.reflector.get<boolean>(DYNAMIC_RENDER_METADATA_KEY, context.getHandler());

    if (!isDynamicRender) {
      return next.handle();
    }

    const res = context.switchToHttp().getResponse<Response>();

    return next.handle().pipe(
      map((data) => {
        if (data && data['RENDER_TEMPLATE']) {
          res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
          res.setHeader('Pragma', 'no-cache');
          res.setHeader('Expires', '0');
          res.render(data['RENDER_TEMPLATE'], data);
          return null;
        }
        return data;
      }),
    );
  }
}
