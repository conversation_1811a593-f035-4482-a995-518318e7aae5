// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-nocheck
import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { catchError, map, Observable, throwError } from 'rxjs';
import { Request } from 'express';
import * as Tokens from 'csrf';
import { InvalidCsrfTokenException } from '../exceptions/invalid-csrf-token.exception';

export class ValidCsrfInterceptor implements NestInterceptor {
  async intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> | Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    await this.valid(request, next);

    return next.handle().pipe(
      catchError((err) => throwError(err)),
      map((data) => ({ _csrf: request.session?._csrf, ...data })),
    );
  }

  async valid(req: Request, next) {
    const csrfToken = new Tokens({});

    const options = {
      excludedMethods: ['GET', 'HEAD', 'OPTIONS'],
    };

    if (options.excludedMethods.includes(req.method)) {
      return Promise.resolve();
    }

    const bodyToken = req.body?._csrf ?? false;

    const token =
      bodyToken || req.get('csrf-token') || req.get('xsrf-token') || req.get('x-csrf-token') || req.get('x-xsrf-token');

    if (!token || !csrfToken.verify(req.session.csrfSecret, token)) {
      throw new InvalidCsrfTokenException();
    }

    return Promise.resolve();
  }
}
