import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Py3Service } from '@vietnam/curama-py3-api';
import { map } from 'rxjs';

@Injectable()
export class RequireUserInterceptor implements NestInterceptor {
  constructor(private readonly py3Service: Py3Service) {}
  async intercept(context: ExecutionContext, next: CallHandler<any>) {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (!user) return next.handle();
    const headers = {
      'x-client-id': user?.clientId,
      'x-client-type': user?.accountType,
      'x-request-id': request.headers['x-request-id'],
      'x-api-fw-principal-key': process.env.API_FW_PRINCIPAL_KEY,
    };
    const userInfo = await this.py3Service.getUserInfo(user?.clientId, headers);
    return next.handle().pipe(
      map((data) => {
        if (!data) {
          data = {};
        }
        data['user'] = userInfo;
        return data;
      }),
    );
  }
}
