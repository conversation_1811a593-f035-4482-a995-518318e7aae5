export interface IPackage {
  id: string;
  code: string;
  name: string;
  fee: number;
  sort: number;
  status: number;
  outOfStock: boolean;
  packType: number;
  options: { [key: string]: any };
}

export interface IPackageAirconTrait {
  id: string;
  seoDescription: string;
  seoName: string;
  image: string;
  gtin13: string;
  model: string;
  modelId: string;
  label: number;
  tatamiSize: number;
  powerSupply: string;
  indoorUnitSize: string;
  outdoorUnitSize: string;
  coolingCapacity: number;
  heatingCapacity: number;
  apf: number;
  energyEfficiencyRating: number;
}

export interface IAirconCategoryTrait {
  category: string;
  brandEng: string;
  brandJp: string;
  manufacturerSite: string;
  series: string;
  seriesJp: string;
  seriesName: string;
  generalDescription: string;
  detailDescription: string;
  structuredDataDescription: string;
  seoDetailDescription: string;
  packages: IPackageAirconTrait[];
}

export interface IPackageBuiltInDishwasherTrait {
  id: string;
  manufacturer: string;
  code: string;
  depth: string;
  image: string;
}
