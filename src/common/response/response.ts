import { HttpStatus } from '@nestjs/common';
import { ErrorResponseInterface, SuccessResponseInterface } from '../interface/response';

export class HttpResponseFormatter {
  public static formatSuccessResponse(content: SuccessResponseInterface) {
    const { status, message, data, meta } = content;
    return {
      status,
      success: true,
      message,
      data,
      meta,
    };
  }

  public static formatErrorResponse(content: ErrorResponseInterface) {
    const { status, message, errors, meta } = content;
    return {
      meta,
      status,
      success: false,
      message,
      errors,
    };
  }
  public static responseOK = (content: SuccessResponseInterface) =>
    this.formatSuccessResponse({ ...content, status: HttpStatus.OK });
  public static responseBadRequest = (content: ErrorResponseInterface) =>
    this.formatErrorResponse({ ...content, status: HttpStatus.BAD_REQUEST });
  public static responseUnauthorized = (content: ErrorResponseInterface) =>
    this.formatErrorResponse({ ...content, status: HttpStatus.UNAUTHORIZED });
  public static responseForbidden = (content: ErrorResponseInterface) =>
    this.formatErrorResponse({ ...content, status: HttpStatus.FORBIDDEN });
  public static responseNotFound = (content: ErrorResponseInterface) =>
    this.formatErrorResponse({ ...content, status: HttpStatus.NOT_FOUND });
  public static responseConflict = (content: ErrorResponseInterface) =>
    this.formatErrorResponse({ ...content, status: HttpStatus.CONFLICT });
  public static responseInternalError = (content: ErrorResponseInterface) =>
    this.formatErrorResponse({ ...content, status: HttpStatus.INTERNAL_SERVER_ERROR });
}
export class ApiErrorResponse<T> {
  constructor(public errorCode: string, public message: string, public statusCode: number, public detail?: T[]) {}
}
