import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as customParseFormat from 'dayjs/plugin/customParseFormat';
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

export const JP_YYYYMD = 'YYYY年M月D日';
export const JP_YYYYMD_HHMM = 'YYYY年M月D日 HH:mm';
export const YYYYMD_HHMM = 'YYYY-MM-DD HH:mm';
export const YYYYMD = 'YYYY-MM-DD';
export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const TZ_JP = 'Asia/Tokyo';
export const JP_MMDD = 'MM月DD日';
export const JP_YYYYMMDD = 'YYYY年MM月DD日';
export const API_DATE_FORMAT_WITH_TZ = 'YYYY-MM-DDTHH:mm:ssZ';

export function formatToDateTime(date: Date | string, format = DATE_TIME_FORMAT): string {
  return dayjs(date).format(format);
}

export function formatToDateTimeWithTimezone(date: Date | string, format = DATE_TIME_FORMAT, tz = TZ_JP): string {
  return dayjs.utc(date).tz(tz).format(format);
}

export function getStartOfDay(date: Date | string, format = DATE_TIME_FORMAT): Date {
  return dayjs.utc(date).tz(TZ_JP).startOf('day').toDate();
}

export function getEndOfDay(date: Date | string, format = DATE_TIME_FORMAT): Date {
  return dayjs.utc(date).tz(TZ_JP).endOf('day').toDate();
}

export function formatDateWithComparisonYear(date: Date | string): string {
  if (!date) return '';
  const now = dayjs();
  const input = dayjs(date);

  return now.isSame(input, 'year') ? input.format(JP_MMDD) : input.format(JP_YYYYMMDD);
}

export function isValidDate(date: string, format: string, strict: boolean): boolean {
  return dayjs(date, format, strict).isValid();
}

export function getAllDatesBetween(startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): string[] {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const dates = [];
  for (let date = start; date.isBefore(end) || date.isSame(end); date = date.add(1, 'day')) {
    dates.push(date.format(YYYYMD));
  }
  return dates;
}
export function findMinValidDate(startDate: dayjs.Dayjs, endDate: dayjs.Dayjs, blockDates: string[]): string {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  for (let date = start; date.isBefore(end) || date.isSame(end); date = date.add(1, 'day')) {
    const formattedDate = date.format(YYYYMD);
    if (!blockDates.includes(formattedDate)) {
      return formattedDate;
    }
  }
  return null;
}
