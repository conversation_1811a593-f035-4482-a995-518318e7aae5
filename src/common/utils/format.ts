export function formatCurrency(value) {
  if (!value) {
    return '¥0';
  }
  return `¥${value.toLocaleString('ja-JP')}`;
}

export const convertStringToNumber = (value: string) => {
  const sanitizedStr = value.replace(/,/g, '');
  const trimmedStr = sanitizedStr.trim();
  const num = Number(trimmedStr);
  if (isNaN(num)) {
    return 0;
  }
  return num;
};

export const numberWithCommas = (x: number) => {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};
