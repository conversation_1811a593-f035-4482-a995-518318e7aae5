import { readFileSync, writeFileSync } from 'fs';
import * as path from 'path';

const getInfrastructureSettingsPath = () =>
  path.join(__dirname, '..', '..', 'configs/infra/infrastructureSettings.json');

const infrastructureMapping = {
  settings: {
    frontend: {
      staticDomain: process.env.INFRA_FRONTEND_STATIC_DOMAIN,
      externalStaticCdn: process.env.INFRA_FRONTEND_EXTERNAL_STATIC_CDN,
      imageDomain: process.env.INFRA_FRONTEND_IMAGE_DOMAIN,
    },
    googleAnalytics: {
      trackingId: process.env.INFRA_GOOGLE_ANALYTIC_TRACKING_ID,
    },
    googleTagManager: {
      containerId: process.env.INFRA_GOOGLE_TAGMANAGER_CONTAINER_ID,
    },
  },
};

export const overrideInfrastructureEnvironment = () => {
  const content = readFileSync(getInfrastructureSettingsPath(), 'utf8');
  const original = JSON.parse(content);
  const mergedValue = { ...original, ...infrastructureMapping };
  writeFileSync(getInfrastructureSettingsPath(), JSON.stringify(mergedValue, null, 2));
};
