import { sign } from 'jsonwebtoken';

function getSignature(imgPath: string) {
  const secret = process.env.PRIVATE_FILE_JWT_SECRET;
  const aud = process.env.PRIVATE_FILE_JWT_AUD;
  const exp = Number(process.env.PRIVATE_FILE_JWT_EXPIRES_IN) || 86400;
  return sign({ path: imgPath }, secret, { algorithm: 'HS256', audience: aud, expiresIn: exp });
}

export function getConfidentialPrivateFile(path, size = '0x0') {
  const confidentialPath = `confidential/${path}`;
  const secret = getSignature(confidentialPath);
  return `${process.env.PRIVATE_FILE_HANDLER_URL}/${confidentialPath}?size=${size}&s=${secret}`;
}
