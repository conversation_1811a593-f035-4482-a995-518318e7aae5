import { CODE_PREFIX } from '../enums/configuration';
import * as crypto from 'crypto';

export const checkAndExtractPackageCode = (str: string) => {
  const regex = new RegExp(`^${CODE_PREFIX.PACKAGE}(\\d{5})$`);
  const match = str.match(regex);
  if (match) {
    return Number(match[1]);
  }
  return null;
};

export const escapeLike = (value: string) => {
  return value.trim().replace(/[%_]/g, '\\$&');
};

export const generateToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

export const resolveAppUrl = () => {
  if (process.env.APP_ENV === 'local') {
    return `${process.env.APP_URL}:${process.env.APP_PORT}`;
  }
  return `${process.env.APP_URL}`;
};

export const renderTemplate = (renderTemplate: string, data: Record<string, any>) => {
  data['RENDER_TEMPLATE'] = renderTemplate;
  return data;
};
