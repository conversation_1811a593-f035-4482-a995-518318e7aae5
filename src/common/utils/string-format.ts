const escapeMap = {
  '&': '&amp;',
  '"': '&quot;',
  "'": '&#39;',
  '<': '&lt;',
  '>': '&gt;',
  '\\': '&#92;',
};

const escapeRegex = /[&"'<>\\]/g;
export const lookupEscape = (ch: string): string => {
  return escapeMap[ch];
};
export const escapeString = (str: string): string => {
  if (str === null || str === undefined) {
    return '';
  }
  return str.replace(escapeRegex, lookupEscape);
};
