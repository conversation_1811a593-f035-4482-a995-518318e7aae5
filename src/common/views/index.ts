import { AdditionalInfoTemplate } from '../enums/additional-information';

export const VIEW_TEMPLATES = {
  terry: {
    calendarBlockPage: 'admin/pages/calendar-block',
    packageList: 'admin/pages/package-list',
    packageDetail: 'admin/pages/package-detail',
    reservationOptionList: 'admin/pages/reservation-option-list',
    reservationOptionDetail: 'admin/pages/reservation-option-detail',
    reservationList: 'admin/pages/reservation-list',
    reservationDetail: 'admin/pages/reservation-detail',
  },
  user: {
    airconInstall: {
      landingPage: 'user/pages/aircon-install/landing',
      packageDetail: 'user/pages/aircon-install/package-detail',
      newReservation: 'user/pages/aircon-install/new-reservation',
      confirmReservation: 'user/pages/aircon-install/reservation-confirm',
      thank: 'user/pages/aircon-install/reservation-thank',
    },
    waterHeater: {
      landingPage: 'user/pages/water-heater/landing',
      newReservation: 'user/pages/water-heater/new-reservation',
      confirmReservation: 'user/pages/water-heater/reservation-confirm',
      thank: 'user/pages/water-heater/reservation-thank',
    },
    dishwasher: {
      landingPage: 'user/pages/dishwasher/landing',
      newReservation: 'user/pages/dishwasher/new-reservation',
      confirmReservation: 'user/pages/dishwasher/reservation-confirm',
      thank: 'user/pages/dishwasher/reservation-thank',
    },
    addingInformation: {
      hotWaterEnergySaving2025: {
        uploadPage: 'user/pages/additional-information/hotWaterEnergySaving2025/upload-page',
        thankPage: 'user/pages/additional-information/hotWaterEnergySaving2025/thank-page',
      },
      tokyoZeroEmissionPoint: {
        uploadPage: 'user/pages/additional-information/tokyoZeroEmissionPoint/upload-page',
        thankPage: 'user/pages/additional-information/tokyoZeroEmissionPoint/thank-page',
      },
      countertopDishwasher: {
        uploadPage: 'user/pages/additional-information/countertopDishwasher/upload-page',
        thankPage: 'user/pages/additional-information/countertopDishwasher/thank-page',
      },
      existingEcoCuteInfoForm: {
        uploadPage: 'user/pages/additional-information/existingEcoCuteInfoForm/upload-page',
        confirmPage: 'user/pages/additional-information/existingEcoCuteInfoForm/confirm-page',
        thankPage: 'user/pages/additional-information/existingEcoCuteInfoForm/thank-page',
      },
    },
  },
};

export const AdditionalInfoViewMapping = {
  [AdditionalInfoTemplate.HotWaterEnergySaving2025]: {
    uploadPage: VIEW_TEMPLATES.user.addingInformation.hotWaterEnergySaving2025.uploadPage,
    thankPage: VIEW_TEMPLATES.user.addingInformation.hotWaterEnergySaving2025.thankPage,
  },
  [AdditionalInfoTemplate.TokyoZeroEmissionPoint]: {
    uploadPage: VIEW_TEMPLATES.user.addingInformation.tokyoZeroEmissionPoint.uploadPage,
    thankPage: VIEW_TEMPLATES.user.addingInformation.tokyoZeroEmissionPoint.thankPage,
  },
  [AdditionalInfoTemplate.CountertopDishwasher]: {
    uploadPage: VIEW_TEMPLATES.user.addingInformation.countertopDishwasher.uploadPage,
    thankPage: VIEW_TEMPLATES.user.addingInformation.countertopDishwasher.thankPage,
  },
  [AdditionalInfoTemplate.ExistingEcoCuteInfoForm]: {
    uploadPage: VIEW_TEMPLATES.user.addingInformation.existingEcoCuteInfoForm.uploadPage,
    thankPage: VIEW_TEMPLATES.user.addingInformation.existingEcoCuteInfoForm.thankPage,
  },
};
