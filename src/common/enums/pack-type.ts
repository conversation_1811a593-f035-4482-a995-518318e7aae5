export enum PackType {
  AI<PERSON>ON_INSTALL = 1,
  WATER_HEATER = 2,
  DISHWASHER = 3,
}

export const PackageTypeMapping = {
  [PackType.AIRCON_INSTALL]: 'Aircon Install Pack',
  [PackType.WATER_HEATER]: 'Water Heater Pack',
  [PackType.DISHWASHER]: 'Dishwasher Pack',
};

export const PackageTypeTraitFileMapping = {
  [PackType.AIRCON_INSTALL]: 'assets/packages/aircon.json',
};

export const PackageTypeRouteMapping = {
  [PackType.AIRCON_INSTALL]: '/aircon-install-pack/',
  [PackType.WATER_HEATER]: '/water-heater-pack/',
  [PackType.DISHWASHER]: '/countertop-dishwasher-pack/',
};
