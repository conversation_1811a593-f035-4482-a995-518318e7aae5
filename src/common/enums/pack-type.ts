export enum PackType {
  AI<PERSON>ON_INSTALL = 1,
  WATER_HEATER = 2,
  <PERSON>ISHWASHER = 3,
  BUILD_IN_DISHWASHER = 4,
}

export const PackageTypeMapping = {
  [PackType.AIRCON_INSTALL]: 'Aircon Install Pack',
  [PackType.WATER_HEATER]: 'Water Heater Pack',
  [PackType.DISHWASHER]: 'Dishwasher Pack',
  [PackType.BUILD_IN_DISHWASHER]: 'Built-in Dishwasher Pack',
};

export const PackageTypeTraitFileMapping = {
  [PackType.AIRCON_INSTALL]: 'assets/packages/aircon.json',
  [PackType.BUILD_IN_DISHWASHER]: 'assets/packages/built-in-dishwasher.json',
};

export const PackageTypeRouteMapping = {
  [PackType.AIRCON_INSTALL]: '/aircon-install-pack/',
  [PackType.WATER_HEATER]: '/water-heater-pack/',
  [PackType.DISHWASHER]: '/countertop-dishwasher-pack/',
  [PackType.BUILD_IN_DISHWASHER]: '/built-in-dishwasher-pack/',
};
