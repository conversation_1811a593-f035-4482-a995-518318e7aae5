import { PackType } from '../enums/pack-type';

export const EMAIL_TEMPLATE_NAME = {
  AIRCON_INSTALL: 'labo_install_pack_new_reservation',
  WATER_HEATER: 'water_heater_pack_new_reservation',
  DISHWASHER: 'dishwasher_pack_new_reservation',
};

export const EmailTemplateMapping = {
  [PackType.AIRCON_INSTALL]: EMAIL_TEMPLATE_NAME.AIRCON_INSTALL,
  [PackType.WATER_HEATER]: EMAIL_TEMPLATE_NAME.WATER_HEATER,
  [PackType.DISHWASHER]: EMAIL_TEMPLATE_NAME.DISHWASHER,
};

export const SpreadSheetIdMapping = {
  [PackType.AIRCON_INSTALL]: process.env.GOOGLE_SPREADSHEET_ID,
  [PackType.WATER_HEATER]: process.env.GOOGLE_SPREADSHEET_WATER_HEATER_ID,
  [PackType.DISHWASHER]: process.env.GOOGLE_SPREADSHEET_DISHWASHER_ID,
};

export const GOOGLE_RESERVATION_SPREADSHEET = {
  AIRCON: {
    WORKSHEET_NAME: '注文データ',
    START_LINE: 'A2',
  },
  WATER_HEATER: {
    WORKSHEET_NAME: '注文データ',
    START_LINE: 'A2',
  },
  DISHWASHER: {
    WORKSHEET_NAME: '注文データ',
    START_LINE: 'A2',
  },
};

export const SpreadSheetMetadataMapping = {
  [PackType.AIRCON_INSTALL]: `${GOOGLE_RESERVATION_SPREADSHEET.AIRCON.WORKSHEET_NAME}!${GOOGLE_RESERVATION_SPREADSHEET.AIRCON.START_LINE}`,
  [PackType.WATER_HEATER]: `${GOOGLE_RESERVATION_SPREADSHEET.WATER_HEATER.WORKSHEET_NAME}!${GOOGLE_RESERVATION_SPREADSHEET.WATER_HEATER.START_LINE}`,
  [PackType.DISHWASHER]: `${GOOGLE_RESERVATION_SPREADSHEET.DISHWASHER.WORKSHEET_NAME}!${GOOGLE_RESERVATION_SPREADSHEET.DISHWASHER.START_LINE}`,
};
