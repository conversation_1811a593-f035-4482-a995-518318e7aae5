export const SORT = {
  ASCENDING: 1,
  DESCENDING: 2,
};

export const PAGINATOR = {
  TERRY: 10,
  SHOP: 10,
};

export const CACHE_STORE = {
  PACKAGES: {
    key: 'Packages',
    ttl: 60 * 60 * 24,
  },
  RESERVATION_OPTIONS: {
    key: 'ReservationOptions',
    ttl: 60 * 60 * 24,
  },
  CONFIGURATION: {
    key: 'Configuration',
    ttl: 60 * 60 * 24,
  },
  RESERVATION_IDEMPOTENCY_KEY: {
    key: 'ReservationIdempotencyKey',
    ttl: 30,
  },
};
export const LABO_INSTALL_PACK_REDIRECT_URL_KEY = 'laboInstallPackRedirectUrl';
export const REDIRECT_TO_USER = 'REDIRECT_TO_USER';

export const ERROR_MESSAGE = {
  INVALID_PACKAGE: '無効なパッケージ。',
  INVALID_PACKAGE_QUANTITY: '無効なパッケージ数量。',
  INVALID_DUPLICATE_PACKAGE: '重複パッケージ。',
  INVALID_OPTION: '無効なオプション。',
  PACKAGE_OUT_OF_STOCK: '製品は在庫切れです。',
};

export const LIMIT_5_MB = 1024 * 1024 * 5;
