import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';
import { PackageEntity } from './package.entity';
import { ReservationEntity } from './reservation.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';

@Entity(ENTITIES_ENUM.Reservations_Packages)
@Index('IDX_Reservations_PackageId', ['packageId'])
export class ReservationsPackagesEntity {
  @PrimaryColumn({ name: 'package_id', type: 'uuid' })
  packageId: string;

  @PrimaryColumn({ name: 'reservation_id', type: 'uuid' })
  reservationId: string;

  @Column({ name: 'quantity', type: 'int' })
  quantity: number;

  @Column({ name: 'pack_price', type: 'int' })
  packPrice: number;

  @Column({ name: 'pack_options', type: 'jsonb', nullable: true })
  packOptions: Record<string, any>;

  @ManyToOne(() => PackageEntity, (pack) => pack.reservationsPackages)
  @JoinColumn({
    name: 'package_id',
    referencedColumnName: 'id',
    foreignKeyConstraintName: 'FK_Reservations_Packages_Packages',
  })
  package: PackageEntity;

  @ManyToOne(() => ReservationEntity, (reservation) => reservation.reservationsPackages)
  @JoinColumn({
    name: 'reservation_id',
    referencedColumnName: 'id',
    foreignKeyConstraintName: 'FK_Reservations_Packages_Reservations',
  })
  reservation: ReservationEntity;
}
