import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ReservationEntity } from './reservation.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { AdditionalInfoStatus, AdditionalInfoTemplate } from '../common/enums/additional-information';
import { AdditionActivityEntity } from './addition-activity.entity';
import { AdditionAttemptEntity } from './addition-attempt.entity';

@Entity(ENTITIES_ENUM.AdditionalInformation)
export class AdditionalInformationEntity extends AbstractEntity {
  @Column({ name: 'template_type', type: 'smallint' })
  templateType: AdditionalInfoTemplate;

  @Column({ name: 'status', type: 'smallint', default: AdditionalInfoStatus.NOT_UPLOADED })
  status: number;

  @Column({ name: 'verification_token', type: 'varchar', length: '64' })
  verificationToken: string;

  @Column({ name: 'is_enable', type: 'boolean', default: true })
  isEnable: boolean;

  @Column({ name: 'reservation_id', type: 'uuid' })
  reservationId: string;

  @ManyToOne(() => ReservationEntity, (reservation) => reservation.additionalInformation)
  @JoinColumn({ name: 'reservation_id', referencedColumnName: 'id' })
  reservation: ReservationEntity;

  @OneToMany(() => AdditionAttemptEntity, (attempt) => attempt.additionalInformation)
  additionAttempts: AdditionAttemptEntity[];

  @OneToMany(() => AdditionActivityEntity, (activity) => activity.additionalInformation)
  activities: AdditionActivityEntity[];
}
