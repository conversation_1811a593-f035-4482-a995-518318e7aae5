import { DocumentType } from '@src/common/enums/documents';
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { AdditionAttemptEntity } from './addition-attempt.entity';

@Entity(ENTITIES_ENUM.Documents)
export class DocumentEntity extends AbstractEntity {
  @Column({ name: 'path' })
  path: string;

  @Column({ name: 'name', type: 'varchar', length: '255' })
  name: string;

  @Column({ name: 'document_type', type: 'smallint' })
  documentType: DocumentType;

  @Column({ name: 'addition_attempt_id', type: 'uuid' })
  additionAttemptId: string;

  @ManyToOne(() => AdditionAttemptEntity, (attempt) => attempt.documents)
  @JoinColumn({ name: 'addition_attempt_id', referencedColumnName: 'id' })
  additionAttempt: AdditionAttemptEntity;
}
