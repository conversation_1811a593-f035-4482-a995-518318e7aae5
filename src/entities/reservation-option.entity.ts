import { Column, Entity, Unique } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';

@Entity(ENTITIES_ENUM.ReservationOptions)
@Unique(['name', 'packType'])
export class ReservationOptionEntity extends AbstractEntity {
  @Column({ name: 'name', type: 'varchar', length: '255' })
  name: string;

  @Column({ name: 'title', type: 'varchar', length: '255' })
  title: string;

  @Column({ name: 'code', type: 'varchar', length: '50', unique: true })
  code: string;

  @Column({ name: 'type', type: 'varchar', length: '50' })
  type: string;

  @Column({ name: 'has_price', type: 'boolean' })
  hasPrice: boolean;

  @Column({ name: 'detail', type: 'jsonb' })
  detail: any;

  @Column({ name: 'enabled', type: 'boolean' })
  enabled: boolean;

  @Column({ name: 'sort', type: 'smallint', default: 1 })
  sort: number;

  @Column({ name: 'pack_type', type: 'smallint', default: PackType.AIRCON_INSTALL })
  packType: PackType;
}
