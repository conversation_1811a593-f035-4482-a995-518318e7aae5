import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { AdditionalInformationEntity } from './additional-information.entity';
import { DocumentEntity } from './documents.entity';

@Entity(ENTITIES_ENUM.AdditionAttempts)
export class AdditionAttemptEntity extends AbstractEntity {
  @Column({ type: 'jsonb', nullable: true })
  detail: Record<string, any>;

  @Column({ name: 'is_deleted', type: 'boolean', default: false })
  isDeleted: boolean;

  @Column({ name: 'additional_information_id', type: 'uuid' })
  additionalInformationId: string;

  @ManyToOne(() => AdditionalInformationEntity, (additionalInfo) => additionalInfo.additionAttempts)
  @JoinColumn({ name: 'additional_information_id', referencedColumnName: 'id' })
  additionalInformation: AdditionalInformationEntity;

  @OneToMany(() => DocumentEntity, (document) => document.additionAttempt)
  documents: DocumentEntity[];
}
