import { Column, Entity, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ReservationsPackagesEntity } from './reservations-packages.entity';
import { ReservationStatus } from '../common/enums/reservation';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';
import { AdditionalInformationEntity } from './additional-information.entity';

@Entity(ENTITIES_ENUM.Reservations)
export class ReservationEntity extends AbstractEntity {
  @Column({ name: 'code', type: 'varchar', length: '50', unique: true })
  code: string;

  @Column({ name: 'status', type: 'smallint', default: ReservationStatus.OPEN })
  status: ReservationStatus;

  @Column({ name: 'user_id', type: 'varchar', length: '50' })
  userId: string;

  @Column({ name: 'working_date_1', type: 'date' })
  workingDate1: string;

  @Column({ name: 'working_date_2', type: 'date', nullable: true })
  workingDate2: string;

  @Column({ name: 'email', type: 'varchar', length: '255' })
  email: string;

  @Column({ name: 'last_name', type: 'varchar', length: '255' })
  lastname: string;

  @Column({ name: 'first_name', type: 'varchar', length: '255' })
  firstname: string;

  @Column({ name: 'phone_number', type: 'varchar', length: '255' })
  phoneNumber: string;

  @Column({ name: 'postal_code', type: 'varchar', length: '255' })
  postalcode: string;

  @Column({ name: 'prefecture', type: 'varchar', length: '255' })
  prefecture: string;

  @Column({ name: 'city', type: 'varchar', length: '255' })
  city: string;

  @Column({ name: 'address', type: 'varchar', length: '255' })
  address: string;

  @Column({ type: 'jsonb', nullable: true })
  options: Record<string, any>;

  @Column({ name: 'total_fee', type: 'int' })
  totalFee: number;

  @Column({ name: 'total_discount', type: 'int' })
  totalDiscount: number;

  @Column({ name: 'pack_type', type: 'smallint', default: PackType.AIRCON_INSTALL })
  packType: PackType;

  @OneToMany(() => ReservationsPackagesEntity, (reservationsPackages) => reservationsPackages.reservation)
  reservationsPackages: ReservationsPackagesEntity[];

  @OneToMany(() => AdditionalInformationEntity, (info) => info.reservation)
  additionalInformation: AdditionalInformationEntity[];
}
