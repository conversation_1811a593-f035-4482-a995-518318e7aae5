import { Column, Entity, Unique } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';

@Entity(ENTITIES_ENUM.Configurations)
@Unique(['configKey', 'packType'])
export class ConfigurationEntity extends AbstractEntity {
  @Column({ name: 'config_key', type: 'varchar', length: '100' })
  configKey: string;

  @Column({ name: 'value', type: 'varchar' })
  value: string;

  @Column({ name: 'pack_type', type: 'smallint', default: PackType.AIRCON_INSTALL })
  packType: PackType;
}
