import { Column, Entity, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ReservationsPackagesEntity } from './reservations-packages.entity';
import { PackageStatus } from '../common/enums/package';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';

@Entity(ENTITIES_ENUM.Packages)
export class PackageEntity extends AbstractEntity {
  @Column({ name: 'code', type: 'varchar', length: '50', unique: true })
  code: string;

  @Column({ name: 'name', type: 'varchar', length: '255', unique: true })
  name: string;

  @Column({ name: 'fee', type: 'int' })
  fee: number;

  @Column({ name: 'sort', type: 'smallint', default: 1 })
  sort: number;

  @Column({ name: 'status', type: 'smallint' })
  status: PackageStatus;

  @Column({ name: 'out_of_stock', type: 'boolean', default: false })
  outOfStock: boolean;

  @Column({ name: 'pack_type', type: 'smallint', default: PackType.AIRCON_INSTALL })
  packType: PackType;

  @OneToMany(() => ReservationsPackagesEntity, (reservationsPackages) => reservationsPackages.package)
  reservationsPackages: ReservationsPackagesEntity[];
}
