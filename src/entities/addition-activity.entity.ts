import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { AdditionActivityType } from '../common/enums/additional-information';
import { AdditionalInformationEntity } from './additional-information.entity';

@Entity(ENTITIES_ENUM.AdditionActivities)
export class AdditionActivityEntity extends AbstractEntity {
  @Column({ name: 'type', type: 'smallint' })
  type: AdditionActivityType;

  @Column({ name: 'addition_attempt_id', type: 'uuid', nullable: true })
  additionAttemptId: string;

  @Column({ name: 'additional_information_id', type: 'uuid' })
  additionalInformationId: string;

  @ManyToOne(() => AdditionalInformationEntity, (addition) => addition.activities)
  @JoinColumn({ name: 'additional_information_id', referencedColumnName: 'id' })
  additionalInformation: AdditionalInformationEntity;
}
