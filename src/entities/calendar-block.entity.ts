import { Entity, PrimaryColumn } from 'typeorm';
import { ENTITIES_ENUM } from '../common/enums/entities';
import { PackType } from '../common/enums/pack-type';

@Entity(ENTITIES_ENUM.CalendarBlocks)
export class CalendarBlockEntity {
  @PrimaryColumn({ name: 'block_date', type: 'date' })
  blockDate: string;

  @PrimaryColumn({ name: 'pack_type', type: 'smallint', default: PackType.AIRCON_INSTALL })
  packType: PackType;
}
