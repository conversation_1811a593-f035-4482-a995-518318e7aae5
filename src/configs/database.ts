import { registerAs } from '@nestjs/config';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export default registerAs(
  'database',
  (): PostgresConnectionOptions => ({
    type: 'postgres',
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    username: process.env.DATABASE_USERNAME || 'example',
    password: process.env.DATABASE_PASSWORD || 'example',
    database: process.env.DATABASE_NAME || 'example',
    schema: process.env.DATABASE_SCHEMA || 'public',
    logging: process.env.APP_DEBUG === 'true' && process.env.APP_ENV === 'local',
    entities: [__dirname + '/../entities/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../migrations/**/*{.ts,.js}'],
    migrationsRun: true,
    ...(process.env.DATABASE_POOL_SIZE && { poolSize: parseInt(process.env.DATABASE_POOL_SIZE) }),
  }),
);
