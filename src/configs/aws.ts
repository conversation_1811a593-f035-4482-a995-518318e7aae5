import { registerAs } from '@nestjs/config';

export interface CognitoAuthenticationConfig {
  domain: string;
  oauth2Url: string;
  tokenExchangeURL: string;
  clientId: string;
  clientSecret: string;
  userPoolId: string;
  payload: {
    client_id: string;
    response_type: string;
    scope: string;
    redirect_uri: string;
  };
}

export interface VerifiedPermission {
  policyStoreId: string;
  userPoolId?: string;
  namespace?: string;
}

export interface AWSConfiguration {
  region?: string;
  accessKey?: string;
  secretKey?: string;
  cognito?: CognitoAuthenticationConfig;
  verifiedPermission?: VerifiedPermission;
}

export const getCognitoOAuth2Config = (): CognitoAuthenticationConfig => {
  const domain = process.env.COGNITO_OAUTH2_DOMAIN;
  const clientId = process.env.COGNITO_OAUTH2_CLIENT_ID;
  const clientSecret = process.env.COGNITO_OAUTH2_CLIENT_SECRET;
  const scopes = process.env.COGNITO_OAUTH2_SCOPES?.split(',');
  const userPoolId = process.env.COGNITO_USER_POOL_ID;
  const redirectUri =
    process.env.COGNITO_OAUTH2_REDIRECT_URI || `${process.env.APP_URL}/admin/terry-account/authenticate`;
  const responseType = 'code';
  const oauth2Url = `${domain}/oauth2/authorize`;
  const tokenExchangeURL = `${domain}/oauth2/token`;
  const payload = {
    client_id: clientId,
    response_type: responseType,
    scope: scopes?.join(' '),
    redirect_uri: redirectUri,
  };

  return {
    domain,
    userPoolId,
    oauth2Url,
    tokenExchangeURL,
    clientId,
    clientSecret,
    payload,
  };
};

const getAwsVerifiedPermissionConfig = (): VerifiedPermission => {
  const policyStoreId = process.env.VERIFIED_PERMISSION_POLICY_STORE_ID;
  const userPoolId = process.env.VERIFIED_PERMISSION_USER_POOL_ID;
  const namespace = process.env.VERIFIED_PERMISSION_NAMESPACE || 'Curama';
  return {
    policyStoreId,
    userPoolId,
    namespace,
  };
};

export const getAwsConfig = (): AWSConfiguration => ({
  accessKey: process.env.AWS_ACCESS_KEY,
  secretKey: process.env.AWS_SECRET_KEY,
  region: process.env.AWS_REGION,
  cognito: getCognitoOAuth2Config(),
  verifiedPermission: getAwsVerifiedPermissionConfig(),
});

export default registerAs('aws', (): AWSConfiguration => getAwsConfig());
