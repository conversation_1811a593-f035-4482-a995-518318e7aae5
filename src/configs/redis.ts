import { registerAs } from '@nestjs/config';

export interface RedisConfig {
  host: string;
  port: number;
  db: number;
  password: string;
  keyPrefix?: string;
}

export default registerAs('redis-cache', (): RedisConfig => {
  return {
    host: process.env.REDIS_CACHE_HOST || 'localhost',
    port: parseInt(process.env.REDIS_CACHE_PORT || '6379'),
    db: parseInt(process.env.REDIS_CACHE_DB || '0'),
    password: process.env.REDIS_CACHE_PASSWORD || '',
  };
});
