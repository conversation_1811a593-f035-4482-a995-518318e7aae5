import { registerAs } from '@nestjs/config';

export type HttpConfig = {
  httpTimeout: number | string;
  httpMaxRetry: number | string;
  statusRetry: Array<number | string>;
  retryEndPoints: Array<number | string>;
};

export default registerAs(
  'http',
  (): HttpConfig => ({
    httpTimeout: +process.env.HTTP_TIMEOUT || 60000,
    httpMaxRetry: +process.env.HTTP_MAX_RETRY || 3,
    statusRetry: process.env.HTTP_STATUS_RETRY?.trim() ? process.env.HTTP_STATUS_RETRY?.split(',') : [],
    retryEndPoints: process.env.HTTP_RETRY_ENDPOINT?.trim() ? process.env.HTTP_RETRY_ENDPOINT?.split(',') : [],
  }),
);
