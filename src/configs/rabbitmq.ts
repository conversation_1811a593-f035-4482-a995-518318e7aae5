import { registerAs } from '@nestjs/config';

export interface RabbitMQConfig {
  host: string;
  port: string;
  username: string;
  password: string;
  path?: string;
}

export default registerAs(
  'rabbitmq',
  (): RabbitMQConfig => ({
    host: process.env.RABBITMQ_HOST || 'localhost',
    port: process.env.RABBITMQ_PORT || '5672',
    username: process.env.RABBITMQ_USERNAME || 'guest',
    password: process.env.RABBITMQ_PASSWORD || 'guest',
    path: process.env.RABBITMQ_PATH || '/',
  }),
);
