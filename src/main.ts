/* eslint-disable @typescript-eslint/no-var-requires */
import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { Except<PERSON><PERSON><PERSON><PERSON>, WinstonLoggerService } from '@vietnam/cnga-middleware';
import * as path from 'path';
import { NestExpressApplication } from '@nestjs/platform-express';
import { ValidationPipe } from '@nestjs/common';
import { useCookieParser } from '@vietnam/cnga-middleware/dist/terry-auth/sessions';
import { overrideInfrastructureEnvironment } from './common/utils/deploy';
import { CsrfInterceptor } from './common/interceptors/csrf.interceptor';
import { ValidCsrfInterceptor } from './common/interceptors/valid-csrf.interceptor';
import { configSession } from './providers/session.provider';
import { Server } from 'http';
import { DynamicRenderInterceptor } from './common/interceptors/dynamic-render.interceptor';

async function bootstrap() {
  // when in .env file has SHOULD_REPLACE_INFRA_SETTING=true
  // then override infrastructure environment
  if (process.env.SHOULD_REPLACE_INFRA_SETTING === 'true') {
    overrideInfrastructureEnvironment();
  }

  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  );

  app.useStaticAssets(path.join(__dirname, '..', 'public'), {
    prefix: process.env.APP_STATIC_PREFIX ? `/${process.env.APP_STATIC_PREFIX}/` : '',
  });
  app.useGlobalInterceptors(new CsrfInterceptor(), new ValidCsrfInterceptor());
  app.useGlobalInterceptors(
    new DynamicRenderInterceptor(new Reflector()),
    new CsrfInterceptor(),
    new ValidCsrfInterceptor(),
  );

  app.useLogger(app.get(WinstonLoggerService));
  app.useGlobalFilters(new ExceptionHandler());
  app.enableCors();
  useCookieParser(app);
  configSession(app);
  const server: Server = await app.listen(process.env.APP_PORT || 3000);
  server.keepAliveTimeout = process.env.SERVER_KEEP_ALIVE_TIMEOUT
    ? Number(process.env.SERVER_KEEP_ALIVE_TIMEOUT)
    : 65000;
  server.headersTimeout = process.env.SERVER_HEADERS_TIMEOUT ? Number(process.env.SERVER_HEADERS_TIMEOUT) : 70000;
  console.info(`App is running on port ${process.env.APP_PORT || 3000}`);
}

bootstrap();
