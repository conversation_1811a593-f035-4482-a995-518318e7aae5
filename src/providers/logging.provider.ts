import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggingConfig } from '../common/interface/logging-config';
import { WinstonLoggerModule } from '@vietnam/cnga-middleware';

export class LoggingProvider extends ServiceProvider {
  static register(): DynamicModule {
    return WinstonLoggerModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        return {
          logChannel: configService.get<LoggingConfig>('logging').logChannel,
          logLevel: configService.get<LoggingConfig>('logging').logLevel,
        };
      },
      inject: [ConfigService],
    });
  }
}
