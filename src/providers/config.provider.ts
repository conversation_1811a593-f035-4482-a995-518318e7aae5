import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import database from '../configs/database';
import rabbitmq from '../configs/rabbitmq';
import logging from '../configs/logging';
import py3 from '../configs/py3';
import http from '../configs/http';
import redis from '../configs/redis';

export class ConfigProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return ConfigModule.forRoot({
      isGlobal: true,
      load: [database, rabbitmq, logging, py3, http, redis],
    });
  }
}
