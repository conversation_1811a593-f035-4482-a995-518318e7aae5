import { INestApplication } from '@nestjs/common';
import { AUTH_STORE, SessionSerializer } from '@vietnam/cnga-middleware';
import RedisStore from 'connect-redis';
import * as session from 'express-session';
import { Redis } from 'ioredis';
import * as passport from 'passport';
import flash = require('connect-flash');

export function configSession(app: INestApplication) {
  const authStore = app.get<Redis>(AUTH_STORE);
  app.use(
    session({
      store: new RedisStore({ client: authStore }),
      secret: process.env.SESSION_KEY,
      resave: false,
      saveUninitialized: false,
      cookie: {
        // maxAge: 1 day in milliseconds
        maxAge: 24 * 60 * 60 * 1000,
      },
    }),
  );

  app.use(flash());

  const sessionSerializer = app.get(SessionSerializer);
  passport.deserializeUser(sessionSerializer.deserializeUser);

  app.use(passport.initialize());
  app.use(passport.session());
}
