import { DynamicModule } from '@nestjs/common';
import * as path from 'path';
import { NunjucksModule } from '@vietnam/cnga-frontend';

export class ViewProvider {
  public static register(): DynamicModule {
    return NunjucksModule.forRoot({
      viewPaths: [path.join(__dirname, '..', '..', 'views')],
      infrastructureSettings: path.join(__dirname, '..', 'configs/infra/infrastructureSettings.json'),
      staticVersions: path.join(__dirname, '..', 'configs/infra/staticVersions.json'),
    });
  }
}
