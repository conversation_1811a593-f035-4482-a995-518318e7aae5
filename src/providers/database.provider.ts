import { DynamicModule, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { DatabaseConfig } from '@src/common/interface/database-config';
import { ServiceProvider } from './provider';

@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
  constructor(private readonly configService: ConfigService) {}

  createTypeOrmOptions(connectionName?: string): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    const databaseConfig: DatabaseConfig = this.configService.get<DatabaseConfig>('database');
    return {
      type: databaseConfig.type,
      ...databaseConfig,
      autoLoadEntities: true,
    };
  }
}

export class DatabaseProvider extends ServiceProvider {
  public static register(): DynamicModule {
    return TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
    });
  }
}
