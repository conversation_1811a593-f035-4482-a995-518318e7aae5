import { ServiceProvider } from './provider';
import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RabbitMQConfig } from '../common/interface/rabbitmq-config';
import { CuramaWorkerModule } from '@vietnam/curama-worker';

export class <PERSON>ue<PERSON>rovider extends ServiceProvider {
  public static register(): DynamicModule {
    return CuramaWorkerModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        const rabbitmqConfig = configService.get<RabbitMQConfig>('rabbitmq');
        const rabbitmqScheme = process.env.RABBITMQ_SCHEME ? process.env.RABBITMQ_SCHEME : 'amqps';

        const url = new URL(`${rabbitmqScheme}://${rabbitmqConfig.host}:${rabbitmqConfig.port}`);
        url.pathname = rabbitmqConfig.path;
        url.username = rabbitmqConfig.username;
        url.password = encodeURIComponent(rabbitmqConfig.password);

        return {
          uri: url.toString(),
          exchanges: [
            {
              name: 'taskManager',
              type: 'direct',
            },
          ],
        };
      },
      inject: [ConfigService],
    });
  }
}
