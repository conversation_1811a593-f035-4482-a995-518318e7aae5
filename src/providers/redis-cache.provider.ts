import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { RedisConfig } from '../configs/redis';

export class RedisCacheProvider {
  public static register(): DynamicModule {
    return RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        const config = configService.get<RedisConfig>('redis-cache');
        return {
          config: {
            ...config,
          },
        };
      },
      inject: [ConfigService],
    });
  }
}
