import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { AppController } from './app.controller';
import { TerryModule } from './modules/terry/terry.module';
import { AuthModule, RequestContextMiddleware, RequestLoggingMiddleware } from '@vietnam/cnga-middleware';
import { ConfigProvider } from './providers/config.provider';
import { LoggingProvider } from './providers/logging.provider';
import { ViewProvider } from './providers/view.provider';
import { DatabaseProvider } from './providers/database.provider';
import { QueueProvider } from './providers/queue.provider';
import { RedisAuthProvider } from './providers/redis-auth.provider';
import { UserModule } from './modules/user/user.module';
import { RedisCacheProvider } from './providers/redis-cache.provider';

@Module({
  imports: [
    ConfigProvider.register(),
    LoggingProvider.register(),
    ViewProvider.register(),
    DatabaseProvider.register(),
    QueueProvider.register(),
    RedisAuthProvider.register(),
    RedisCacheProvider.register(),
    TerryModule,
    UserModule,
    AuthModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestContextMiddleware, RequestLoggingMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });
  }
}
