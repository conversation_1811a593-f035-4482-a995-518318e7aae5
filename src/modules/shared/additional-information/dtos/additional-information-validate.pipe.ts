import { ArgumentMetadata, Injectable, PipeTransform, BadRequestException } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { AdditionalInformationHotWaterEnergySaving2025Dto } from './additional-information-hot-water-energy-saving-2025.dto';
import { AdditionInformationCountertopDishwasherDto } from './addition-information-countertop-dishwasher.dto';
import { AdditionalInformationExistingEcoCuteInfoDto } from './additional-information-existing-ecocute-info.dto';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';

@Injectable()
export class AdditionalInformationValidationPipe implements PipeTransform {
  async transform(value: any, metadata: ArgumentMetadata) {
    if (
      !value ||
      !value.packType ||
      (typeof value.packType === 'string' && isNaN(value.packType)) ||
      !value.templateType ||
      (typeof value.templateType === 'string' && isNaN(value.templateType))
    ) {
      throw new BadRequestException('PackType field is required');
    }

    let dtoClass;
    switch (Number(value.templateType)) {
      case AdditionalInfoTemplate.HotWaterEnergySaving2025:
        dtoClass = AdditionalInformationHotWaterEnergySaving2025Dto;
        break;
      case AdditionalInfoTemplate.TokyoZeroEmissionPoint:
        return value;
      case AdditionalInfoTemplate.CountertopDishwasher:
        dtoClass = AdditionInformationCountertopDishwasherDto;
        break;
      case AdditionalInfoTemplate.ExistingEcoCuteInfoForm:
        dtoClass = AdditionalInformationExistingEcoCuteInfoDto;
        break;
      default:
        throw new BadRequestException('Invalid type');
    }

    const dtoInstance = plainToInstance(dtoClass, value);
    const errors = await validate(dtoInstance);

    if (errors.length > 0) {
      throw new BadRequestException('Validation failed');
    }

    return dtoInstance;
  }
}
