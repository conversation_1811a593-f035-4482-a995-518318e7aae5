import { IsArray, IsIn, IsInt, IsString, Length, ValidateNested } from 'class-validator';
import { AdditionalInformationAccountType, AdditionalInfoTemplate } from '@src/common/enums/additional-information';
import { Transform, Type } from 'class-transformer';
import { PackType } from '@src/common/enums/pack-type';

export class ListFileNamesDto {
  @IsString()
  name: string;

  @IsString()
  position: string;
}

export class AdditionalInformationHotWaterEnergySaving2025Dto {
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType: number;

  @IsInt()
  @Type(() => Number)
  templateType: AdditionalInfoTemplate.CountertopDishwasher;

  @IsString()
  @Length(64)
  verificationToken: string;

  @IsString()
  @Length(1, 255)
  bankName: string;

  @IsString()
  @Length(1, 255)
  branchName: string;

  @IsString()
  @IsIn(AdditionalInformationAccountType)
  accountType: string;

  @IsString()
  @Length(1, 255)
  accountNumber: string;

  @IsString()
  @Length(1, 255)
  accountHolderNameKana: string;

  @ValidateNested()
  @Type(() => ListFileNamesDto)
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return {};
    }
  })
  @IsArray()
  listFileNames: ListFileNamesDto[];
}
