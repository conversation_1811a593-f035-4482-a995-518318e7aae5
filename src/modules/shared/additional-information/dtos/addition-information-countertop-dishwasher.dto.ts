import { IsIn, IsInt, IsString, Length } from 'class-validator';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';
import { Type } from 'class-transformer';
import { PackType } from '@src/common/enums/pack-type';

export class AdditionInformationCountertopDishwasherDto {
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType: number;

  @IsInt()
  @Type(() => Number)
  templateType: AdditionalInfoTemplate.CountertopDishwasher;

  @IsString()
  @Length(64)
  verificationToken: string;

  @IsString()
  @Length(1, 30)
  faucetModelNumber: string;
}
