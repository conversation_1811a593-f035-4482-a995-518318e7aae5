import { IsArray, IsIn, IsInt, IsString, Length, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';
import { PackType } from '@src/common/enums/pack-type';

export class ListFileNamesDto {
  @IsString()
  name: string;

  @IsString()
  position: string;
}

export class AdditionalInformationExistingEcoCuteInfoDto {
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType: number;

  @IsInt()
  @Type(() => Number)
  templateType: AdditionalInfoTemplate.ExistingEcoCuteInfoForm;

  @IsString()
  @Length(64)
  verificationToken: string;

  @ValidateNested()
  @Type(() => ListFileNamesDto)
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return [];
    }
  })
  @IsArray()
  listFileNames: ListFileNamesDto[];
}
