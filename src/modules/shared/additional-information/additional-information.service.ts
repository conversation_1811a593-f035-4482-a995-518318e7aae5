import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { AdditionalInformationEntity } from '@src/entities/additional-information.entity';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';
import { AdditionalInformationHotWaterEnergySaving2025Dto } from './dtos/additional-information-hot-water-energy-saving-2025.dto';
import { AdditionInformationCountertopDishwasherDto } from './dtos/addition-information-countertop-dishwasher.dto';
import { AdditionalInformationExistingEcoCuteInfoDto } from './dtos/additional-information-existing-ecocute-info.dto';
import { DocumentService } from '../document/document.service';
import { SpreadsheetService } from '../spreadsheet/spreadsheet.service';
import {
  AdditionActivityType,
  AdditionalInfoStatus,
  AdditionalInfoTemplate,
} from '@common/enums/additional-information';
import { AdditionActivityEntity } from '@src/entities/addition-activity.entity';
import { randomUUID } from 'crypto';
import { DocumentEntity } from '@src/entities/documents.entity';

@Injectable()
export class AdditionalInformationService {
  private readonly logger = new Logger(AdditionalInformationService.name);
  constructor(
    @InjectRepository(AdditionalInformationEntity)
    private readonly additionalInformationRepository: Repository<AdditionalInformationEntity>,
    private readonly documentService: DocumentService,
    private readonly spreadsheetService: SpreadsheetService,
    private readonly dataSource: DataSource,
  ) {}

  async getAdditionalInformationOrFail(
    conditions: { [key: string]: any },
    relations: string[] = [],
    order: { [key: string]: any } = { createdAt: 'ASC' },
  ) {
    const additionalInformation = await this.additionalInformationRepository.findOne({
      where: conditions,
      relations: relations,
      order,
    });
    if (!additionalInformation) {
      throw new NotFoundException('Reservation additional information not found');
    }
    return additionalInformation;
  }

  async uploadAdditionalInformation(
    userId: string,
    files: Express.Multer.File[],
    data:
      | AdditionalInformationHotWaterEnergySaving2025Dto
      | AdditionInformationCountertopDishwasherDto
      | AdditionalInformationExistingEcoCuteInfoDto,
    templateType: AdditionalInfoTemplate,
  ) {
    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        const { verificationToken, ...detail } = data;
        const additionalInformation = await this.getAdditionalInformationOrFail({ verificationToken }, ['reservation']);
        if (additionalInformation.reservation.userId !== userId) {
          throw new NotFoundException('Reservation additional information not found');
        }
        const additionAttemptId = randomUUID();
        await entityManager.save(AdditionAttemptEntity, {
          id: additionAttemptId,
          additionalInformationId: additionalInformation.id,
          detail: {
            ...detail,
          },
        });
        await entityManager.save(AdditionActivityEntity, {
          type: AdditionActivityType.UPLOAD,
          additionalInformationId: additionalInformation.id,
          additionAttemptId,
        });

        if (files.length) {
          const uploadResponse = await Promise.all(
            files.map((file) =>
              this.documentService.uploadFile(
                file,
                additionalInformation.reservationId,
                additionalInformation.id,
                additionAttemptId,
              ),
            ),
          );
          const currentTimestamp = new Date();
          const newFiles = entityManager.create(
            DocumentEntity,
            uploadResponse.map((file, index) => ({
              ...file,
              createdAt: new Date(currentTimestamp.getTime() + index),
              updatedAt: new Date(currentTimestamp.getTime() + index),
            })),
          );
          await entityManager.save(newFiles);
        }

        await this.spreadsheetService.saveAdditionalInformationOnGoogleSheet(
          data,
          templateType,
          additionalInformation.reservation.code,
        );

        await entityManager.update(
          AdditionalInformationEntity,
          { id: additionalInformation.id },
          { status: AdditionalInfoStatus.UPLOADED },
        );
        return { data: {} };
      });
    } catch (err) {
      this.logger.log('[uploadAdditionalInformation] error: ', err);
      return { error: err.message };
    }
  }

  async refreshUploadUrl(id: string) {
    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        Promise.all([
          await entityManager.update(
            AdditionalInformationEntity,
            { id },
            { status: AdditionalInfoStatus.AWAITING_REUPLOAD },
          ),
          await entityManager.save(AdditionActivityEntity, {
            type: AdditionActivityType.REACTIVE,
            additionalInformationId: id,
          }),
        ]);
        return { data: {} };
      });
    } catch (err) {
      this.logger.log('[refreshUploadUrl] error: ', err);
      return { error: err.message };
    }
  }

  async deleteAdditionalInformation(id: string) {
    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        Promise.all([
          await entityManager.update(
            AdditionAttemptEntity,
            { additionalInformationId: id, isDeleted: false },
            { isDeleted: true },
          ),
          await entityManager.update(AdditionalInformationEntity, { id }, { isEnable: false }),
          await entityManager.save(AdditionActivityEntity, {
            type: AdditionActivityType.DELETE,
            additionalInformationId: id,
          }),
        ]);
        return { data: {} };
      });
    } catch (err) {
      this.logger.log('[deleteAdditionalInformation] error: ', err);
      return { error: err.message };
    }
  }
}
