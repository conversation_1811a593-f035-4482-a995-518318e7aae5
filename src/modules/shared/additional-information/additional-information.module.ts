import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdditionalInformationService } from './additional-information.service';
import { SpreadsheetModule } from '../spreadsheet/spreadsheet.module';
import { AdditionalInformationEntity } from '@src/entities/additional-information.entity';
import { ReservationModule } from '../reservation/reservation.module';
import { DocumentModule } from '../document/document.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AdditionalInformationEntity]),
    ReservationModule,
    SpreadsheetModule,
    DocumentModule,
  ],
  providers: [AdditionalInformationService],
  exports: [AdditionalInformationService],
})
export class AdditionalInformationModule {}
