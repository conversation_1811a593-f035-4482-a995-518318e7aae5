import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';
import { getConfidentialPrivateFile } from '@common/utils/image-handler';
import { escapeString } from '@src/common/utils/string-format';
import { DOCUMENT_EXPIRATION_TIME } from '@src/common/constants/additional-information';
import * as dayjs from 'dayjs';

@Injectable()
export class AdditionAttemptService {
  constructor(
    @InjectRepository(AdditionAttemptEntity)
    private readonly additionAttemptRepository: Repository<AdditionAttemptEntity>,
  ) {}

  async getAttemptById(id: string) {
    const attempt = await this.additionAttemptRepository.findOne({
      where: { id },
      relations: ['documents'],
      order: { documents: { createdAt: 'ASC' } },
    });
    if (!attempt) {
      throw new NotFoundException('Reservation additional information attempt not found');
    }
    const existDays = dayjs().diff(dayjs(attempt.createdAt), 'day');
    const isDeletedDocument = attempt.isDeleted || existDays > DOCUMENT_EXPIRATION_TIME;
    return {
      ...attempt,
      isDeleted: isDeletedDocument,
      documents: attempt.documents.map((doc) => ({
        ...doc,
        name: escapeString(doc.name),
        path: isDeletedDocument ? '' : doc.path,
        urlThumb: isDeletedDocument ? '' : getConfidentialPrivateFile(doc.path, '430x322'),
        url: isDeletedDocument ? '' : getConfidentialPrivateFile(doc.path),
      })),
    };
  }
}
