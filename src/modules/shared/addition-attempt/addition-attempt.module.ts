import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdditionAttemptService } from './addition-attempt.service';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AdditionAttemptEntity])],
  providers: [AdditionAttemptService],
  exports: [AdditionAttemptService],
})
export class AdditionAttemptModule {}
