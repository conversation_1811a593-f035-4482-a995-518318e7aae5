import Redis from 'ioredis';
import { Injectable } from '@nestjs/common';
import { InjectRedis } from '@liaoliaots/nestjs-redis';

export enum Buckets {
  Packages = 1,
  ReservationOptions = 2,
  Configurations = 3,
  Reservation = 4,
}

const redisPrefix = process.env.REDIS_CACHE_PREFIX || 'labo-install-pack';

@Injectable()
export class ObjectCacheService {
  constructor(@InjectRedis() private readonly redis: Redis) {}

  public async get<T>(key: string, bucket: Buckets): Promise<T | undefined> {
    const data = await this.getRaw(key, bucket);
    if (!data) {
      return;
    }
    return JSON.parse(data);
  }

  public async getRaw(key: string, bucket: Buckets): Promise<string | undefined> {
    const fullKey = this.getFullKey(key, bucket);
    const data = await this.redis.get(fullKey);
    return data;
  }

  public async addOrUpdate<T>(key: string, bucket: Buckets, object: T, expireInSeconds = 0): Promise<void> {
    const data = JSON.stringify(object);
    return this.addOrUpdateRaw(key, bucket, data, expireInSeconds);
  }

  public async addOrUpdateRaw(key: string, bucket: Buckets, object: any, expireInSeconds = 0): Promise<void> {
    const fullKey = this.getFullKey(key, bucket);
    if (expireInSeconds > 0) {
      await this.redis.set(fullKey, object, 'EX', expireInSeconds);
      return;
    }
    await this.redis.set(fullKey, object);
  }

  public async delete(key: string, bucket: Buckets): Promise<void> {
    const fullKey = this.getFullKey(key, bucket);
    await this.redis.del(fullKey);
  }

  private getFullKey(key: string, bucket: Buckets): string {
    const keyParts = [redisPrefix, bucket.toString(), key];
    return keyParts.join(':');
  }
}
