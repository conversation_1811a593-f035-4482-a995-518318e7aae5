import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClientModule } from '@vietnam/cnga-http-request';
import { DocumentEntity } from '@src/entities/documents.entity';
import { DocumentService } from './document.service';

@Module({
  imports: [TypeOrmModule.forFeature([DocumentEntity]), ApiClientModule.forRoot()],
  providers: [DocumentService],
  exports: [DocumentService],
})
export class DocumentModule {}
