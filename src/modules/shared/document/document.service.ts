import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { DocumentType } from '@common/enums/documents';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { randomUUID } from 'crypto';
import * as sharp from 'sharp';

@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);

  async uploadFile(file: Express.Multer.File, reservationId: string, additionId: string, additionAttemptId: string) {
    const newId = randomUUID();
    let ext = file.mimetype.split('/')[1];
    const type = file.mimetype.startsWith('image') ? DocumentType.IMAGE : DocumentType.PDF;
    if (ext === 'png') {
      file.buffer = await sharp(file.buffer).webp().toBuffer();
      ext = 'webp';
      file.mimetype = 'image/webp';
    }

    const path = `labo-install-pack/${reservationId}/${additionId}/${additionAttemptId}/${newId}.${ext}`;
    const s3Client = new S3Client();
    const s3PutCommand = new PutObjectCommand({
      Bucket: process.env.LABO_CONFIDENTIAL_S3_BUCKET_NAME,
      Key: path,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    try {
      await s3Client.send(s3PutCommand);
      return {
        id: newId,
        path: path,
        additionAttemptId,
        name: Buffer.from(file.originalname, 'binary').toString('utf8'),
        documentType: type,
      };
    } catch (e) {
      this.logger.log(e);
      throw new InternalServerErrorException('UPLOAD_FILE_ERROR');
    }
  }
}
