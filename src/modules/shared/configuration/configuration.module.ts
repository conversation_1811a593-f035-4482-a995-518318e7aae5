import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigurationService } from './configuration.service';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigurationEntity } from '../../../entities/configuration.entity';
import { ObjectCacheModule } from '../object-cache/object-cache.module';

@Module({
  imports: [CacheModule.register(), TypeOrmModule.forFeature([ConfigurationEntity]), ObjectCacheModule],
  providers: [ConfigurationService],
  exports: [ConfigurationService],
})
export class ConfigurationModule {}
