import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { CACHE_STORE } from '../../../common/constants';
import { IConfiguration } from '../../../common/interface/reservation';
import { ConfigurationEntity } from '../../../entities/configuration.entity';
import { UpdateConfigurationDto } from './dtos/update-configuration.dto';
import { Buckets, ObjectCacheService } from '../object-cache/object-cache.service';
const schema = process.env.DATABASE_SCHEMA;

@Injectable()
export class ConfigurationService implements OnModuleInit {
  constructor(
    @InjectRepository(ConfigurationEntity)
    private readonly configurationRepository: Repository<ConfigurationEntity>,
    private objectCache: ObjectCacheService,
    private readonly dataSource: DataSource,
  ) {}

  async onModuleInit() {
    await this.resetCache();
  }

  async getConfigurations() {
    const data: IConfiguration[] = await this.objectCache.get(CACHE_STORE.CONFIGURATION.key, Buckets.Configurations);
    if (data) {
      return data;
    }
    const configs = await this.configurationRepository.find();
    await this.objectCache.addOrUpdate(
      CACHE_STORE.CONFIGURATION.key,
      Buckets.Configurations,
      configs,
      CACHE_STORE.CONFIGURATION.ttl,
    );
    return configs;
  }

  getConfiguration(key: string, packType: number) {
    return this.configurationRepository.findOneBy({ configKey: key, packType });
  }

  async updateConfigurations(id: string, data: UpdateConfigurationDto) {
    try {
      await this.configurationRepository.update(
        { id },
        {
          value: data.value,
        },
      );
      await this.resetCache();
      return { data: {} };
    } catch (err) {
      return { error: err.message };
    }
  }

  async resetCache() {
    await this.objectCache.delete(CACHE_STORE.CONFIGURATION.key, Buckets.Configurations);
  }

  async generateRandomCode(table: string, field: string, prefix: string, length: number) {
    const nextCode = await this.dataSource.query(
      `SELECT "${schema}".generate_code('"${schema}"."${table}"', $1, $2, $3);`,
      [field, prefix, length],
    );
    return nextCode[0].generate_code;
  }

  async getNextCode(sequence: string, prefix: string, length: number) {
    const lastCode = await this.dataSource.query(
      `SELECT lpad(last_value::text, ${length}, '0') as code FROM "${schema}".${sequence};`,
    );
    return `${prefix}${lastCode[0].code}`;
  }

  async setNextCode(sequence: string) {
    return await this.dataSource.query(
      `SELECT setval('"${schema}".${sequence}', (SELECT last_value + 1 FROM "${schema}".${sequence}));`,
    );
  }
}
