import { Injectable, NotFoundException } from '@nestjs/common';
import { PackageTypeTraitFileMapping, PackType } from '@src/common/enums/pack-type';
import { IPackageBuiltInDishwasherTrait } from '@src/common/interface/package';
import { readFileSync } from 'fs';
import * as path from 'path';
import { PackageService } from './package.service';
import { PackageStatus } from '../../../common/enums/package';

@Injectable()
export class BuiltInDishwasherPackageService {
  constructor(private readonly packageService: PackageService) {}

  getCategoryTraits() {
    const jsonData = JSON.parse(
      readFileSync(
        path.join(__dirname, '..', '..', '..', PackageTypeTraitFileMapping[PackType.BUILD_IN_DISHWASHER]),
        'utf8',
      ),
    );
    const categoryTraits: IPackageBuiltInDishwasherTrait[] = jsonData.categories;
    return categoryTraits;
  }

  async getBuiltInDishwasherPackagesOrFail(productCode: string) {
    const categoryTraits = this.getCategoryTraits();
    const category = categoryTraits.find((trait) => trait.code === productCode);
    if (!category) {
      throw new NotFoundException();
    }
    const activePackages = await this.packageService.getEnablePackages(PackType.BUILD_IN_DISHWASHER);
    const selectedPackage = activePackages.find((p) => p.id === category.id);
    if (!selectedPackage || selectedPackage.status === PackageStatus.DISABLE || selectedPackage.outOfStock) {
      throw new NotFoundException();
    }
    return {
      ...selectedPackage,
      categoryCode: category.code,
      image: category.image,
    };
  }
}
