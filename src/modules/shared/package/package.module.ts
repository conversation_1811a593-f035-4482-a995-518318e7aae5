import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PackageEntity } from '@src/entities/package.entity';
import { PackageService } from './package.service';
import { ReservationsPackagesEntity } from '@src/entities/reservations-packages.entity';
import { ConfigurationModule } from '../configuration/configuration.module';
import { CacheModule } from '@nestjs/cache-manager';
import { ObjectCacheModule } from '../object-cache/object-cache.module';
import { AirconPackageService } from './aircon-package.service';
import { BuiltInDishwasherPackageService } from './built-in-dishwasher-package.service';

@Module({
  imports: [
    CacheModule.register(),
    TypeOrmModule.forFeature([PackageEntity, ReservationsPackagesEntity]),
    ConfigurationModule,
    ObjectCacheModule,
  ],
  providers: [PackageService, AirconPackageService, BuiltInDishwasherPackageService],
  exports: [PackageService, AirconPackageService, BuiltInDishwasherPackageService],
})
export class PackageModule {}
