import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PackageEntity } from '../../../entities/package.entity';
import { PackageService } from './package.service';
import { ReservationsPackagesEntity } from '../../../entities/reservations-packages.entity';
import { ConfigurationModule } from '../configuration/configuration.module';
import { CacheModule } from '@nestjs/cache-manager';
import { ObjectCacheModule } from '../object-cache/object-cache.module';

@Module({
  imports: [
    CacheModule.register(),
    TypeOrmModule.forFeature([PackageEntity, ReservationsPackagesEntity]),
    ConfigurationModule,
    ObjectCacheModule,
  ],
  providers: [PackageService],
  exports: [PackageService],
})
export class PackageModule {}
