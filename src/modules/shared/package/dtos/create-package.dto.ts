import { PartialType } from '@nestjs/swagger';
import { IsBoolean, IsIn, IsInt, IsString, Min } from 'class-validator';
import { PackageStatus } from '../../../../common/enums/package';
import { Transform, Type } from 'class-transformer';
import { PackType } from '../../../../common/enums/pack-type';

export class CreatePackageDto {
  @IsString()
  name: string;

  @IsString()
  code: string;

  @IsInt()
  @Min(1)
  @Type(() => Number)
  fee: number;

  @IsInt()
  @Min(1)
  @Type(() => Number)
  sort: number;

  @IsBoolean()
  outOfStock: boolean;

  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackageStatus))
  status: number;

  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType: number;
}

export class UpdatePackageDto extends PartialType(CreatePackageDto) {}
