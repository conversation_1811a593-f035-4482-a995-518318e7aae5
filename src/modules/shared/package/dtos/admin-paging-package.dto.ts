import { Transform, Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional } from 'class-validator';
import { PAGINATOR } from '../../../../common/constants';
import { PackageStatus } from '../../../../common/enums/package';
import { PackType } from '../../../../common/enums/pack-type';

export class AdminPaginatePackageDto {
  @IsOptional()
  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackageStatus))
  status?: number;

  @IsOptional()
  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType?: number;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  page?: number;

  get offset(): number {
    return this.page > 0 ? (this.page - 1) * PAGINATOR.TERRY : 0;
  }

  get pageNumber(): number {
    return parseInt(String(this.page ? this.page : 1));
  }

  get perPage(): number {
    return PAGINATOR.TERRY;
  }
}
