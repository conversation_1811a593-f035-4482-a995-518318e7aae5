import { Injectable } from '@nestjs/common';
import { PackageTypeRouteMapping, PackageTypeTraitFileMapping, PackType } from '@src/common/enums/pack-type';
import { IPackage, IAirconCategoryTrait, IPackageAirconTrait } from '@src/common/interface/package';
import { readFileSync } from 'fs';
import * as path from 'path';

@Injectable()
export class AirconPackageService {
  constructor() {}

  getCategoryTraits() {
    const jsonData = JSON.parse(
      readFileSync(
        path.join(__dirname, '..', '..', '..', PackageTypeTraitFileMapping[PackType.AIRCON_INSTALL]),
        'utf8',
      ),
    );
    const categoryTraits: IAirconCategoryTrait[] = jsonData.categories;
    return categoryTraits;
  }

  getCategorySpecifications(packages: IPackage[]) {
    const categoryTraits = this.getCategoryTraits();
    const packageTraits = categoryTraits
      .map((catTrait) =>
        (catTrait.packages as IPackageAirconTrait[]).map((pkg) => ({
          ...pkg,
          brandJp: catTrait.brandJp,
          seriesJp: catTrait.seriesJp,
          structuredDataDescription: catTrait.structuredDataDescription,
        })),
      )
      .flat();
    const productsJsonLd = packages.map((pack) => {
      const trait = packageTraits.find((packTrait) => {
        return packTrait.id === pack.id;
      });
      if (!trait) {
        return null;
      }
      const structuredDataName = `${trait.brandJp} ${trait.model} ${trait.seriesJp} ${trait.tatamiSize}畳用`;
      return {
        '@type': 'Product',
        name: structuredDataName,
        image: [`${process.env.APP_URL?.replace('https:', '')}/${process.env.APP_STATIC_PREFIX}/${trait.image}`],
        description: trait.structuredDataDescription,
        sku: pack.code,
        gtin13: trait.gtin13,
        brand: {
          '@type': 'http://schema.org/Brand',
          name: trait.brandJp,
        },
        offers: {
          '@type': 'Offer',
          url: process.env.APP_URL?.replace('https:', '') + PackageTypeRouteMapping[PackType.AIRCON_INSTALL],
          priceCurrency: 'JPY',
          price: pack.fee,
          availability: 'https://schema.org/InStock',
        },
      };
    });
    return {
      categoryTraits,
      productsJsonLd: JSON.stringify({
        '@context': 'https://schema.org/',
        '@graph': productsJsonLd.filter((item) => item !== null),
      }),
    };
  }
}
