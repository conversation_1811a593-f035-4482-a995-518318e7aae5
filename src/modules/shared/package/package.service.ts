import { BadRequestException, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, In, Not, Repository } from 'typeorm';
import { PackageEntity } from '../../../entities/package.entity';
import { AdminPaginatePackageDto } from './dtos/admin-paging-package.dto';
import { PackageStatus, PackageStatusMapping } from '@src/common/enums/package';
import { CreatePackageDto, UpdatePackageDto } from './dtos/create-package.dto';
import { ReservationsPackagesEntity } from '../../../entities/reservations-packages.entity';
import { ConfigurationService } from '../configuration/configuration.service';
import { CODE_PREFIX, CODE_SEQUENCES } from '@src/common/enums/configuration';
import { unparse } from 'papaparse';
import { extname } from 'path';
import { promisify } from 'util';
import { pipeline } from 'stream';
import { parse } from 'csv-parse';
import { convertStringToNumber } from '@src/common/utils/format';
import { randomUUID } from 'crypto';
import { checkAndExtractPackageCode } from '@src/common/utils/helper';
import {
  PackageTypeMapping,
  PackageTypeRouteMapping,
  PackageTypeTraitFileMapping,
  PackType,
} from '@src/common/enums/pack-type';
import { CACHE_STORE } from '@src/common/constants';
import { IPackage, IAirconCategoryTrait, IPackageAirconTrait } from '@src/common/interface/package';
import { Buckets, ObjectCacheService } from '../object-cache/object-cache.service';
import { readFileSync } from 'fs';
import * as path from 'path';
const schema = process.env.DATABASE_SCHEMA;

@Injectable()
export class PackageService implements OnModuleInit {
  private readonly logger = new Logger(PackageService.name);
  constructor(
    private objectCache: ObjectCacheService,
    @InjectRepository(PackageEntity)
    private readonly packageRepository: Repository<PackageEntity>,
    @InjectRepository(ReservationsPackagesEntity)
    private readonly reservationsPackagesRepository: Repository<ReservationsPackagesEntity>,
    private readonly configurationService: ConfigurationService,
    private readonly dataSource: DataSource,
  ) {}

  async onModuleInit() {
    await this.resetCache();
  }

  async getPackagePageCommonData() {
    const status = Object.entries(PackageStatusMapping).map((e) => ({ id: e[0], name: e[1] }));
    const packageType = Object.entries(PackageTypeMapping).map((e) => ({ id: e[0], name: e[1] }));
    const nextCode = await this.configurationService.getNextCode(CODE_SEQUENCES.PACKAGE, CODE_PREFIX.PACKAGE, 5);
    return { status, nextCode, packageType };
  }

  getAirconCategoryTraits() {
    const jsonData = JSON.parse(
      readFileSync(
        path.join(__dirname, '..', '..', '..', PackageTypeTraitFileMapping[PackType.AIRCON_INSTALL]),
        'utf8',
      ),
    );
    const categoryTraits: IAirconCategoryTrait[] = jsonData.categories;
    return categoryTraits;
  }

  getAirCategorySpecifications(packages: IPackage[]) {
    const categoryTraits = this.getAirconCategoryTraits();
    const packageTraits = categoryTraits
      .map((catTrait) =>
        (catTrait.packages as IPackageAirconTrait[]).map((pkg) => ({
          ...pkg,
          brandJp: catTrait.brandJp,
          seriesJp: catTrait.seriesJp,
          structuredDataDescription: catTrait.structuredDataDescription,
        })),
      )
      .flat();
    const productsJsonLd = packages.map((pack) => {
      const trait = packageTraits.find((packTrait) => {
        return packTrait.id === pack.id;
      });
      if (!trait) {
        return null;
      }
      const structuredDataName = `${trait.brandJp} ${trait.model} ${trait.seriesJp} ${trait.tatamiSize}畳用`;
      return {
        '@type': 'Product',
        name: structuredDataName,
        image: [`${process.env.APP_URL?.replace('https:', '')}/${process.env.APP_STATIC_PREFIX}/${trait.image}`],
        description: trait.structuredDataDescription,
        sku: pack.code,
        gtin13: trait.gtin13,
        brand: {
          '@type': 'http://schema.org/Brand',
          name: trait.brandJp,
        },
        offers: {
          '@type': 'Offer',
          url: process.env.APP_URL?.replace('https:', '') + PackageTypeRouteMapping[PackType.AIRCON_INSTALL],
          priceCurrency: 'JPY',
          price: pack.fee,
          availability: 'https://schema.org/InStock',
        },
      };
    });
    return {
      categoryTraits,
      productsJsonLd: JSON.stringify({
        '@context': 'https://schema.org/',
        '@graph': productsJsonLd.filter((item) => item !== null),
      }),
    };
  }

  async getPackages(paginatePackageDto: AdminPaginatePackageDto) {
    const { perPage, pageNumber, offset, status, packType } = paginatePackageDto;
    let queryBuilder = this.packageRepository
      .createQueryBuilder('package')
      .select(['package.id', 'package.code', 'package.name', 'package.status', 'package.createdAt']);

    if (status) {
      queryBuilder.andWhere('package.status = :status', { status });
    }

    if (packType) {
      queryBuilder.andWhere('package.packType = :packType', { packType });
    }

    queryBuilder = queryBuilder.orderBy('package.sort', 'ASC').addOrderBy('package.createdAt', 'DESC');

    if (perPage && pageNumber) {
      queryBuilder = queryBuilder.skip(offset).take(perPage);
    }

    const [packages, total] = await queryBuilder.getManyAndCount();
    let reservationCount = [];
    if (packages.length) {
      reservationCount = await this.reservationsPackagesRepository
        .createQueryBuilder('resPack')
        .select(['resPack.packageId AS "packageId"', 'SUM(resPack.quantity) AS "count"'])
        .where('resPack.packageId IN (:...ids)', { ids: packages.map((e) => e.id) })
        .groupBy('resPack.packageId')
        .getRawMany();
    }

    return {
      packages: packages.map((pack) => {
        const packageReservationCount = reservationCount.find((item) => item.packageId === pack.id);
        pack['reservationCount'] = +packageReservationCount?.count || 0;
        return pack;
      }),
      pagination: {
        perPage: perPage,
        pageNumber: pageNumber,
        total,
      },
    };
  }

  async getPackageList(payload: { conditions?: { [key: string]: any }; select?: string[]; needSort?: boolean } = {}) {
    const { conditions, select, needSort } = payload;
    const queryConditions = { where: conditions };
    if (select?.length) {
      queryConditions['select'] = select;
    }
    if (needSort) {
      queryConditions['order'] = { sort: 'ASC', createdAt: 'DESC' };
    }
    return await this.packageRepository.find(queryConditions);
  }

  async getEnablePackages(packType: number) {
    const key = `${CACHE_STORE.PACKAGES.key}:${packType}`;
    const data: IPackage[] = await this.objectCache.get(key, Buckets.Packages);
    if (data) {
      return data;
    }
    const packages = await this.getPackageList({
      conditions: { status: PackageStatus.ENABLE, packType },
      needSort: true,
    });
    await this.objectCache.addOrUpdate(key, Buckets.Packages, packages, CACHE_STORE.PACKAGES.ttl);
    return packages;
  }

  async getPackageDetail(id: string, relations: string[] = []) {
    return await this.packageRepository.findOne({
      where: { id },
      relations,
    });
  }

  async createPackage(data: CreatePackageDto) {
    try {
      const nextCode = await this.configurationService.getNextCode(CODE_SEQUENCES.PACKAGE, CODE_PREFIX.PACKAGE, 5);
      const existCodePackage = await this.packageRepository.findOne({ where: { code: data.code } });
      if (existCodePackage) {
        throw new BadRequestException('Code already exists');
      }
      const existModelPackage = await this.packageRepository.findOne({ where: { name: data.name } });
      if (existModelPackage) {
        throw new BadRequestException('Package name already exists');
      }
      await this.packageRepository.insert({ ...data });
      if (nextCode === data.code) {
        await this.configurationService.setNextCode(CODE_SEQUENCES.PACKAGE);
      }
      await this.resetCache();
      return { data: { code: data.code } };
    } catch (err) {
      return { error: err.message };
    }
  }

  async updatePackage(id: string, data: UpdatePackageDto) {
    try {
      const nextCode = await this.configurationService.getNextCode(CODE_SEQUENCES.PACKAGE, CODE_PREFIX.PACKAGE, 5);
      const existCodePackage = await this.packageRepository.findOne({ where: { code: data.code, id: Not(id) } });
      if (existCodePackage) {
        throw new BadRequestException('Code already exists');
      }
      const existModelName = await this.packageRepository.findOne({
        where: { name: data.name, id: Not(id) },
      });
      if (existModelName) {
        throw new BadRequestException('Package name already exists');
      }
      await this.packageRepository.update({ id }, { ...data });
      if (nextCode === data.code) {
        await this.configurationService.setNextCode(CODE_SEQUENCES.PACKAGE);
      }
      await this.resetCache();
      return { data: { code: data.code } };
    } catch (err) {
      return { error: err.message };
    }
  }

  async deletePackage(id: string) {
    try {
      const inUsePackage = await this.reservationsPackagesRepository.findOne({ where: { packageId: id } });
      if (inUsePackage) {
        throw new BadRequestException('Package is in use');
      }
      await this.packageRepository.delete({ id });
      await this.resetCache();
      return { message: 'Delete package successfully' };
    } catch (err) {
      return { error: err.message };
    }
  }

  async exportPackages(packageIds: string[], res: any) {
    const packages = await this.getPackageList({
      conditions: { id: In(packageIds) },
      needSort: true,
    });
    const data = packages.map((pack) => ({
      code: pack.code,
      name: pack.name,
      price: pack.fee,
      out_of_stock: pack.outOfStock,
      sort: pack.sort,
      status: pack.status,
      pack_type: pack.packType,
    }));
    const csv = unparse(data);
    res.header('Content-Type', 'text/csv');
    res.attachment('packages.csv');
    res.send(csv);
  }

  async importPackages(file: Express.Multer.File) {
    try {
      const extension = extname(file.originalname);
      if (extension !== '.csv') {
        throw new BadRequestException(`CSVのファイル形式でインポートしてください。`);
      }
      const fileBufferData = file.buffer.toString().split('\n').slice(1);
      const fileStream = fileBufferData.join('\n');
      if (!fileStream) {
        throw new BadRequestException('ファイルにデータがありません。');
      }
      const packages = await this.packageRepository.find({});
      const nextCode = await this.configurationService.getNextCode(CODE_SEQUENCES.PACKAGE, CODE_PREFIX.PACKAGE, 5);
      let nextCodeNumber = checkAndExtractPackageCode(nextCode);
      const fileParser = parse(fileStream, {
        delimiter: ',',
        from_line: 1,
      });
      const validImportData = [];
      const pipelineAsync = promisify(pipeline);
      await pipelineAsync(fileParser, async (source) => {
        let line = 2;
        for await (const record of source) {
          const errorMessages = [];
          const packageCode = record[0]?.trim();
          const packageName = record[1]?.trim();
          const packagePrice = record[2]?.trim();
          const packageOutOfStock = record[3]?.trim();
          const packageSort = record[4]?.trim();
          const packageStatus = record[5]?.trim();
          const packageType = record[6]?.trim();

          if (!packageName) {
            errorMessages.push('パッケージ名が必要です');
          }
          const existPackageName = packages.find((pack) => pack.name === packageName && pack.code !== packageCode);
          if (existPackageName) {
            errorMessages.push('同じ名前のパッケージが既に存在します');
          }
          if (!packagePrice) {
            errorMessages.push('料金が必要です');
          } else if (!convertStringToNumber(packagePrice) || !Number.isInteger(convertStringToNumber(packagePrice))) {
            errorMessages.push('料金は整数である必要があります');
          }
          if (!packageOutOfStock) {
            errorMessages.push('利用可能数が必要です。');
          } else if (packageOutOfStock !== 'true' && packageOutOfStock !== 'false') {
            errorMessages.push('利用可能数はtrueまたはfalseである必要があります');
          }
          if (!packageSort) {
            errorMessages.push('ソートが必要です');
          } else if (
            !convertStringToNumber(packageSort) ||
            !Number.isInteger(convertStringToNumber(packageSort)) ||
            convertStringToNumber(packageSort) < 1 ||
            convertStringToNumber(packageSort) > 10000
          ) {
            errorMessages.push('ソートは1から10000の間で整数である必要があります');
          }
          if (!packageStatus || !Object.values(PackageStatus).includes(convertStringToNumber(packageStatus))) {
            errorMessages.push('ステータスが必要です');
          }
          if (!packageType || !Object.values(PackType).includes(convertStringToNumber(packageType))) {
            errorMessages.push('パッケージタイプが必要です');
          }
          const importData = {
            id: '',
            code: '',
            name: packageName,
            fee: convertStringToNumber(packagePrice),
            outOfStock: packageOutOfStock === 'true',
            sort: convertStringToNumber(packageSort),
            status: convertStringToNumber(packageStatus),
            packType: convertStringToNumber(packageType),
            line,
          };

          if (packageCode) {
            const newCodeNumber = checkAndExtractPackageCode(packageCode);
            if (!newCodeNumber) {
              errorMessages.push('コードは有効ではありません');
            } else {
              importData.code = packageCode;
              if (newCodeNumber > nextCodeNumber) {
                nextCodeNumber = newCodeNumber + 1;
              }
            }
          } else {
            importData.code = `${CODE_PREFIX.PACKAGE}${nextCodeNumber.toString().padStart(5, '0')}`;
            nextCodeNumber++;
          }

          if (errorMessages.length) {
            throw new BadRequestException(`${line}行目：${errorMessages.join('、')}`);
          }

          const overlapImportPackageCode = validImportData.find((pack) => pack.code === record[0]);
          if (overlapImportPackageCode) {
            throw new BadRequestException(
              `${overlapImportPackageCode.line}行目そして${line}行目：同じコードのパッケージが複数回インポートされました`,
            );
          }

          const overlapImportPackageName = validImportData.find((pack) => pack.name === record[1]);
          if (overlapImportPackageName) {
            throw new BadRequestException(
              `${overlapImportPackageName.line}行目そして${line}行目：同じ名前のパッケージが複数回インポートされました`,
            );
          }

          const packageExist = packages.find((pack) => pack.code === record[0]);
          if (packageExist) {
            importData.id = packageExist.id;
          } else {
            importData.id = randomUUID();
          }
          validImportData.push(importData);
          line++;
        }
      });
      const updateSequenceQuery = `SELECT setval('${schema}.package_code_sequence_seq', ${nextCodeNumber});`;
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        await entityManager.save(PackageEntity, validImportData);
        await entityManager.query(updateSequenceQuery);
        await this.resetCache();
        return { message: 'ファイルインポートは完了しました。' };
      });
    } catch (err) {
      return { error: err.message };
    }
  }

  async resetCache() {
    const keys = Object.values(PackType).map((packType) => `${CACHE_STORE.PACKAGES.key}:${packType}`);
    await Promise.all(keys.map((key) => this.objectCache.delete(key, Buckets.Packages)));
  }
}
