import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger } from '@nestjs/common';
import { ACCOUNT_TYPES } from '@vietnam/cnga-middleware';
import { Request } from 'express';
import { MQQueueV2EventName } from '@src/common/enums/notification';
import { ReservationDetailType } from '@src/common/types/email-content.type';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  constructor(private readonly amqpConnection: AmqpConnection) {}

  async sendEmailToUser(data: any, request: Request) {
    if (!data.customerEmail) return;
    try {
      return await this.amqpConnection.publish(
        'taskManager',
        'notification',
        {
          task: MQQueueV2EventName.SendMailTask,
          content: {
            from: process.env.CUSTOMER_SERVICE_EMAIL,
            to: data.customerEmail,
            template_name: data.emailTemplate,
            subject: data.emailSubject,
            properties: data.emailProperties,
          },
        },
        { headers: this.getNotificationHeaders(request) },
      );
    } catch (err) {
      this.logger.error('sendEmailToUser error: ', err);
    }
  }

  async pushNotificationToUser(data: any, request: Request) {
    try {
      return await this.amqpConnection.publish(
        'taskManager',
        'notification',
        {
          task: MQQueueV2EventName.PushNotification,
          content: {
            clientId: data.userId,
            message: data.message,
            sound: 'sound1.aiff',
            data: {
              //TODO: need to update when confirm reservation type
              type: 'labo_install_pack',
              url: `/shopapp/labo-install-pack/v1/reservation/${data.reservationCode}/`,
            },
          },
        },
        { headers: this.getNotificationHeaders(request) },
      );
    } catch (err) {
      this.logger.error(err, 'pushNotificationToUser error: ');
    }
  }

  private getNotificationHeaders(request: Request) {
    return {
      'x-request-id': request.headers['x-request-id'],
      'x-client-id': request.terrySession?.adminId,
      'x-client-type': ACCOUNT_TYPES.Admin,
    };
  }

  buildEmailPropertiesForNewReservation(data: {
    reservationCode: string;
    responseDate: string;
    lastname: string;
    firstname: string;
    fee: string;
    reservationDetail: ReservationDetailType[];
    deliveryContent: boolean;
    additionalInformationUrl: string;
  }) {
    return {
      full_name: `${data.lastname}${data.firstname}`,
      response_date: data.responseDate,
      reservation_code: data.reservationCode,
      total_price: data.fee,
      additional_information_url: data.additionalInformationUrl,
      reservation_detail_html: this.buildReservationDetailByFormat(data.reservationDetail, 'html'),
      reservation_detail_text: this.buildReservationDetailByFormat(data.reservationDetail, 'text'),
      delivery_content_html: data.deliveryContent ? this.buildDeliveryContentByFormat('html') : '',
      delivery_content_text: data.deliveryContent ? this.buildDeliveryContentByFormat('text') : '',
    };
  }
  buildReservationDetailByFormat(raw: ReservationDetailType[], format: 'html' | 'text') {
    if (!raw?.length) {
      return '';
    }
    if (format === 'html') {
      return raw
        .map((item) => {
          return item.quantity
            ? `<span>・${item.name}<br>　数量：${item.quantity}<br>　単価：${item.fee}<br>　合計：${item.totalFee}</span>`
            : `<span>・${item.name}<br>　合計：${item.totalFee}</span>`;
        })
        .join('<br><br>');
    }
    if (format === 'text') {
      return raw
        .map((item) => {
          return item.quantity
            ? `・${item.name}\n　数量：${item.quantity}\n　単価：${item.fee}\n　合計：${item.totalFee}`
            : `・${item.name}\n　合計：${item.totalFee}`;
        })
        .join('\n');
    }
  }
  buildDeliveryContentByFormat(format: 'html' | 'text') {
    if (format === 'html') {
      return `<p>【製品の配送について】</p><p>「卓上食洗機」と「分岐水栓」は別々に配送されます。工事にはどちらも必要ですので、当日までに受け取りをお願いいたします。</p>`;
    }
    if (format === 'text') {
      return `【製品の配送について】\n\n「卓上食洗機」と「分岐水栓」は別々に配送されます。工事にはどちらも必要ですので、当日までに受け取りをお願いいたします。\n\n`;
    }
  }
}
