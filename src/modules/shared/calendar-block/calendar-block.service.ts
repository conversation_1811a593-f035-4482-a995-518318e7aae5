import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, Repository } from 'typeorm';
import { CalendarBlockQueryDto } from './dtos/calendar-block-query.dto';
import { CalendarBlockEntity } from '../../../entities/calendar-block.entity';
import * as dayjs from 'dayjs';
import { YYYYMD } from '../../../common/utils/date-util';
import { SetCalendarBlockDateDto } from './dtos/set-calendar-block-date.dto';
import { DeleteCalendarBlockDateDto } from './dtos/delete-calendar-block-date.dto';

@Injectable()
export class CalendarBlockService {
  constructor(
    @InjectRepository(CalendarBlockEntity)
    private readonly calendarBlockRepository: Repository<CalendarBlockEntity>,
  ) {}

  async getCalendarBlockDates(params: CalendarBlockQueryDto) {
    const start = dayjs(params.start).format(YYYYMD);
    const end = dayjs(params.end).format(YYYYMD);
    const blockDates = await this.calendarBlockRepository.find({
      where: {
        blockDate: Between(start, end),
        packType: params.packType,
      },
      select: ['blockDate'],
    });
    return blockDates.map((block) => block.blockDate);
  }

  async setCalendarBlockDate(data: SetCalendarBlockDateDto) {
    const insertData = data.dates.map((date) => ({
      blockDate: date,
      packType: data.packType,
    }));
    return await this.calendarBlockRepository.createQueryBuilder().insert().values(insertData).orIgnore().execute();
  }

  async deleteCalendarBlockDate(data: DeleteCalendarBlockDateDto) {
    return await this.calendarBlockRepository.delete({
      blockDate: In(data.dates),
      packType: data.packType,
    });
  }
}
