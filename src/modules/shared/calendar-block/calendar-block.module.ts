import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CalendarBlockService } from './calendar-block.service';
import { CacheModule } from '@nestjs/cache-manager';
import { CalendarBlockEntity } from '../../../entities/calendar-block.entity';

@Module({
  imports: [CacheModule.register(), TypeOrmModule.forFeature([CalendarBlockEntity])],
  providers: [CalendarBlockService],
  exports: [CalendarBlockService],
})
export class CalendarBlockModule {}
