import { Transform, Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsDateString, IsIn, IsInt } from 'class-validator';
import { PackType } from '../../../../common/enums/pack-type';

export class DeleteCalendarBlockDateDto {
  @IsDateString({}, { each: true })
  @IsArray()
  @ArrayMinSize(1)
  dates?: string[];

  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType?: number;
}
