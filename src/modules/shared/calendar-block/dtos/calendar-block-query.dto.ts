import { Transform, Type } from 'class-transformer';
import { IsDateString, IsIn, IsInt } from 'class-validator';
import { PackType } from '../../../../common/enums/pack-type';

export class CalendarBlockQueryDto {
  @IsDateString()
  start: string;

  @IsDateString()
  end: string;

  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(PackType))
  packType: number;
}
