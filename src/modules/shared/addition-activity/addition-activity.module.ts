import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdditionActivityEntity } from '@src/entities/addition-activity.entity';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';
import { AdditionActivityService } from './addition-activity.service';

@Module({
  imports: [TypeOrmModule.forFeature([AdditionActivityEntity, AdditionAttemptEntity])],
  providers: [AdditionActivityService],
  exports: [AdditionActivityService],
})
export class AdditionActivityModule {}
