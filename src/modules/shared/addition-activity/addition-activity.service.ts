import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdditionActivityEntity } from '@src/entities/addition-activity.entity';
import { AdditionActivityType } from '@src/common/enums/additional-information';
import { getConfidentialPrivateFile } from '@src/common/utils/image-handler';
import { AdditionAttemptEntity } from '@src/entities/addition-attempt.entity';
import { DOCUMENT_EXPIRATION_TIME } from '@src/common/constants/additional-information';
import { escapeString } from '@src/common/utils/string-format';
import * as dayjs from 'dayjs';

@Injectable()
export class AdditionActivityService {
  constructor(
    @InjectRepository(AdditionActivityEntity)
    private readonly additionActivityRepository: Repository<AdditionActivityEntity>,
    @InjectRepository(AdditionAttemptEntity)
    private readonly additionAttemptRepository: Repository<AdditionAttemptEntity>,
  ) {}

  async getActivityByAddition(additionalInformationId: string) {
    const [activities, attempts] = await Promise.all([
      this.additionActivityRepository.find({
        where: { additionalInformationId },
        order: { createdAt: 'DESC' },
      }),
      this.additionAttemptRepository.find({
        where: { additionalInformationId },
        relations: ['documents'],
        order: { createdAt: 'DESC', documents: { createdAt: 'ASC' } },
      }),
    ]);
    const activitiesWithDetails = activities.map((activity) => {
      if (activity.type !== AdditionActivityType.UPLOAD) {
        return activity;
      }
      const attempt = attempts.find((attempt) => attempt.id === activity.additionAttemptId);
      if (!attempt) {
        return {
          ...activity,
          isDeleted: true,
          attempt: {
            isDeleted: true,
            documents: [],
          },
        };
      }
      const existDays = dayjs().diff(dayjs(attempt.createdAt), 'day');
      const isDeletedDocument = attempt.isDeleted || existDays > DOCUMENT_EXPIRATION_TIME;
      return {
        ...activity,
        isDeleted: isDeletedDocument,
        attempt: {
          ...attempt,
          isDeleted: isDeletedDocument,
          documents: attempt.documents.map((doc) => ({
            ...doc,
            name: escapeString(doc.name),
            path: isDeletedDocument ? '' : doc.path,
            urlThumb: isDeletedDocument ? '' : getConfidentialPrivateFile(doc.path, '430x322'),
            url: isDeletedDocument ? '' : getConfidentialPrivateFile(doc.path),
          })),
        },
      };
    });
    return activitiesWithDetails;
  }
}
