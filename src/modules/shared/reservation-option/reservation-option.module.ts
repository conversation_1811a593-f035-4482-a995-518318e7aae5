import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReservationOptionService } from './reservation-option.service';
import { ReservationOptionEntity } from '../../../entities/reservation-option.entity';
import { CacheModule } from '@nestjs/cache-manager';
import { ObjectCacheModule } from '../object-cache/object-cache.module';

@Module({
  imports: [CacheModule.register(), TypeOrmModule.forFeature([ReservationOptionEntity]), ObjectCacheModule],
  providers: [ReservationOptionService],
  exports: [ReservationOptionService],
})
export class ReservationOptionModule {}
