import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { ReservationOptionType } from '../../../../common/enums/reservation';

export class TextOptionDetailDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsInt()
  @IsOptional()
  @Min(0)
  price: number;
}

export class ReservationOptionDetailDto {
  @IsString()
  @IsOptional()
  unit?: string;

  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(10)
  maxQuantity: number;

  @IsInt()
  @IsOptional()
  @Min(0)
  pricePerUnit: number;

  @IsArray()
  @ArrayMinSize(1)
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TextOptionDetailDto)
  listOptions: TextOptionDetailDto[];
}

export class UpdateReservationOptionDto {
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @IsOptional()
  @IsString()
  @IsIn(Object.values(ReservationOptionType))
  type?: string;

  @IsOptional()
  @IsBoolean()
  hasPrice?: boolean;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => ReservationOptionDetailDto)
  detail: ReservationOptionDetailDto;
}
