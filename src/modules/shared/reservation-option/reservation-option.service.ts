import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ReservationOptionEntity } from '../../../entities/reservation-option.entity';
import { UpdateReservationOptionDto } from './dtos/update-reservation-option.dto';
import { IReservationOption } from '../../../common/interface/reservation';
import { CACHE_STORE } from '../../../common/constants';
import { PackType } from '../../../common/enums/pack-type';
import { Buckets, ObjectCacheService } from '../object-cache/object-cache.service';

@Injectable()
export class ReservationOptionService implements OnModuleInit {
  constructor(
    @InjectRepository(ReservationOptionEntity)
    private readonly reservationOptionRepository: Repository<ReservationOptionEntity>,
    private objectCache: ObjectCacheService,
  ) {}

  async onModuleInit() {
    await this.resetCache();
  }

  async getEnableReservationOptions(packType: number) {
    const key = `${CACHE_STORE.RESERVATION_OPTIONS.key}:${packType}`;
    const data: IReservationOption[] = await this.objectCache.get(key, Buckets.ReservationOptions);
    if (data) {
      return data;
    }
    const options = await this.reservationOptionRepository.find({
      where: { enabled: true, packType },
      order: { sort: 'ASC' },
    });
    await this.objectCache.addOrUpdate(key, Buckets.ReservationOptions, options, CACHE_STORE.RESERVATION_OPTIONS.ttl);
    return options;
  }

  async getAllReservationOptions(conditions: { [key: string]: any } = {}) {
    return await this.reservationOptionRepository.find({ where: conditions, order: { sort: 'ASC' } });
  }

  async getReservationOptionDetail(id: string) {
    return this.reservationOptionRepository.findOne({
      where: { id },
    });
  }

  async updateReservationOption(id: string, body: UpdateReservationOptionDto) {
    await this.reservationOptionRepository.update(
      { id },
      {
        hasPrice: body.hasPrice,
        type: body.type,
        enabled: body.enabled,
        detail: body.detail as any,
      },
    );
    await this.resetCache();
    return { data: {} };
  }

  async resetCache() {
    const keys = Object.values(PackType).map((packType) => `${CACHE_STORE.RESERVATION_OPTIONS.key}:${packType}`);
    await Promise.all(keys.map((key) => this.objectCache.delete(key, Buckets.ReservationOptions)));
  }
}
