import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReservationEntity } from '../../../entities/reservation.entity';
import { ReservationService } from './reservation.service';
import { ReservationOptionModule } from '../reservation-option/reservation-option.module';
import { PackageModule } from '../package/package.module';
import { SpreadsheetModule } from '../spreadsheet/spreadsheet.module';
import { NotificationModule } from '../notification/notification.module';
import { ConfigurationModule } from '../configuration/configuration.module';
import { ObjectCacheModule } from '../object-cache/object-cache.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ReservationEntity]),
    ObjectCacheModule,
    PackageModule,
    ReservationOptionModule,
    SpreadsheetModule,
    NotificationModule,
    ConfigurationModule,
  ],
  providers: [ReservationService],
  exports: [ReservationService],
})
export class ReservationModule {}
