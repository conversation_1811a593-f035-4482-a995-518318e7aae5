import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { ReservationEntity } from '../../../entities/reservation.entity';
import { AdminPaginateReservationDto } from './dtos/admin-paging-reservation.dto';
import { ReservationOptionType, ReservationStatusMapping } from '@src/common/enums/reservation';
import { CreateAirconReservationDto } from './dtos/create-aircon-reservation.dto';
import { ReservationOptionService } from '../reservation-option/reservation-option.service';
import { PackageService } from '../package/package.service';
import { randomUUID } from 'crypto';
import { ReservationsPackagesEntity } from '../../../entities/reservations-packages.entity';
import { UpdateReservationDto } from './dtos/update-reservation.dto';
import { SpreadsheetService } from '../spreadsheet/spreadsheet.service';
import { formatCurrency } from '@src/common/utils/format';
import { NotificationService } from '../notification/notification.service';
import { Request } from 'express';
import { CACHE_STORE, ERROR_MESSAGE } from '@src/common/constants';
import { EmailTemplateMapping } from '@src/common/constants/reservations';
import { DEFAULT_DISCOUNT_WATER_HEATER } from '@src/common/constants/packages';
import { ConfigurationService } from '../configuration/configuration.service';
import { CODE_PREFIX } from '@src/common/enums/configuration';
import { ENTITIES_ENUM } from '@src/common/enums/entities';
import { CreateHeaterReservationDto } from './dtos/create-heater-reservation.dto';
import { PackType } from '@src/common/enums/pack-type';
import { escapeLike, generateToken } from '@src/common/utils/helper';
import { CreateDishwasherReservationDto } from './dtos/create-dishwasher-reservation.dto';
import * as dayjs from 'dayjs';
import { ReservationDeliveryType } from '@src/common/enums/reservation-options';
import { PackageStatus } from '@src/common/enums/package';
import { Buckets, ObjectCacheService } from '../object-cache/object-cache.service';
import {
  AdditionActivityType,
  AdditionalInfoStatus,
  AdditionalInfoStatusMapping,
  AdditionalInfoTemplate,
} from '@src/common/enums/additional-information';
import { AdditionalInformationEntity } from '@src/entities/additional-information.entity';
import { AdditionActivityEntity } from '@src/entities/addition-activity.entity';

@Injectable()
export class ReservationService {
  private readonly logger = new Logger(ReservationService.name);
  constructor(
    @InjectRepository(ReservationEntity)
    private readonly reservationRepository: Repository<ReservationEntity>,
    private readonly dataSource: DataSource,
    private objectCache: ObjectCacheService,
    private readonly reservationOptionService: ReservationOptionService,
    private readonly packageService: PackageService,
    private readonly spreadsheetService: SpreadsheetService,
    private readonly notificationService: NotificationService,
    private readonly configurationService: ConfigurationService,
  ) {}

  async getReservationPageCommonData() {
    const status = Object.entries(ReservationStatusMapping).map((e) => ({ id: e[0], name: e[1] }));
    const addingDocumentStatus = Object.entries(AdditionalInfoStatusMapping).map((e) => ({ id: e[0], name: e[1] }));
    return { status, addingDocumentStatus };
  }

  getAdditionalInformationTemplate(packType: number) {
    let templateIds: number[] = [];
    switch (packType) {
      case PackType.AIRCON_INSTALL:
      case PackType.WATER_HEATER:
        templateIds = [
          AdditionalInfoTemplate.HotWaterEnergySaving2025,
          AdditionalInfoTemplate.TokyoZeroEmissionPoint,
          AdditionalInfoTemplate.ExistingEcoCuteInfoForm,
        ];
        break;
      case PackType.DISHWASHER:
        templateIds = [AdditionalInfoTemplate.CountertopDishwasher];
        break;
    }
    return templateIds;
  }

  async getReservations(paginateReservationDto: AdminPaginateReservationDto) {
    const { perPage, pageNumber, offset, status, packType, reservationCode } = paginateReservationDto;
    let queryBuilder = this.reservationRepository
      .createQueryBuilder('reservation')
      .select([
        'reservation.id',
        'reservation.code',
        'reservation.lastname',
        'reservation.firstname',
        'reservation.userId',
        'reservation.workingDate1',
        'reservation.status',
        'reservation.createdAt',
      ]);

    if (status) {
      queryBuilder.andWhere('reservation.status = :status', { status });
    }

    if (packType) {
      queryBuilder.andWhere('reservation.packType = :packType', { packType });
    }

    if (reservationCode) {
      queryBuilder.andWhere('reservation.code ILIKE :code', { code: `%${escapeLike(reservationCode)}%` });
    }

    queryBuilder = queryBuilder.orderBy('reservation.createdAt', 'DESC');

    if (perPage && pageNumber) {
      queryBuilder = queryBuilder.skip(offset).take(perPage);
    }

    const [reservations, total] = await queryBuilder.getManyAndCount();
    return {
      reservations,
      pagination: {
        perPage: perPage,
        pageNumber: pageNumber,
        total,
      },
    };
  }

  async getReservationDetail(id: string, relations: string[] = [], order: { [key: string]: any } = {}) {
    return await this.reservationRepository.findOne({
      where: { id },
      relations: relations,
      order,
    });
  }

  async createReservation(
    body: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
    request: Request,
  ) {
    return await this.dataSource.transaction(async (entityManager: EntityManager) => {
      await this.validateIdempotencyKey(body.packType, body.idempotencyKey);
      const { reservationData, reservationPackageData, reservationDetailForEmail } = await this.createReservationData(
        body,
      );

      const nextCode = await this.configurationService.generateRandomCode(
        ENTITIES_ENUM.Reservations,
        'code',
        CODE_PREFIX.RESERVATION,
        7,
      );
      reservationData['code'] = nextCode;
      await Promise.all([
        this.spreadsheetService.saveReservationDataOnGoogleSheet(body, nextCode),
        entityManager.insert(ReservationEntity, reservationData),
        entityManager.insert(ReservationsPackagesEntity, reservationPackageData),
      ]);
      await this.notificationService.sendEmailToUser(
        {
          customerEmail: reservationData.email,
          emailTemplate: EmailTemplateMapping[body.packType],
          emailProperties: this.notificationService.buildEmailPropertiesForNewReservation({
            reservationCode: nextCode,
            lastname: reservationData.lastname,
            firstname: reservationData.firstname,
            responseDate: dayjs().add(1, 'day').format('YYYY/MM/DD'),
            fee: formatCurrency(reservationData.totalFee),
            reservationDetail: reservationDetailForEmail,
            deliveryContent:
              body.packType === PackType.DISHWASHER &&
              (body as CreateDishwasherReservationDto).productDeliveryType === ReservationDeliveryType.PRE_DELIVERY,
          }),
        },
        request,
      );
      return { code: nextCode };
    });
  }

  async validateIdempotencyKey(packType: number, idempotencyKey: string) {
    const key = `${CACHE_STORE.RESERVATION_IDEMPOTENCY_KEY.key}:${packType}:${idempotencyKey}`;
    const existKey = await this.objectCache.get(key, Buckets.Reservation);
    if (existKey) {
      throw new NotFoundException();
    }
    await this.objectCache.addOrUpdate(key, Buckets.Reservation, 'locked', CACHE_STORE.RESERVATION_IDEMPOTENCY_KEY.ttl);
  }

  async createReservationData(
    reservationDto: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
  ) {
    const reservationId = randomUUID();
    const reservationData = {
      id: reservationId,
      packType: reservationDto.packType,
      userId: reservationDto.userId,
      workingDate1: reservationDto.workingDate1,
      workingDate2: reservationDto.workingDate2,
      email: reservationDto.email,
      lastname: reservationDto.lastname,
      firstname: reservationDto.firstname,
      phoneNumber: reservationDto.phoneNumber,
      postalcode: reservationDto.postalcode,
      prefecture: reservationDto.prefecture,
      city: reservationDto.city,
      address: reservationDto.address,
      totalFee: 0,
      totalDiscount: reservationDto.packType === PackType.WATER_HEATER ? DEFAULT_DISCOUNT_WATER_HEATER : 0,
      options: {},
    };

    const { reservationPackageData, reservationDetailForEmail, packageFee } = await this.processPackages(
      reservationDto,
      reservationId,
    );
    const { optionsData, totalFee } = await this.processOptions(reservationDto, reservationDetailForEmail, packageFee);
    reservationData.totalFee = totalFee;
    reservationData.options = optionsData;
    return { reservationData, reservationPackageData, reservationDetailForEmail };
  }

  async processPackages(
    reservationDto: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
    reservationId: string,
  ) {
    const packages = await this.packageService.getEnablePackages(reservationDto.packType);
    const packagesData = [];
    const reservationPackageData = [];
    const reservationDetailForEmail = [];
    let packageFee = 0;

    for (const pack of reservationDto.packages) {
      const packageDetail = packages.find((p) => p.id === pack.id);
      if (!packageDetail) {
        throw new BadRequestException(ERROR_MESSAGE.INVALID_PACKAGE);
      }
      if (packageDetail.status === PackageStatus.DISABLE || packageDetail.outOfStock) {
        throw new BadRequestException(ERROR_MESSAGE.PACKAGE_OUT_OF_STOCK);
      }
      const duplicatePackage = packagesData.find((p) => p.packageId === pack.id);
      if (duplicatePackage) {
        throw new BadRequestException(ERROR_MESSAGE.INVALID_DUPLICATE_PACKAGE);
      }
      packagesData.push({
        packageId: pack.id,
        quantity: pack.quantity,
        fee: packageDetail.fee * pack.quantity,
      });

      reservationDetailForEmail.push({
        name: packageDetail.name,
        quantity: pack.quantity,
        totalFee: formatCurrency(packageDetail.fee * pack.quantity),
        fee: formatCurrency(packageDetail.fee),
      });
      reservationPackageData.push({
        reservationId,
        packageId: pack.id,
        quantity: pack.quantity,
        packPrice: packageDetail.fee,
      });
      packageFee += packageDetail.fee * pack.quantity;
    }

    return { packagesData, reservationPackageData, reservationDetailForEmail, packageFee };
  }

  async processOptions(
    reservationDto: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
    reservationDetailForEmail: { [key: string]: string | number }[],
    totalFee: number,
  ) {
    const reservationOptions = await this.reservationOptionService.getEnableReservationOptions(reservationDto.packType);
    const optionsData = {};
    const packagesCount = reservationDto.packages.reduce((acc, pack) => acc + pack.quantity, 0);

    for (const option of reservationOptions) {
      const key = option.name;
      if (reservationDto[key]) {
        if (option.type === ReservationOptionType.QUANTITY) {
          const quantity = reservationDto[key];
          if (quantity < 0 || quantity > option.detail.maxQuantity) {
            throw new BadRequestException(ERROR_MESSAGE.INVALID_PACKAGE_QUANTITY);
          }
          optionsData[key] = { quantity: reservationDto[key], fee: option.detail.pricePerUnit * reservationDto[key] };
          reservationDetailForEmail.push({
            name: option.title,
            quantity: reservationDto[key],
            totalFee: formatCurrency(option.detail.pricePerUnit * reservationDto[key]),
            fee: formatCurrency(option.detail.pricePerUnit),
          });
          totalFee += option.detail.pricePerUnit * reservationDto[key];
        }

        if (option.type === ReservationOptionType.TEXT) {
          const optionDetail = option.detail.listOptions.find((opt) => opt.title === reservationDto[key]);
          if (!optionDetail) {
            throw new BadRequestException(ERROR_MESSAGE.INVALID_OPTION);
          }
          optionsData[key] = { value: reservationDto[key] };
          if (option.hasPrice && optionDetail.price > 0) {
            const price = optionDetail.price;
            optionsData[key].fee = price;
            let title = optionDetail.title;
            let fee = price;
            let quantity = 0; //not show quantity in email
            if (key === 'productDeliveryType') {
              title = '当日持込料';
            } else if (key === 'extendedWarranty') {
              title = `延長保証`;
              fee = optionDetail.price * packagesCount;
              quantity = packagesCount;
            }
            totalFee += fee;
            reservationDetailForEmail.push({
              name: title,
              quantity: quantity,
              totalFee: formatCurrency(fee),
              fee: formatCurrency(price),
            });
          }
        }
      }
    }
    return { optionsData, totalFee };
  }

  async updateReservation(id: string, data: UpdateReservationDto) {
    try {
      return await this.reservationRepository.update(
        { id },
        {
          status: data.status,
        },
      );
    } catch (err) {
      return { error: err.message };
    }
  }

  async createAddingDocumentUrl(id: string, templateType: AdditionalInfoTemplate) {
    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        const token = generateToken();
        const additionalInformationId = randomUUID();
        await Promise.all([
          entityManager.insert(AdditionalInformationEntity, {
            id: additionalInformationId,
            verificationToken: token,
            templateType,
            reservationId: id,
          }),
          entityManager.insert(AdditionActivityEntity, {
            additionalInformationId,
            type: AdditionActivityType.CREATE,
          }),
        ]);
        return { data: {} };
      });
    } catch (err) {
      this.logger.log('[createAddingDocumentUrl] error: ', err);
      return { error: err.message };
    }
  }

  async reactiveAddingDocumentUrl(id: string) {
    try {
      return await this.dataSource.transaction(async (entityManager: EntityManager) => {
        await Promise.all([
          entityManager.update(
            AdditionalInformationEntity,
            { id },
            { status: AdditionalInfoStatus.NOT_UPLOADED, isEnable: true, createdAt: new Date() },
          ),
          entityManager.insert(AdditionActivityEntity, {
            additionalInformationId: id,
            type: AdditionActivityType.CREATE,
          }),
        ]);
        return { data: {} };
      });
    } catch (err) {
      this.logger.log('[reactiveAddingDocumentUrl] error: ', err);
      return { error: err.message };
    }
  }
}
