import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsEmail,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { MAX_PACKAGE_QUANTITY } from '@src/common/constants/packages';
import { PackageDto } from './package.dto';
import { plainToInstance, Transform, Type } from 'class-transformer';

export class CreateAirconReservationDto {
  @IsString()
  @IsNotEmpty()
  idempotencyKey: string;

  @IsString()
  @IsDateString()
  @IsNotEmpty()
  workingDate1: string;

  @IsString()
  @IsDateString()
  @IsNotEmpty()
  workingDate2: string;

  @ValidateNested()
  @Type(() => PackageDto)
  @Transform(({ value }) => {
    try {
      const parsed = typeof value === 'string' ? JSON.parse(value) : value;
      return Array.isArray(parsed) ? plainToInstance(PackageDto, parsed) : [];
    } catch {
      return [];
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(MAX_PACKAGE_QUANTITY.AIRCON_INSTALL)
  packages: PackageDto[];

  @IsString()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  lastname: string;

  @IsString()
  @IsNotEmpty()
  firstname: string;

  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsNotEmpty()
  postalcode: string;

  @IsString()
  @IsNotEmpty()
  prefecture: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  existingAirConditionerRemoval: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  existingAirConditionerRetrieval: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  indoorPipeDecorativeCover: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  outdoorPipeDecorativeCover: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  outdoorUnitMountWithBrackets: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  twoLevelOutdoorUnitWithBrackets: number;

  @IsString()
  @IsNotEmpty()
  productDeliveryType: string;

  @IsString()
  @IsNotEmpty()
  extendedWarranty: string;
}
