import { Transform, Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional } from 'class-validator';
import { ReservationStatus } from '../../../../common/enums/reservation';

export class UpdateReservationDto {
  @Transform((data) => (!data.value ? undefined : parseInt(data.value)))
  @IsInt()
  @Type(() => Number)
  @IsIn(Object.values(ReservationStatus))
  @IsOptional()
  status?: number;
}
