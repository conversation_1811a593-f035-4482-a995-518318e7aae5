import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { MAX_PACKAGE_QUANTITY } from '@common/constants/packages';
import { PackageDto } from './package.dto';
import { plainToInstance, Transform, Type } from 'class-transformer';

export class CreateBuiltInDishwasherReservationDto {
  @IsString()
  @IsNotEmpty()
  idempotencyKey: string;

  @IsString()
  @IsDateString()
  @IsNotEmpty()
  workingDate1: string;

  @IsString()
  @IsDateString()
  @IsNotEmpty()
  workingDate2: string;

  @ValidateNested()
  @Type(() => PackageDto)
  @Transform(({ value }) => {
    try {
      const parsed = typeof value === 'string' ? JSON.parse(value) : value;
      return Array.isArray(parsed) ? plainToInstance(PackageDto, parsed) : [];
    } catch {
      return [];
    }
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(MAX_PACKAGE_QUANTITY.BUILD_IN_DISHWASHER)
  packages: PackageDto[];

  @IsString()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  color: string;

  @IsString()
  @IsNotEmpty()
  lastname: string;

  @IsString()
  @IsNotEmpty()
  firstname: string;

  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsNotEmpty()
  postalcode: string;

  @IsString()
  @IsNotEmpty()
  prefecture: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  extendedWarranty: string;

  @IsString()
  @IsNotEmpty()
  productDeliveryType: string;
}
