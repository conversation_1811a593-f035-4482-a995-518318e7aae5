import { Injectable, Logger } from '@nestjs/common';
import { google } from 'googleapis';
import { CreateAirconReservationDto } from '../reservation/dtos/create-aircon-reservation.dto';
import * as dayjs from 'dayjs';
import { formatCurrency } from '@src/common/utils/format';
import { SpreadSheetIdMapping, SpreadSheetMetadataMapping } from '@src/common/constants/reservations';
import { MaxPackageQuantityMapping } from '@src/common/constants/packages';
import { CreateHeaterReservationDto } from '../reservation/dtos/create-heater-reservation.dto';
import { PackType } from '@src/common/enums/pack-type';
import { CreateDishwasherReservationDto } from '../reservation/dtos/create-dishwasher-reservation.dto';
import { AdditionalInformationHotWaterEnergySaving2025Dto } from '../additional-information/dtos/additional-information-hot-water-energy-saving-2025.dto';
import { AdditionInformationCountertopDishwasherDto } from '../additional-information/dtos/addition-information-countertop-dishwasher.dto';
import { AdditionalInformationExistingEcoCuteInfoDto } from '../additional-information/dtos/additional-information-existing-ecocute-info.dto';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';
import {
  AdditionalInfoSpreadSheetIdMapping,
  SpreadSheetAdditionInformationMapping,
} from '@src/common/constants/additional-information';

@Injectable()
export class SpreadsheetService {
  protected auth;
  protected authClient;
  private readonly logger = new Logger(SpreadsheetService.name);
  constructor() {
    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
    this.auth = new google.auth.GoogleAuth({
      credentials: {
        ...credentials,
        private_key: credentials.private_key.replace(/\\n/g, '\n'),
      },
      scopes: 'https://www.googleapis.com/auth/spreadsheets',
    });
  }

  public async initGoogleAuthClient(): Promise<void> {
    if (!this.authClient) {
      this.authClient = await this.auth.getClient();
    }
  }

  async saveDataOnGoogleSheet(dataToAppend: (string | number)[], spreadsheetId: string, sheetRange: string) {
    await this.initGoogleAuthClient();
    this.logger.log('[saveDataOnGoogleSheet] init client auth');
    const sheets = google.sheets({ version: 'v4', auth: this.authClient });
    this.logger.log('[saveDataOnGoogleSheet] get sheets');
    const result = await sheets.spreadsheets.values.append({
      spreadsheetId: spreadsheetId,
      range: sheetRange,
      valueInputOption: 'USER_ENTERED',
      insertDataOption: 'INSERT_ROWS',
      requestBody: {
        values: [dataToAppend],
      },
    });
    return result;
  }

  async saveReservationDataOnGoogleSheet(
    data: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
    code: string,
  ) {
    try {
      const dataToAppend = this.prepareCommonData(data, code);
      this.appendPackTypeSpecificData(data, dataToAppend);

      const spreadsheetId = SpreadSheetIdMapping[data.packType];
      const sheetRange = SpreadSheetMetadataMapping[data.packType];
      const result = await this.saveDataOnGoogleSheet(dataToAppend, spreadsheetId, sheetRange);
      this.logger.log('[saveReservationDataOnGoogleSheet] append data', { result });
    } catch (err) {
      this.logger.log('[saveReservationDataOnGoogleSheet] error', { error: err });
      throw err;
    }
  }

  prepareCommonData(
    data: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
    code: string,
  ) {
    const maxPackageQuantity = MaxPackageQuantityMapping[data.packType];
    const packagesData = Array.from({ length: maxPackageQuantity }, (_, i) => {
      const packageData = data.packages[i];
      if (packageData) {
        return [`${packageData.name} （${formatCurrency(packageData.fee)} /台）`, packageData.quantity];
      }
      return ['', ''];
    }).flat();

    return [
      code,
      dayjs().format('YYYY/MM/DD HH:mm'),
      data.lastname,
      data.firstname,
      data.email,
      `'${data.phoneNumber}`,
      dayjs(data.workingDate1).format('YYYY/MM/DD'),
      dayjs(data.workingDate2).format('YYYY/MM/DD'),
      ...packagesData,
    ];
  }

  appendPackTypeSpecificData(
    data: (CreateAirconReservationDto | CreateHeaterReservationDto | CreateDishwasherReservationDto) & {
      userId: string;
      packType: number;
    },
    dataToAppend: (string | number)[],
  ) {
    switch (data.packType) {
      case PackType.AIRCON_INSTALL:
        this.appendAirconInstallData(data as CreateAirconReservationDto, dataToAppend);
        break;
      case PackType.WATER_HEATER:
        this.appendHeaterData(data as CreateHeaterReservationDto, dataToAppend);
        break;
      case PackType.DISHWASHER:
        this.appendDishwasherData(data as CreateDishwasherReservationDto, dataToAppend);
        break;
    }
  }

  appendAirconInstallData(data: CreateAirconReservationDto, dataToAppend: (string | number)[]) {
    dataToAppend.push(`'${data.postalcode}`);
    dataToAppend.push(data.prefecture);
    dataToAppend.push(data.city);
    dataToAppend.push(data.address);
    dataToAppend.push(data.existingAirConditionerRemoval);
    dataToAppend.push(data.existingAirConditionerRetrieval);
    dataToAppend.push(data.indoorPipeDecorativeCover);
    dataToAppend.push(data.outdoorPipeDecorativeCover);
    dataToAppend.push(data.outdoorUnitMountWithBrackets);
    dataToAppend.push(data.twoLevelOutdoorUnitWithBrackets);
    dataToAppend.push(data.productDeliveryType);
    dataToAppend.push(data.extendedWarranty);
  }

  appendHeaterData(data: CreateHeaterReservationDto, dataToAppend: (string | number)[]) {
    dataToAppend.push(`'${data.postalcode}`);
    dataToAppend.push(data.prefecture);
    dataToAppend.push(data.city);
    dataToAppend.push(data.address);
  }

  appendDishwasherData(data: CreateDishwasherReservationDto, dataToAppend: (string | number)[]) {
    dataToAppend.push(data.productDeliveryType);
    dataToAppend.push(`'${data.postalcode}`);
    dataToAppend.push(data.prefecture);
    dataToAppend.push(data.city);
    dataToAppend.push(data.address);
  }

  async saveAdditionalInformationOnGoogleSheet(
    data:
      | AdditionalInformationHotWaterEnergySaving2025Dto
      | AdditionInformationCountertopDishwasherDto
      | AdditionalInformationExistingEcoCuteInfoDto,
    templateType: AdditionalInfoTemplate,
    reservationCode: string,
  ) {
    try {
      const dataToAppend = this.createAdditionalInformationAppendData(data, templateType, reservationCode);
      if (!dataToAppend.length) {
        return;
      }
      const spreadsheetId = AdditionalInfoSpreadSheetIdMapping[templateType];
      const sheetRange = SpreadSheetAdditionInformationMapping[templateType];
      const result = await this.saveDataOnGoogleSheet(dataToAppend, spreadsheetId, sheetRange);
      this.logger.log('[saveAdditionalInformation] append data', { result });
    } catch (err) {
      this.logger.log('[saveAdditionalInformation] error', { error: err });
      throw err;
    }
  }

  createAdditionalInformationAppendData(
    data:
      | AdditionalInformationHotWaterEnergySaving2025Dto
      | AdditionInformationCountertopDishwasherDto
      | AdditionalInformationExistingEcoCuteInfoDto,
    templateType: AdditionalInfoTemplate,
    reservationCode: string,
  ) {
    switch (templateType) {
      case AdditionalInfoTemplate.HotWaterEnergySaving2025:
        return this.appendAdditionalHotWaterEnergySaving2025(
          reservationCode,
          data as AdditionalInformationHotWaterEnergySaving2025Dto,
        );
      case AdditionalInfoTemplate.CountertopDishwasher:
        return this.appendAdditionalCountertopDishwasher(
          reservationCode,
          data as AdditionInformationCountertopDishwasherDto,
        );
      case AdditionalInfoTemplate.ExistingEcoCuteInfoForm:
        return this.appendAdditionalExistingEcoCuteInfo(
          reservationCode,
          data as AdditionalInformationExistingEcoCuteInfoDto,
        );
      default:
        return [];
    }
  }

  appendAdditionalHotWaterEnergySaving2025(
    reservationCode: string,
    data: AdditionalInformationHotWaterEnergySaving2025Dto,
  ) {
    return [
      reservationCode,
      data.bankName,
      data.branchName,
      data.accountType,
      data.accountNumber,
      data.accountHolderNameKana,
      dayjs().format('YYYY/MM/DD HH:mm'),
    ];
  }

  appendAdditionalCountertopDishwasher(reservationCode: string, data: AdditionInformationCountertopDishwasherDto) {
    return [reservationCode, `'${data.faucetModelNumber}`, dayjs().format('YYYY/MM/DD HH:mm')];
  }

  appendAdditionalExistingEcoCuteInfo(reservationCode: string, data: AdditionalInformationExistingEcoCuteInfoDto) {
    // Calculate number of EcoCute units (each unit requires 2 images: nameplate + full view)
    const ecoCuteCount = Math.ceil(data.listFileNames.length / 2);
    const fileCount = data.listFileNames.length;
    const fileNames = data.listFileNames.map((f) => f.name).join(', ');

    return [reservationCode, ecoCuteCount, fileCount, fileNames, dayjs().format('YYYY/MM/DD HH:mm')];
  }
}
