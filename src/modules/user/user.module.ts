import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { QueueProvider } from '../../providers/queue.provider';
import { ReservationModule } from '../shared/reservation/reservation.module';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { AirconInstallController } from './controllers/aircon-install.controller';
import { ConfigurationModule } from '../shared/configuration/configuration.module';
import { CalendarBlockModule } from '../shared/calendar-block/calendar-block.module';
import { PackageModule } from '../shared/package/package.module';
import { ReservationOptionModule } from '../shared/reservation-option/reservation-option.module';
import { SessionManagerMiddleware } from '@vietnam/cnga-middleware';
import { WaterHeaterController } from './controllers/water-heater.controller';
import { DishwasherController } from './controllers/dishwasher.controller';
import { AdditionalInformationController } from './controllers/additional-information.controller';
import { AdditionalInformationModule } from '../shared/additional-information/additional-information.module';
import { BuiltInDishwasherStandardController } from './controllers/built-in-dishwasher-standard.controller';
import { BuiltInDishwasherUnspecifiedController } from './controllers/built-in-dishwasher-unspecified.controller';

@Module({
  imports: [
    QueueProvider.register(),
    CuramaPy3ApiModule,
    ReservationModule,
    ConfigurationModule,
    CalendarBlockModule,
    PackageModule,
    ReservationOptionModule,
    AdditionalInformationModule,
  ],
  controllers: [
    AirconInstallController,
    WaterHeaterController,
    DishwasherController,
    BuiltInDishwasherUnspecifiedController,
    BuiltInDishwasherStandardController,
    AdditionalInformationController,
  ],
  providers: [],
  exports: [],
})
export class UserModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(SessionManagerMiddleware).forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });
  }
}
