import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Post,
  Render,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ERROR_MESSAGE } from '@src/common/constants';
import { MAX_PACKAGE_QUANTITY, DEFAULT_DISCOUNT_WATER_HEATER } from '@src/common/constants/packages';
import { PageUserSetup } from '@src/common/decorators/page-user-setup.decorator';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { AuthenticationRedirectGuard } from '@src/common/guards/authentication-redirect.guard';
import { RequireUserInterceptor } from '@src/common/interceptors/require-user.interceptor';
import { UserRequestInterceptor } from '@src/common/interceptors/user-request.interceptor';
import { routes } from '@src/common/routes';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { CreateHeaterReservationDto } from '@src/modules/shared/reservation/dtos/create-heater-reservation.dto';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ACCOUNT_TYPES, AccountTypeAuthorization } from '@vietnam/cnga-middleware';
import * as dayjs from 'dayjs';
import { formatCurrency, numberWithCommas } from '@src/common/utils/format';
import { findMinValidDate, getAllDatesBetween, YYYYMD } from '@src/common/utils/date-util';
import { PackType } from '@src/common/enums/pack-type';
import { PackageStatus } from '@src/common/enums/package';
import { randomUUID } from 'crypto';
import { VIEW_TEMPLATES } from '@src/common/views';
import { EMAIL_TEMPLATE_NAME } from '@src/common/constants/reservations';

@Controller()
export class WaterHeaterController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly configurationService: ConfigurationService,
    private readonly calendarBlockService: CalendarBlockService,
    private readonly packageService: PackageService,
    private readonly reservationOptionService: ReservationOptionService,
  ) {}
  @Post(routes.user.waterHeaterPack.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async register(@Req() req, @Body() body) {
    try {
      const packages = await this.packageService.getEnablePackages(PackType.WATER_HEATER);
      const selectedPackage = body.forms?.packages;
      if (!selectedPackage || !selectedPackage.length) {
        throw new BadRequestException(ERROR_MESSAGE.INVALID_PACKAGE);
      }
      for (const pack of selectedPackage) {
        const packageDetail = packages.find((p) => p.id === pack.id);
        if (!packageDetail || packageDetail.status === PackageStatus.DISABLE || packageDetail.outOfStock) {
          throw new BadRequestException(ERROR_MESSAGE.PACKAGE_OUT_OF_STOCK);
        }
      }
      req.session.waterHeaterPackReservationForm = body;
      return {};
    } catch (err) {
      return { error: err.message };
    }
  }

  @Get(routes.user.waterHeaterPack.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.waterHeater.newReservation)
  @PageUserSetup({
    title: 'エコキュート交換工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'エコキュート交換工事パックの予約フォーム。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      meta_ogTitle: 'エコキュート交換工事パックの予約フォーム',
      meta_ogDescription:
        'エコキュート交換工事パックの予約フォーム。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.newReservationPage}`,
    },
  })
  async heaterReservationPage(@Req() req) {
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.WATER_HEATER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.WATER_HEATER,
      }),
      this.packageService.getEnablePackages(PackType.WATER_HEATER),
      this.reservationOptionService.getEnableReservationOptions(PackType.WATER_HEATER),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      randomIdempotencyKey: randomUUID(),
      reservationThankPageUrl: routes.user.waterHeaterPack.reservationThankPage,
      reservationConfirmPageUrl: routes.user.waterHeaterPack.reservationConfirmPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.WATER_HEATER,
      waterHeaterDiscount: DEFAULT_DISCOUNT_WATER_HEATER,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }
  @Get(routes.user.waterHeaterPack.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.waterHeater.confirmReservation)
  @PageUserSetup({
    title: 'エコキュート交換工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'エコキュート交換工事パックの予約フォーム。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      meta_ogTitle: 'エコキュート交換工事パックの予約フォーム',
      meta_ogDescription:
        'エコキュート交換工事パックの予約フォーム。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.reservationConfirmPage}`,
    },
  })
  async reservationConfirmPage(@Req() req) {
    const currentForm = req.session.waterHeaterPackReservationForm;
    if (!currentForm) {
      throw new NotFoundException();
    }
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.WATER_HEATER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.WATER_HEATER,
      }),
      this.packageService.getEnablePackages(PackType.WATER_HEATER),
      this.reservationOptionService.getEnableReservationOptions(PackType.WATER_HEATER),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      currentForm,
      reservationThankPageUrl: routes.user.waterHeaterPack.reservationThankPage,
      reservationPageUrl: routes.user.waterHeaterPack.newReservationPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.WATER_HEATER,
      waterHeaterDiscount: DEFAULT_DISCOUNT_WATER_HEATER,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }
  @Post(routes.user.waterHeaterPack.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async confirm(@Req() req, @Res() res, @Body() body: CreateHeaterReservationDto) {
    if (!req.session.waterHeaterPackReservationForm) {
      throw new NotFoundException();
    }
    const data = await this.reservationService.createReservation(
      {
        ...body,
        packType: PackType.WATER_HEATER,
        userId: req.user?.clientId,
        mailTemplate: EMAIL_TEMPLATE_NAME.WATER_HEATER,
      },
      req,
    );
    req.session.waterHeaterPackReservationCode = data.code;
    return res.redirect(routes.user.waterHeaterPack.reservationThankPage);
  }
  @Get(routes.user.waterHeaterPack.reservationThankPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.waterHeater.thank)
  @PageUserSetup({
    title: 'エコキュート交換工事パックの予約リクエスト完了ページ',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'エコキュート交換工事パックの予約リクエスト完了ページ。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      meta_ogTitle: 'エコキュート交換工事パックの予約リクエスト完了ページ',
      meta_ogDescription:
        'エコキュート交換工事パックの予約リクエスト完了ページ。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.reservationThankPage}`,
    },
  })
  async reservationThankPage(@Req() req) {
    const waterHeaterPackReservationCode = req.session.waterHeaterPackReservationCode;
    const currentForm = req.session.waterHeaterPackReservationForm;
    if (!waterHeaterPackReservationCode || !currentForm) {
      throw new NotFoundException();
    }
    const activePackages = await this.packageService.getEnablePackages(PackType.WATER_HEATER);
    delete req.session.waterHeaterPackReservationForm;
    delete req.session.waterHeaterPackReservationCode;
    return {
      reservationCode: waterHeaterPackReservationCode,
      currentForm,
      activePackages,
    };
  }

  @Get(routes.user.waterHeaterPack.landingPage)
  @UseInterceptors(UserRequestInterceptor)
  @Render(VIEW_TEMPLATES.user.waterHeater.landingPage)
  @PageUserSetup({
    title: '市場最安級！エコキュート交換工事パック ¥360,900〜',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        '高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      meta_ogTitle: '市場最安級！エコキュート交換工事パック ¥360,900〜',
      meta_ogDescription:
        '高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
      meta_ogImage: `${process.env.APP_URL?.replace('https:', '')}/${
        process.env.APP_STATIC_PREFIX
      }/image/user/lp-water-heater/bnr_water-heater-pack_ogp.jpg`,
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.landingPage}`,
    },
  })
  async landingWaterHeaterPage() {
    const activePackages = await this.packageService.getEnablePackages(PackType.WATER_HEATER);
    return {
      activePackages: activePackages,
      waterHeaterDiscount: DEFAULT_DISCOUNT_WATER_HEATER,
      numberWithCommas,
    };
  }
}
