import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Post,
  Render,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ERROR_MESSAGE } from '@src/common/constants';
import { MAX_PACKAGE_QUANTITY } from '@src/common/constants/packages';
import { PageUserSetup } from '@src/common/decorators/page-user-setup.decorator';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { AuthenticationRedirectGuard } from '@src/common/guards/authentication-redirect.guard';
import { RequireUserInterceptor } from '@src/common/interceptors/require-user.interceptor';
import { UserRequestInterceptor } from '@src/common/interceptors/user-request.interceptor';
import { routes } from '@src/common/routes';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ACCOUNT_TYPES, AccountTypeAuthorization } from '@vietnam/cnga-middleware';
import * as dayjs from 'dayjs';
import { formatCurrency, numberWithCommas } from '@src/common/utils/format';
import { findMinValidDate, getAllDatesBetween, YYYYMD } from '@src/common/utils/date-util';
import { PackType } from '@src/common/enums/pack-type';
import { VIEW_TEMPLATES } from '@src/common/views';
import { CreateDishwasherReservationDto } from '@src/modules/shared/reservation/dtos/create-dishwasher-reservation.dto';
import { PackageStatus } from '@src/common/enums/package';
import { randomUUID } from 'crypto';

@Controller()
export class DishwasherController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly configurationService: ConfigurationService,
    private readonly calendarBlockService: CalendarBlockService,
    private readonly packageService: PackageService,
    private readonly reservationOptionService: ReservationOptionService,
  ) {}

  @Post(routes.user.dishwasherPack.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async register(@Req() req, @Body() body) {
    try {
      const packages = await this.packageService.getEnablePackages(PackType.DISHWASHER);
      const selectedPackage = body.forms?.packages;
      if (!selectedPackage || !selectedPackage.length) {
        throw new BadRequestException(ERROR_MESSAGE.INVALID_PACKAGE);
      }
      for (const pack of selectedPackage) {
        const packageDetail = packages.find((p) => p.id === pack.id);
        if (!packageDetail || packageDetail.status === PackageStatus.DISABLE || packageDetail.outOfStock) {
          throw new BadRequestException(ERROR_MESSAGE.PACKAGE_OUT_OF_STOCK);
        }
      }
      req.session.dishwasherPackReservationForm = body;
      return {};
    } catch (err) {
      return { error: err.message };
    }
  }

  @Get(routes.user.dishwasherPack.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.dishwasher.newReservation)
  @PageUserSetup({
    title: '卓上食洗機 給水不要パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      meta_ogTitle: '卓上食洗機 給水不要パックの予約フォーム',
      meta_ogDescription:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      link_canonical: `${process.env.APP_URL}${routes.user.dishwasherPack.newReservationPage}`,
    },
  })
  async dishwasherReservationPage(@Req() req) {
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.DISHWASHER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.DISHWASHER,
      }),
      this.packageService.getEnablePackages(PackType.DISHWASHER),
      this.reservationOptionService.getEnableReservationOptions(PackType.DISHWASHER),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      randomIdempotencyKey: randomUUID(),
      reservationThankPageUrl: routes.user.dishwasherPack.reservationThankPage,
      reservationConfirmPageUrl: routes.user.dishwasherPack.reservationConfirmPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.DISHWASHER,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }

  @Get(routes.user.dishwasherPack.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.dishwasher.confirmReservation)
  @PageUserSetup({
    title: '卓上食洗機 給水不要パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      meta_ogTitle: '卓上食洗機 給水不要パックの予約フォーム',
      meta_ogDescription:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      link_canonical: `${process.env.APP_URL}${routes.user.dishwasherPack.reservationConfirmPage}`,
    },
  })
  async reservationConfirmPage(@Req() req) {
    const currentForm = req.session.dishwasherPackReservationForm;
    if (!currentForm) {
      throw new NotFoundException();
    }
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.DISHWASHER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.DISHWASHER,
      }),
      this.packageService.getEnablePackages(PackType.DISHWASHER),
      this.reservationOptionService.getEnableReservationOptions(PackType.DISHWASHER),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      currentForm,
      reservationThankPageUrl: routes.user.dishwasherPack.reservationThankPage,
      reservationPageUrl: routes.user.dishwasherPack.newReservationPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.DISHWASHER,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }

  @Post(routes.user.dishwasherPack.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async confirm(@Req() req, @Res() res, @Body() body: CreateDishwasherReservationDto) {
    if (!req.session.dishwasherPackReservationForm) {
      throw new NotFoundException();
    }
    const data = await this.reservationService.createReservation(
      {
        ...body,
        packType: PackType.DISHWASHER,
        userId: req.user?.clientId,
      },
      req,
    );
    req.session.dishwasherPackReservationCode = data.code;
    return res.redirect(routes.user.dishwasherPack.reservationThankPage);
  }

  @Get(routes.user.dishwasherPack.reservationThankPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.dishwasher.thank)
  @PageUserSetup({
    title: '卓上食洗機 給水不要パックの予約リクエスト完了ページ',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      meta_ogTitle: '卓上食洗機 給水不要パックの予約リクエスト完了ページ',
      meta_ogDescription:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      link_canonical: `${process.env.APP_URL}${routes.user.dishwasherPack.reservationThankPage}`,
    },
  })
  async reservationThankPage(@Req() req) {
    const dishwasherPackReservationCode = req.session.dishwasherPackReservationCode;
    const currentForm = req.session.dishwasherPackReservationForm;
    if (!dishwasherPackReservationCode || !currentForm) {
      throw new NotFoundException();
    }
    const activePackages = await this.packageService.getEnablePackages(PackType.DISHWASHER);
    delete req.session.dishwasherPackReservationForm;
    delete req.session.dishwasherPackReservationCode;
    return {
      reservationCode: dishwasherPackReservationCode,
      currentForm,
      activePackages,
    };
  }

  @Get(routes.user.dishwasherPack.landingPage)
  @UseInterceptors(UserRequestInterceptor)
  @Render(VIEW_TEMPLATES.user.dishwasher.landingPage)
  @PageUserSetup({
    title: 'siroca シロカ卓上食洗機 給水不要パック - くらしのマーケット',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      meta_ogTitle: 'siroca シロカ卓上食洗機 給水不要パック - くらしのマーケット',
      meta_ogDescription:
        '卓上食洗機でも給水の手間が無くなります。分岐水栓+長期保証+sirocaの卓上食洗機の給水不要パックを、くらしのマーケットがご提供します。',
      meta_ogImage: `${process.env.APP_URL?.replace('https:', '')}/${
        process.env.APP_STATIC_PREFIX
      }/image/user/lp-dishwasher/bnr_ogimage.jpg`,
      link_canonical: `${process.env.APP_URL}${routes.user.dishwasherPack.landingPage}`,
    },
  })
  async landingDishwasherPage() {
    const activePackages = await this.packageService.getEnablePackages(PackType.DISHWASHER);
    return {
      activePackages: activePackages,
      reservationPageUrl: routes.user.dishwasherPack.newReservationPage,
      numberWithCommas,
    };
  }
}
