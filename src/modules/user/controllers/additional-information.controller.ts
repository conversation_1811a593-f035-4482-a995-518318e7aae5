import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Post,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ACCOUNT_TYPES, AccountTypeAuthorization } from '@vietnam/cnga-middleware';
import { PageUserSetup } from '@src/common/decorators/page-user-setup.decorator';
import { AuthenticationRedirectGuard } from '@src/common/guards/authentication-redirect.guard';
import { RequireUserInterceptor } from '@src/common/interceptors/require-user.interceptor';
import { UserRequestInterceptor } from '@src/common/interceptors/user-request.interceptor';
import { numberWithCommas } from '@src/common/utils/format';
import { routes } from '@src/common/routes';
import { renderTemplate } from '@src/common/utils/helper';
import { DynamicRender } from '@src/common/decorators/dynamic-render.decorator';
import { MultiInputFiles } from '@src/common/decorators/multiple-input-files.decorator';
import { AdditionalInformationAccountType, AdditionalInfoStatus } from '@src/common/enums/additional-information';
import { AdditionalInfoViewMapping } from '@src/common/views';
import { AdditionalInformationPageDto } from '@src/modules/shared/additional-information/dtos/additional-information-page.dto';
import { AdditionalInformationService } from '@src/modules/shared/additional-information/additional-information.service';
import { AdditionalInformationHotWaterEnergySaving2025Dto } from '@src/modules/shared/additional-information/dtos/additional-information-hot-water-energy-saving-2025.dto';
import { AdditionalInformationCountertopDishwasherDto } from '@src/modules/shared/additional-information/dtos/additional-information-countertop-dishwasher.dto';
import { AdditionalInformationValidationPipe } from '@src/modules/shared/additional-information/dtos/additional-information-validate.pipe';
import { AdditionalInformationExistingEcoCuteInfoDto } from '@src/modules/shared/additional-information/dtos/additional-information-existing-ecocute-info.dto';
import { AdditionalInformationBuiltInDishwasherDto } from '@src/modules/shared/additional-information/dtos/additional-information-built-in-dishwasher.dto';
import { AdditionalInformationValidationDto } from '@src/modules/shared/additional-information/dtos/additional-information-validation.dto';

@Controller()
export class AdditionalInformationController {
  constructor(private readonly additionalInformationService: AdditionalInformationService) {}

  @Get(routes.user.additionalInformation.uploadPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @DynamicRender()
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @PageUserSetup({
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
  })
  async uploadPage(@Req() req, @Res() response: any, @Query() query: AdditionalInformationPageDto) {
    const additionalInformation = await this.additionalInformationService.getAdditionalInformationOrFail(
      {
        verificationToken: query.token,
      },
      ['reservation'],
    );
    if (additionalInformation.reservation.userId !== req.user.clientId || !additionalInformation.isEnable) {
      throw new NotFoundException('Reservation additional information not found');
    }
    if (additionalInformation.status == AdditionalInfoStatus.UPLOADED) {
      return response.redirect(`${routes.user.additionalInformation.thankPage}?token=${query.token}`);
    }
    const responseData = {
      numberWithCommas,
      AdditionalInformationAccountType,
      templateType: additionalInformation.templateType,
      packType: additionalInformation.reservation.packType,
      addingInformationThankPageUrl: routes.user.additionalInformation.thankPage,
      addingInformationConfirmPageUrl: routes.user.additionalInformation.confirmPage,
      verificationToken: query.token,
    };
    return renderTemplate(AdditionalInfoViewMapping[additionalInformation.templateType].uploadPage, responseData);
  }

  @Post(routes.user.additionalInformation.uploadPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async uploadAdditionalInformation(@Req() req, @Body() body: AdditionalInformationValidationDto) {
    try {
      const additionalInformation = await this.additionalInformationService.getAdditionalInformationOrFail(
        {
          verificationToken: body.verificationToken,
        },
        ['reservation'],
      );
      if (additionalInformation.reservation.userId !== req.user.clientId || !additionalInformation.isEnable) {
        throw new NotFoundException('Reservation additional information not found');
      }
      if (additionalInformation.status === AdditionalInfoStatus.UPLOADED) {
        throw new BadRequestException('Reservation additional information already uploaded');
      }
      return {};
    } catch (err) {
      return { error: err.message };
    }
  }

  @Post(routes.user.additionalInformation.confirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @MultiInputFiles([
    { name: 'identityFiles', fileType: ['image/png', 'image/jpeg', 'image/jpg'], maxCount: 2 },
    { name: 'additionalFiles', fileType: ['image/png', 'image/jpeg', 'image/jpg'], maxCount: 30 },
  ])
  async confirmAdditionalInformation(
    @Req() req,
    @UploadedFiles() files: { identityFiles: Express.Multer.File[]; additionalFiles: Express.Multer.File[] },
    @Body(new AdditionalInformationValidationPipe())
    body:
      | AdditionalInformationHotWaterEnergySaving2025Dto
      | AdditionalInformationExistingEcoCuteInfoDto
      | AdditionalInformationCountertopDishwasherDto
      | AdditionalInformationBuiltInDishwasherDto,
  ) {
    const additionalInformation = await this.additionalInformationService.getAdditionalInformationOrFail(
      {
        verificationToken: body.verificationToken,
      },
      ['reservation'],
    );
    if (additionalInformation.reservation.userId !== req.user.clientId || !additionalInformation.isEnable) {
      throw new NotFoundException('Reservation additional information not found');
    }
    if (additionalInformation.status === AdditionalInfoStatus.UPLOADED) {
      throw new BadRequestException('Reservation additional information already uploaded');
    }
    const inputFiles = [];
    if (files.identityFiles) {
      inputFiles.push(...files.identityFiles);
    }
    if (files.additionalFiles) {
      inputFiles.push(...files.additionalFiles);
    }
    return this.additionalInformationService.uploadAdditionalInformation(
      req.user.clientId,
      inputFiles,
      body,
      additionalInformation.templateType,
    );
  }

  @Get(routes.user.additionalInformation.thankPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @DynamicRender()
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @PageUserSetup({
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
  })
  async thankPage(@Req() req, @Res() res, @Query() query: AdditionalInformationPageDto) {
    const additionalInformation = await this.additionalInformationService.getAdditionalInformationOrFail(
      {
        verificationToken: query.token,
      },
      ['reservation'],
    );
    if (additionalInformation.reservation.userId !== req.user.clientId || !additionalInformation.isEnable) {
      throw new NotFoundException('Reservation additional information not found');
    }
    if (additionalInformation.status !== AdditionalInfoStatus.UPLOADED) {
      return res.redirect(`${routes.user.additionalInformation.uploadPage}?token=${query.token}`);
    }
    return renderTemplate(AdditionalInfoViewMapping[additionalInformation.templateType].thankPage, {});
  }
}
