import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Render,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ERROR_MESSAGE } from '@src/common/constants';
import { MAX_PACKAGE_QUANTITY } from '@src/common/constants/packages';
import { PageUserSetup } from '@src/common/decorators/page-user-setup.decorator';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { AuthenticationRedirectGuard } from '@src/common/guards/authentication-redirect.guard';
import { RequireUserInterceptor } from '@src/common/interceptors/require-user.interceptor';
import { UserRequestInterceptor } from '@src/common/interceptors/user-request.interceptor';
import { routes } from '@src/common/routes';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { CreateAirconReservationDto } from '@src/modules/shared/reservation/dtos/create-aircon-reservation.dto';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ACCOUNT_TYPES, AccountTypeAuthorization } from '@vietnam/cnga-middleware';
import * as dayjs from 'dayjs';
import { formatCurrency, numberWithCommas } from '@src/common/utils/format';
import { findMinValidDate, getAllDatesBetween, YYYYMD } from '@src/common/utils/date-util';
import { PackType } from '@src/common/enums/pack-type';
import { PackageStatus } from '@src/common/enums/package';
import { VIEW_TEMPLATES } from '@src/common/views';
import { AirconPackageDetailParamDto } from '@src/modules/shared/package/dtos/aircon-package-detail.dto';
import { randomUUID } from 'crypto';
import { AirconPackageService } from '@src/modules/shared/package/aircon-package.service';
import { EMAIL_TEMPLATE_NAME } from '@src/common/constants/reservations';

@Controller()
export class AirconInstallController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly configurationService: ConfigurationService,
    private readonly calendarBlockService: CalendarBlockService,
    private readonly packageService: PackageService,
    private readonly airconPackageService: AirconPackageService,
    private readonly reservationOptionService: ReservationOptionService,
  ) {}
  @Post(routes.user.airconInstallPack.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async register(@Req() req, @Body() body) {
    try {
      const packages = await this.packageService.getEnablePackages(PackType.AIRCON_INSTALL);
      const selectedPackage = body.forms?.packages;
      if (!selectedPackage || !selectedPackage.length) {
        throw new BadRequestException(ERROR_MESSAGE.INVALID_PACKAGE);
      }
      for (const pack of selectedPackage) {
        const packageDetail = packages.find((p) => p.id === pack.id);
        if (!packageDetail || packageDetail.status === PackageStatus.DISABLE || packageDetail.outOfStock) {
          throw new BadRequestException(ERROR_MESSAGE.PACKAGE_OUT_OF_STOCK);
        }
      }
      req.session.airconInstallPackReservationForm = body;
      return {};
    } catch (err) {
      return { error: err.message };
    }
  }

  @Get(routes.user.airconInstallPack.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.airconInstall.newReservation)
  @PageUserSetup({
    title: 'エアコン取付工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'エアコン取付工事パックの予約フォーム。一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'エアコン取付工事パックの予約フォーム',
      meta_ogDescription:
        'エアコン取付工事パックの予約フォーム。一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.newReservationPage}`,
    },
  })
  async airconReservationPage(@Req() req) {
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.AIRCON_INSTALL),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.AIRCON_INSTALL,
      }),
      this.packageService.getEnablePackages(PackType.AIRCON_INSTALL),
      this.reservationOptionService.getEnableReservationOptions(PackType.AIRCON_INSTALL),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      randomIdempotencyKey: randomUUID(),
      reservationThankPageUrl: routes.user.airconInstallPack.reservationThankPage,
      reservationConfirmPageUrl: routes.user.airconInstallPack.reservationConfirmPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.AIRCON_INSTALL,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }
  @Get(routes.user.airconInstallPack.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.airconInstall.confirmReservation)
  @PageUserSetup({
    title: 'エアコン取付工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'エアコン取付工事パックの予約フォーム。一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'エアコン取付工事パックの予約フォーム',
      meta_ogDescription:
        'エアコン取付工事パックの予約フォーム。一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.reservationConfirmPage}`,
    },
  })
  async reservationConfirmPage(@Req() req) {
    const currentForm = req.session.airconInstallPackReservationForm;
    if (!currentForm) {
      throw new NotFoundException();
    }
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.AIRCON_INSTALL),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.AIRCON_INSTALL,
      }),
      this.packageService.getEnablePackages(PackType.AIRCON_INSTALL),
      this.reservationOptionService.getEnableReservationOptions(PackType.AIRCON_INSTALL),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      currentForm,
      reservationThankPageUrl: routes.user.airconInstallPack.reservationThankPage,
      reservationPageUrl: routes.user.airconInstallPack.newReservationPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.AIRCON_INSTALL,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }
  @Post(routes.user.airconInstallPack.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async confirm(@Req() req, @Res() res, @Body() body: CreateAirconReservationDto) {
    if (!req.session.airconInstallPackReservationForm) {
      throw new NotFoundException();
    }
    const data = await this.reservationService.createReservation(
      {
        ...body,
        packType: PackType.AIRCON_INSTALL,
        userId: req.user?.clientId,
        mailTemplate: EMAIL_TEMPLATE_NAME.AIRCON_INSTALL,
      },
      req,
    );
    req.session.airconInstallPackReservationCode = data.code;
    return res.redirect(routes.user.airconInstallPack.reservationThankPage);
  }
  @Get(routes.user.airconInstallPack.reservationThankPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.airconInstall.thank)
  @PageUserSetup({
    title: 'エアコン取付工事パックの予約リクエスト完了ページ',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'エアコン取付工事パックの予約リクエスト完了ページ。一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'エアコン取付工事パックの予約リクエスト完了ページ',
      meta_ogDescription:
        'エアコン取付工事パックの予約リクエスト完了ページ。一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.waterHeaterPack.reservationThankPage}`,
    },
  })
  async reservationThankPage(@Req() req) {
    const airconInstallPackReservationCode = req.session.airconInstallPackReservationCode;
    const currentForm = req.session.airconInstallPackReservationForm;
    if (!airconInstallPackReservationCode || !currentForm) {
      throw new NotFoundException();
    }
    const activePackages = await this.packageService.getEnablePackages(PackType.AIRCON_INSTALL);
    delete req.session.airconInstallPackReservationForm;
    delete req.session.airconInstallPackReservationCode;
    return {
      reservationCode: airconInstallPackReservationCode,
      currentForm,
      activePackages,
    };
  }

  @Get(routes.user.airconInstallPack.landingPage)
  @UseInterceptors(UserRequestInterceptor)
  @Render(VIEW_TEMPLATES.user.airconInstall.landingPage)
  @PageUserSetup({
    title: '最新エアコンが安い！エアコン取付工事パック ¥74,330〜',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        '一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！¥74,330〜。',
      meta_ogTitle: '最新エアコンが安い！エアコン取付工事パック ¥74,330〜',
      meta_ogDescription:
        '一括仕入れにより最新エアコンが安い！くらしのマーケットでは、パナソニック、ダイキン、三菱電機などの人気エアコンを市場最安値級でご提供。エアコン購入と取付工事をまとめて簡単予約！¥74,330〜。',
      meta_ogImage: `${process.env.APP_URL?.replace('https:', '')}/${
        process.env.APP_STATIC_PREFIX
      }/image/user/lp-aircon-install/bnr_ogimage.png`,
      link_canonical: `${process.env.APP_URL}${routes.user.airconInstallPack.landingPage}`,
    },
  })
  async landingAirconInstallPage() {
    const activePackages = await this.packageService.getEnablePackages(PackType.AIRCON_INSTALL);
    const { categoryTraits, productsJsonLd } = this.airconPackageService.getCategorySpecifications(activePackages);
    return {
      productsJsonLd,
      categoryTraits,
      activePackages,
      reservationPageUrl: routes.user.airconInstallPack.newReservationPage,
      landingPageUrl: routes.user.airconInstallPack.landingPage,
      numberWithCommas,
    };
  }

  @Get(routes.user.airconInstallPack.packageDetailPage)
  @UseInterceptors(UserRequestInterceptor)
  @Render(VIEW_TEMPLATES.user.airconInstall.packageDetail)
  @PageUserSetup({
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
  })
  async landingAirconPackageDetailPage(@Param() param: AirconPackageDetailParamDto) {
    const { modelId } = param;
    const categoryTraits = this.airconPackageService.getCategoryTraits();
    const category = categoryTraits.find((trait) => {
      const modelIds = trait.packages.map((p) => p.modelId);
      return modelIds.includes(modelId);
    });
    if (!category) {
      throw new NotFoundException();
    }
    const packageTrait = category.packages.find((p) => p.modelId === modelId);
    const activePackages = await this.packageService.getEnablePackages(PackType.AIRCON_INSTALL);
    const packageData = activePackages.find((p) => p.id === packageTrait.id);
    if (!packageData) {
      throw new NotFoundException();
    }
    const title = `${category.brandJp} ${category.seriesJp} ${packageTrait.tatamiSize}畳用 ¥${numberWithCommas(
      packageData.fee,
    )}〜`;
    const description = category.seoDetailDescription
      .replace('{{price}}', numberWithCommas(packageData.fee))
      .replace('{{tatamiSize}}', packageTrait.tatamiSize.toString());
    return {
      title: title,
      meta: {
        meta_description: description,
        meta_ogTitle: title,
        meta_ogDescription: description,
        meta_ogImage: `${process.env.APP_URL.replace('https:', '')}/${process.env.APP_STATIC_PREFIX}/${
          packageTrait.image
        }`,
        link_canonical: `${process.env.APP_URL}${routes.user.airconInstallPack.landingPage}${packageTrait.modelId}/`,
      },
      categoryTrait: category,
      modelId,
      packageData,
      activePackages,
      reservationPageUrl: routes.user.airconInstallPack.newReservationPage,
      landingPageUrl: routes.user.airconInstallPack.landingPage,
      numberWithCommas,
    };
  }
}
