import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Post,
  Render,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ERROR_MESSAGE } from '@src/common/constants';
import { PageUserSetup } from '@src/common/decorators/page-user-setup.decorator';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { AuthenticationRedirectGuard } from '@src/common/guards/authentication-redirect.guard';
import { RequireUserInterceptor } from '@src/common/interceptors/require-user.interceptor';
import { UserRequestInterceptor } from '@src/common/interceptors/user-request.interceptor';
import { routes } from '@src/common/routes';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { PackageService } from '@src/modules/shared/package/package.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ACCOUNT_TYPES, AccountTypeAuthorization } from '@vietnam/cnga-middleware';
import * as dayjs from 'dayjs';
import { formatCurrency } from '@common/utils/format';
import { findMinValidDate, getAllDatesBetween, YYYYMD } from '@src/common/utils/date-util';
import { PackType } from '@common/enums/pack-type';
import { MAX_PACKAGE_QUANTITY } from '@src/common/constants/packages';
import { CreateUnspecifiedReservationDto } from '@src/modules/shared/reservation/dtos/create-unspecified-reservation.dto';
import { DynamicRender } from '@src/common/decorators/dynamic-render.decorator';
import { randomUUID } from 'crypto';
import { VIEW_TEMPLATES } from '@common/views';
import { EMAIL_TEMPLATE_NAME } from '@common/constants/reservations';

@Controller()
export class BuiltInDishwasherUnspecifiedController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly configurationService: ConfigurationService,
    private readonly calendarBlockService: CalendarBlockService,
    private readonly packageService: PackageService,
    private readonly reservationOptionService: ReservationOptionService,
  ) {}

  @Post(routes.user.builtInDishwasherPack.unspecified.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async registerUnspecified(@Req() req, @Body() body) {
    try {
      req.session.builtInDishwasherPackUnspecifiedProductReservationForm = body;
      return {};
    } catch (err) {
      return { error: err.message };
    }
  }

  @Get(routes.user.builtInDishwasherPack.unspecified.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @DynamicRender()
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.builtInDishwasher.unspecified.newReservation)
  @PageUserSetup({
    title: 'ビルトイン食洗機交換工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'ビルトイン食洗機交換工事パックの予約フォーム',
      meta_ogDescription:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.builtInDishwasherPack.unspecified.newReservationPage}`,
    },
  })
  async dishwasherReservationPageUnspecified(@Req() req, @Res() res) {
    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.BUILD_IN_DISHWASHER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.BUILD_IN_DISHWASHER,
      }),
      this.packageService.getEnablePackages(PackType.BUILD_IN_DISHWASHER),
      this.reservationOptionService.getEnableReservationOptions(PackType.BUILD_IN_DISHWASHER),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );

    return {
      randomIdempotencyKey: randomUUID(),
      reservationThankPageUrl: routes.user.builtInDishwasherPack.unspecified.reservationThankPage,
      reservationConfirmPageUrl: routes.user.builtInDishwasherPack.unspecified.reservationConfirmPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.DISHWASHER,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }

  @Get(routes.user.builtInDishwasherPack.unspecified.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @DynamicRender()
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.builtInDishwasher.unspecified.confirmReservation)
  @PageUserSetup({
    title: 'ビルトイン食洗機交換工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'ビルトイン食洗機交換工事パックの予約フォーム',
      meta_ogDescription:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.builtInDishwasherPack.unspecified.reservationConfirmPage}`,
    },
  })
  async reservationConfirmPageUnspecified(@Req() req, @Res() res) {
    const currentForm = req.session.builtInDishwasherPackUnspecifiedProductReservationForm;
    if (!currentForm) {
      throw new NotFoundException();
    }

    const [config, blockedDates, activePackages, reservationOptions] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.BUILD_IN_DISHWASHER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.BUILD_IN_DISHWASHER,
      }),
      this.packageService.getEnablePackages(PackType.BUILD_IN_DISHWASHER),
      this.reservationOptionService.getEnableReservationOptions(PackType.BUILD_IN_DISHWASHER),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );

    return {
      currentForm,
      reservationThankPageUrl: routes.user.builtInDishwasherPack.unspecified.reservationThankPage,
      reservationPageUrl: routes.user.builtInDishwasherPack.unspecified.newReservationPage,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      activePackages,
      reservationOptions,
      maxPackageQuantity: MAX_PACKAGE_QUANTITY.DISHWASHER,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }

  @Post(routes.user.builtInDishwasherPack.unspecified.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async confirmUnspecified(@Req() req, @Res() res, @Body() body: CreateUnspecifiedReservationDto) {
    const currentForm = req.session.builtInDishwasherPackUnspecifiedProductReservationForm;
    if (!currentForm) {
      throw new NotFoundException();
    }

    const data = await this.reservationService.createReservation(
      {
        ...body,
        packType: PackType.BUILD_IN_DISHWASHER,
        userId: req.user?.clientId,
        packages: [],
        workingDate1: null,
        workingDate2: null,
        mailTemplate: EMAIL_TEMPLATE_NAME.BUILD_IN_DISHWASHER_UNSPECIFIED,
      },
      req,
    );
    req.session.builtInDishwasherPackUnspecifiedReservationCode = data.code;
    return res.redirect(routes.user.builtInDishwasherPack.unspecified.reservationThankPage);
  }

  @Get(routes.user.builtInDishwasherPack.unspecified.reservationThankPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @DynamicRender()
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.builtInDishwasher.unspecified.thank)
  @PageUserSetup({
    title: 'ビルトイン食洗機交換工事パックの予約リクエスト完了ページ',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'ビルトイン食洗機交換工事パックの予約リクエスト完了ページ',
      meta_ogDescription:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.dishwasherPack.reservationThankPage}`,
    },
  })
  async reservationThankPageUnspecified(@Req() req, @Res() res) {
    const currentForm = req.session.builtInDishwasherPackUnspecifiedProductReservationForm;
    const builtInDishwasherPackReservationCode = req.session.builtInDishwasherPackUnspecifiedReservationCode;
    if (!currentForm || !builtInDishwasherPackReservationCode) {
      throw new NotFoundException();
    }
    const activePackages = await this.packageService.getEnablePackages(PackType.BUILD_IN_DISHWASHER);
    delete req.session.builtInDishwasherPackUnspecifiedProductReservationForm;
    delete req.session.builtInDishwasherPackUnspecifiedReservationCode;

    return {
      reservationCode: builtInDishwasherPackReservationCode,
      currentForm,
      activePackages,
    };
  }
}
