import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Render,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PageUserSetup } from '@src/common/decorators/page-user-setup.decorator';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { AuthenticationRedirectGuard } from '@src/common/guards/authentication-redirect.guard';
import { RequireUserInterceptor } from '@src/common/interceptors/require-user.interceptor';
import { UserRequestInterceptor } from '@src/common/interceptors/user-request.interceptor';
import { routes } from '@src/common/routes';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { ACCOUNT_TYPES, AccountTypeAuthorization } from '@vietnam/cnga-middleware';
import * as dayjs from 'dayjs';
import { formatCurrency } from '@src/common/utils/format';
import { findMinValidDate, getAllDatesBetween, YYYYMD } from '@src/common/utils/date-util';
import { PackType } from '@src/common/enums/pack-type';
import { VIEW_TEMPLATES } from '@src/common/views';
import { BuiltInDishwasherParamDto } from '@src/modules/shared/reservation/dtos/built-in-dishwasher-query.dto';
import { ERROR_MESSAGE } from '@src/common/constants';
import { CreateBuiltInDishwasherReservationDto } from '@src/modules/shared/reservation/dtos/create-built-in-dishwasher-reservation.dto';
import { EMAIL_TEMPLATE_NAME } from '@src/common/constants/reservations';
import { BuiltInDishwasherPackageService } from '@src/modules/shared/package/built-in-dishwasher-package.service';
import { randomUUID } from 'crypto';
import { AdditionalInfoTemplate } from '@src/common/enums/additional-information';

@Controller()
export class BuiltInDishwasherStandardController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly configurationService: ConfigurationService,
    private readonly calendarBlockService: CalendarBlockService,
    private readonly builtInDishwasherPackageService: BuiltInDishwasherPackageService,
    private readonly reservationOptionService: ReservationOptionService,
  ) {}

  @Get(routes.user.builtInDishwasherPack.standard.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.builtInDishwasher.standard.newReservation)
  @PageUserSetup({
    title: 'ビルトイン食洗機交換工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
  })
  async reservationPage(@Req() req, @Param() param: BuiltInDishwasherParamDto) {
    const [config, blockedDates, reservationOptions, selectedPackage] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.BUILD_IN_DISHWASHER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.BUILD_IN_DISHWASHER,
      }),
      this.reservationOptionService.getEnableReservationOptions(PackType.BUILD_IN_DISHWASHER),
      this.builtInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail(param.productCode),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      randomIdempotencyKey: randomUUID(),
      reservationUrl: routes.user.builtInDishwasherPack.standard.generic,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      selectedPackage,
      reservationOptions,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
      meta: {
        meta_description:
          'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
        meta_ogTitle: 'ビルトイン食洗機交換工事パックの予約フォーム',
        meta_ogDescription:
          'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
        link_canonical: `${process.env.APP_URL}${routes.user.builtInDishwasherPack.standard.generic}${param.productCode}/`,
      },
    };
  }

  @Post(routes.user.builtInDishwasherPack.standard.newReservationPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async register(@Req() req, @Body() body, @Param() param: BuiltInDishwasherParamDto) {
    try {
      await this.builtInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail(param.productCode);
      const sessionName = `builtInDishwasherPackReservationForm_${param.productCode}`;
      req.session[sessionName] = body;
      return {};
    } catch (err) {
      return { error: err.message };
    }
  }

  @Get(routes.user.builtInDishwasherPack.standard.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.builtInDishwasher.standard.confirmReservation)
  @PageUserSetup({
    title: 'ビルトイン食洗機交換工事パックの予約フォーム',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: true,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
    meta: {
      meta_description:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      meta_ogTitle: 'ビルトイン食洗機交換工事パックの予約フォーム',
      meta_ogDescription:
        'パナソニック、リンナイなどの人気ビルトイン食洗機を、くらしのマーケットがお安くご提供。ビルトイン食洗機購入と取付工事をまとめて簡単予約！',
      link_canonical: `${process.env.APP_URL}${routes.user.builtInDishwasherPack.standard.reservationConfirmPage}`,
    },
  })
  async reservationConfirmPage(@Req() req, @Param() param: BuiltInDishwasherParamDto) {
    const currentForm = req.session[`builtInDishwasherPackReservationForm_${param.productCode}`];
    if (!currentForm) {
      throw new NotFoundException();
    }
    const [config, blockedDates, reservationOptions, selectedPackage] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, PackType.BUILD_IN_DISHWASHER),
      this.calendarBlockService.getCalendarBlockDates({
        start: dayjs().format(YYYYMD),
        end: dayjs().add(2, 'month').format(YYYYMD),
        packType: PackType.BUILD_IN_DISHWASHER,
      }),
      this.reservationOptionService.getEnableReservationOptions(PackType.BUILD_IN_DISHWASHER),
      this.builtInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail(param.productCode),
    ]);
    const prepareDays = Number(config?.value || '0');
    const additionalBlockedDates = getAllDatesBetween(dayjs(), dayjs().add(prepareDays, 'day'));
    blockedDates.push(...additionalBlockedDates);
    const defaultDate = findMinValidDate(
      dayjs().add(prepareDays === 0 ? 0 : prepareDays - 1, 'day'),
      dayjs().add(2, 'month'),
      blockedDates,
    );
    return {
      currentForm,
      reservationThankPageUrl: routes.user.builtInDishwasherPack.standard.reservationThankPage,
      reservationPageUrl: routes.user.builtInDishwasherPack.standard.newReservationPage,
      reservationUrl: routes.user.builtInDishwasherPack.standard.generic,
      userId: req.user?.clientId,
      defaultDate,
      blockedDates,
      selectedPackage,
      reservationOptions,
      errorMessages: ERROR_MESSAGE,
      formatCurrency,
    };
  }

  @Post(routes.user.builtInDishwasherPack.standard.reservationConfirmPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  async confirm(
    @Req() req,
    @Res() res,
    @Body() body: CreateBuiltInDishwasherReservationDto,
    @Param() param: BuiltInDishwasherParamDto,
  ) {
    await this.builtInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail(param.productCode);
    const currentForm = req.session[`builtInDishwasherPackReservationForm_${param.productCode}`];
    if (!currentForm) {
      throw new NotFoundException();
    }

    const data = await this.reservationService.createReservation(
      {
        ...body,
        packType: PackType.BUILD_IN_DISHWASHER,
        userId: req.user?.clientId,
        mailTemplate: EMAIL_TEMPLATE_NAME.BUILD_IN_DISHWASHER_STANDARD,
      },
      req,
    );
    req.session[`builtInDishwasherPackReservationCode_${param.productCode}`] = data.code;
    return res.redirect(`${routes.user.builtInDishwasherPack.standard.generic}${param.productCode}/thanks/`);
  }

  @Get(routes.user.builtInDishwasherPack.standard.reservationThankPage)
  @AccountTypeAuthorization(ACCOUNT_TYPES.EndUser)
  @UseGuards(AuthenticationRedirectGuard)
  @UseInterceptors(UserRequestInterceptor, RequireUserInterceptor)
  @Render(VIEW_TEMPLATES.user.builtInDishwasher.standard.thank)
  @PageUserSetup({
    title: 'エコキュート交換工事パックの予約リクエスト完了ページ',
    pageSpeed: false,
    lightPage: false,
    useLightHeader: false,
    usingApp: false,
    urlSuffix: '',
    stationSlug: '',
    usingCalendarApp: true,
  })
  async reservationThankPage(@Req() req, @Param() param: BuiltInDishwasherParamDto) {
    const currentForm = req.session[`builtInDishwasherPackReservationForm_${param.productCode}`];
    const builtInDishwasherPackReservationCode =
      req.session[`builtInDishwasherPackReservationCode_${param.productCode}`];
    if (!builtInDishwasherPackReservationCode || !currentForm) {
      throw new NotFoundException();
    }
    const reservation = await this.reservationService.getOneReservation(
      {
        code: builtInDishwasherPackReservationCode,
      },
      ['additionalInformation'],
    );
    const additionalInformation = reservation.additionalInformation.find(
      (info) => info.templateType === AdditionalInfoTemplate.BuiltInDishwasher,
    );
    const selectedPackage = await this.builtInDishwasherPackageService.getBuiltInDishwasherPackagesOrFail(
      param.productCode,
    );
    delete req.session[`builtInDishwasherPackReservationForm_${param.productCode}`];
    delete req.session[`builtInDishwasherPackReservationCode_${param.productCode}`];
    return {
      reservationCode: builtInDishwasherPackReservationCode,
      additionalInformationUrl: `/additional-information/?token=${additionalInformation.verificationToken}`,
      currentForm,
      selectedPackage,
      packageCode: param.productCode,
      meta: {
        meta_description:
          'エコキュート交換工事パックの予約リクエスト完了ページ。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
        meta_ogTitle: 'エコキュート交換工事パックの予約リクエスト完了ページ',
        meta_ogDescription:
          'エコキュート交換工事パックの予約リクエスト完了ページ。高品質・高コスパ！コロナの最新エコキュートが市場最安級で登場！現地調査無料、補助金申請も代行！購入&交換工事をセットで簡単予約！',
        link_canonical: `${process.env.APP_URL}${routes.user.builtInDishwasherPack.standard.generic}${param.productCode}/thanks/`,
      },
    };
  }
}
