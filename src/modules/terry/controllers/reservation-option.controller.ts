import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Put,
  Query,
  Render,
  UseGuards,
} from '@nestjs/common';
import { PageAdminSetup } from '@src/common/decorators/page-admin-setup.decorator';
import { routes } from '@src/common/routes';
import { AdminActivePage } from '@src/common/enums/admin-active-page';
import { AdminAuthenticatedGuard, AdminAuthorization } from '@vietnam/cnga-middleware';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { UpdateReservationOptionDto } from '@src/modules/shared/reservation-option/dtos/update-reservation-option.dto';
import { PackType } from '@src/common/enums/pack-type';
import { ReservationOptionQueryDto } from '@src/modules/shared/reservation-option/dtos/reservation-option-query.dto';
import { VIEW_TEMPLATES } from '@src/common/views';
import { PackageService } from '@src/modules/shared/package/package.service';

@Controller()
@UseGuards(AdminAuthenticatedGuard)
export class ReservationOptionController {
  constructor(
    private readonly reservationOptionService: ReservationOptionService,
    private readonly packageService: PackageService,
  ) {}

  @Get(routes.terry.reservationOptions.reservationOptionsPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.reservationOptionList)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.RESERVATION_OPTIONS })
  async getListReservationOptionsPage(@Query() query: ReservationOptionQueryDto) {
    const packType = !query.packType ? PackType.AIRCON_INSTALL : query.packType;
    const [options, { packageType }] = await Promise.all([
      this.reservationOptionService.getAllReservationOptions({ packType }),
      this.packageService.getPackagePageCommonData(),
    ]);
    return {
      data: {
        packType,
        options,
        listPackageType: packageType,
      },
    };
  }

  @Get(routes.terry.reservationOptions.reservationOptionDetailPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.reservationOptionDetail)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.RESERVATION_OPTIONS })
  async getReservationOptionDetailPage(@Param('id', new ParseUUIDPipe()) id: string) {
    const reservationOption = await this.reservationOptionService.getReservationOptionDetail(id);
    if (!reservationOption) {
      throw new NotFoundException('Option not found');
    }
    return {
      data: {
        reservationOption,
      },
    };
  }

  @Put(routes.terry.reservationOptions.updateReservationOption)
  @AdminAuthorization('Consumer', 'edit')
  async updateReservationOption(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() body: UpdateReservationOptionDto,
  ) {
    const reservationOption = await this.reservationOptionService.getReservationOptionDetail(id);
    if (!reservationOption) {
      throw new NotFoundException('Reservation option not found');
    }
    return this.reservationOptionService.updateReservationOption(id, body);
  }
}
