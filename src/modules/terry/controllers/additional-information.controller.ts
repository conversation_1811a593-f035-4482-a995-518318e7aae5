import { Controller, Delete, Get, Param, ParseUUIDPipe, Put, UseGuards } from '@nestjs/common';
import { routes } from '@common/routes';
import { AdminAuthenticatedGuard, AdminAuthorization } from '@vietnam/cnga-middleware';
import { AdditionalInformationService } from '@src/modules/shared/additional-information/additional-information.service';
import { AdditionalInfoStatus } from '@common/enums/additional-information';
import { AdditionActivityService } from '@src/modules/shared/addition-activity/addition-activity.service';
import { AdditionAttemptService } from '@src/modules/shared/addition-attempt/addition-attempt.service';

@Controller()
@UseGuards(AdminAuthenticatedGuard)
export class AdditionalInformationController {
  constructor(
    private readonly additionalInformationService: AdditionalInformationService,
    private readonly additionActivityService: AdditionActivityService,
    private readonly additionAttemptService: AdditionAttemptService,
  ) {}

  @Get(routes.terry.additionalInformation.getAdditionalInfoActivities)
  @AdminAuthorization('Consumer', 'edit')
  async getAdditionalInformationActivities(@Param('id', new ParseUUIDPipe()) id: string) {
    await this.additionalInformationService.getAdditionalInformationOrFail({ id });
    const additionActivities = await this.additionActivityService.getActivityByAddition(id);
    return { data: additionActivities };
  }

  @Get(routes.terry.additionalInformation.getAdditionalInfoAttemptDetail)
  @AdminAuthorization('Consumer', 'edit')
  async getAdditionalInfoAttemptDetail(
    @Param('additionId', new ParseUUIDPipe()) additionId: string,
    @Param('attemptId', new ParseUUIDPipe()) attemptId: string,
  ) {
    await this.additionalInformationService.getAdditionalInformationOrFail({ id: additionId });
    const additionAttempt = await this.additionAttemptService.getAttemptById(attemptId);
    if (additionAttempt.additionalInformationId !== additionId) {
      throw new Error('This additional information attempt does not belong to this additional information');
    }
    return { data: additionAttempt };
  }

  @Delete(routes.terry.additionalInformation.deleteAdditionalInformation)
  @AdminAuthorization('Consumer', 'edit')
  async deleteAdditionalInformationAttempt(@Param('id', new ParseUUIDPipe()) id: string) {
    const additionInformation = await this.additionalInformationService.getAdditionalInformationOrFail({ id });
    if (!additionInformation.isEnable) {
      throw new Error('This additional information has been deleted');
    }
    return await this.additionalInformationService.deleteAdditionalInformation(id);
  }

  @Put(routes.terry.additionalInformation.refreshUploadUrl)
  @AdminAuthorization('Consumer', 'edit')
  async refreshUploadUrl(@Param('id', new ParseUUIDPipe()) id: string) {
    const additionInformation = await this.additionalInformationService.getAdditionalInformationOrFail({ id });
    if (!additionInformation.isEnable) {
      throw new Error('This additional information has been deleted');
    }
    if (additionInformation.status !== AdditionalInfoStatus.UPLOADED) {
      throw new Error('This additional information is not in the correct status');
    }
    return await this.additionalInformationService.refreshUploadUrl(id);
  }
}
