import { Body, Controller, Delete, Get, Post, Query, Render, UseGuards } from '@nestjs/common';
import { routes } from '@src/common/routes';
import { AdminActivePage } from '@src/common/enums/admin-active-page';
import { AdminAuthenticatedGuard, AdminAuthorization } from '@vietnam/cnga-middleware';
import { ConfigurationService } from '@src/modules/shared/configuration/configuration.service';
import { CalendarBlockService } from '@src/modules/shared/calendar-block/calendar-block.service';
import { SetCalendarBlockDateDto } from '@src/modules/shared/calendar-block/dtos/set-calendar-block-date.dto';
import { DeleteCalendarBlockDateDto } from '@src/modules/shared/calendar-block/dtos/delete-calendar-block-date.dto';
import { CalendarBlockQueryDto } from '@src/modules/shared/calendar-block/dtos/calendar-block-query.dto';
import { PageAdminSetup } from '@src/common/decorators/page-admin-setup.decorator';
import { ConfigKeyEnum } from '@src/common/enums/configuration';
import { PackType } from '@src/common/enums/pack-type';
import { ConfigurationQueryDto } from '@src/modules/shared/configuration/dtos/configuration-query.dto';
import { VIEW_TEMPLATES } from '@src/common/views';
import { PackageService } from '@src/modules/shared/package/package.service';

@Controller()
@UseGuards(AdminAuthenticatedGuard)
export class CalendarBlockController {
  constructor(
    private readonly calendarBlockService: CalendarBlockService,
    private readonly configurationService: ConfigurationService,
    private readonly packageService: PackageService,
  ) {}

  @Get(routes.terry.calendarBlock.calendarBlockPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.calendarBlockPage)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.CALENDAR_BLOCK })
  async getListCalendarBlockDatePage(@Query() query: ConfigurationQueryDto) {
    const packType = !query.packType ? PackType.AIRCON_INSTALL : query.packType;
    const [calendarBlockRange, { packageType }] = await Promise.all([
      this.configurationService.getConfiguration(ConfigKeyEnum.CALENDAR_BLOCK_RANGE, packType),
      this.packageService.getPackagePageCommonData(),
    ]);
    return {
      data: {
        packType,
        calendarBlockRange: calendarBlockRange || {},
        listPackageType: packageType,
      },
    };
  }

  @Get(routes.terry.calendarBlock.getCalendarBlock)
  @AdminAuthorization('Consumer', 'edit')
  async getListCalendarBlockDate(@Query() query: CalendarBlockQueryDto) {
    const blockDates = await this.calendarBlockService.getCalendarBlockDates(query);
    return { blockDates };
  }

  @Post(routes.terry.calendarBlock.setCalendarBlock)
  @AdminAuthorization('Consumer', 'edit')
  async setCalendarBlockDate(@Body() body: SetCalendarBlockDateDto) {
    return await this.calendarBlockService.setCalendarBlockDate(body);
  }

  @Delete(routes.terry.calendarBlock.setCalendarBlock)
  @AdminAuthorization('Consumer', 'edit')
  async deleteCalendarBlockDate(@Body() body: DeleteCalendarBlockDateDto) {
    return await this.calendarBlockService.deleteCalendarBlockDate(body);
  }
}
