import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Render,
  Req,
  UseGuards,
} from '@nestjs/common';
import { routes } from '@common/routes';
import { AdminActivePage } from '@common/enums/admin-active-page';
import { ReservationService } from '@src/modules/shared/reservation/reservation.service';
import { AdminPaginateReservationDto } from '@src/modules/shared/reservation/dtos/admin-paging-reservation.dto';
import { AdminAuthenticatedGuard, AdminAuthorization } from '@vietnam/cnga-middleware';
import { ReservationOptionType, ReservationStatus } from '@common/enums/reservation';
import { PageAdminSetup } from '@src/common/decorators/page-admin-setup.decorator';
import { formatCurrency } from '@common/utils/format';
import { PackType } from '@common/enums/pack-type';
import { ReservationOptionService } from '@src/modules/shared/reservation-option/reservation-option.service';
import { CreateAddingDocumentUrlDto } from '@src/modules/shared/reservation/dtos/create-adding-document.dto';
import { resolveAppUrl } from '@common/utils/helper';
import { VIEW_TEMPLATES } from '@common/views';
import { AdditionalInfoTemplateMapping } from '@common/enums/additional-information';
import { AdditionActivityService } from '@src/modules/shared/addition-activity/addition-activity.service';
import { PackageService } from '@src/modules/shared/package/package.service';

@Controller()
@UseGuards(AdminAuthenticatedGuard)
export class ReservationController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly reservationOptionService: ReservationOptionService,
    private readonly additionActivityService: AdditionActivityService,
    private readonly packageService: PackageService,
  ) {}

  @Get(routes.terry.reservation.reservationListPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.reservationList)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.RESERVATIONS })
  async getListReservationsPage(@Query() query: AdminPaginateReservationDto) {
    const packType = !query.packType ? PackType.AIRCON_INSTALL : query.packType;
    const [{ status }, { packageType }, { reservations, pagination }] = await Promise.all([
      this.reservationService.getReservationPageCommonData(),
      this.packageService.getPackagePageCommonData(),
      this.reservationService.getReservations({
        ...query,
        packType,
        offset: query.offset || 0,
        pageNumber: query.pageNumber || 1,
        perPage: query.perPage || 10,
      }),
    ]);
    return {
      data: {
        query,
        packType,
        listPackageType: packageType,
        status,
        reservations,
        pagination,
      },
    };
  }

  @Get(routes.terry.reservation.pagingReservation)
  @AdminAuthorization('Consumer', 'edit')
  async getPagingListReservation(@Query() query: AdminPaginateReservationDto) {
    return await this.reservationService.getReservations(query);
  }

  @Get(routes.terry.reservation.reservationDetailPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.reservationDetail)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.RESERVATIONS })
  async getReservationDetailPage(@Param('id', new ParseUUIDPipe()) id: string) {
    const [{ status, addingDocumentStatus }, { packageType }, reservationDetail] = await Promise.all([
      this.reservationService.getReservationPageCommonData(),
      this.packageService.getPackagePageCommonData(),
      this.reservationService.getReservationDetail(
        id,
        ['reservationsPackages', 'reservationsPackages.package', 'additionalInformation'],
        {
          additionalInformation: {
            createdAt: 'DESC',
          },
        },
      ),
    ]);
    if (!reservationDetail) {
      throw new NotFoundException('Reservation not found');
    }
    const templateIds = this.reservationService.getAdditionalInformationTemplate(reservationDetail.packType);
    const documentTemplate = Object.entries(AdditionalInfoTemplateMapping)
      .filter((e) => templateIds.includes(Number(e[0])))
      .map((e) => ({ id: e[0], name: e[1] }));

    const reservationOptions = await this.reservationOptionService.getEnableReservationOptions(
      reservationDetail.packType,
    );
    const reservationOptionsData = reservationOptions.reduce((allOptions, option) => {
      const optionData = reservationDetail.options[option.name];
      if (optionData) {
        let value = '';
        if (option.type === ReservationOptionType.QUANTITY) {
          value = `${optionData.quantity} ${option.detail.unit}（${formatCurrency(
            optionData.fee / optionData.quantity,
          )} /${option.detail.unit}）`;
        }
        if (option.type === ReservationOptionType.TEXT) {
          value = optionData.value;
          if (option.name === 'extendedWarranty') {
            if (!optionData.fee) return allOptions;
            value = `${optionData.value}（${formatCurrency(optionData.fee)}/台）`;
          }
        }
        allOptions[option.name] = {
          id: option.id,
          title: option.title,
          value,
        };
      }
      return allOptions;
    }, {});

    let additionActivities = [];
    if (reservationDetail.additionalInformation.length > 0) {
      additionActivities = await this.additionActivityService.getActivityByAddition(
        reservationDetail.additionalInformation[0].id,
      );
    }

    return {
      data: {
        appUrl: resolveAppUrl(),
        reservationDetail,
        listPackageType: packageType,
        additionActivities,
        status,
        addingDocumentStatus,
        documentTemplate,
        reservationOptions,
        reservationOptionsData,
      },
      formatCurrency,
    };
  }

  @Post(routes.terry.reservation.createAddingDocumentUrl)
  @AdminAuthorization('Consumer', 'edit')
  async createAddingDocumentUrl(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() body: CreateAddingDocumentUrlDto,
  ) {
    const reservationDetail = await this.reservationService.getReservationDetail(id, ['additionalInformation']);
    if (!reservationDetail) {
      throw new NotFoundException('Reservation not found');
    }
    const templateIds = this.reservationService.getAdditionalInformationTemplate(reservationDetail.packType);
    if (!templateIds.includes(body.templateId)) {
      throw new BadRequestException('Invalid template id');
    }
    const existedAddition = reservationDetail.additionalInformation.find(
      (info) => info.templateType === body.templateId,
    );
    if (!existedAddition) {
      return await this.reservationService.createAddingDocumentUrl(id, body.templateId);
    }
    if (existedAddition.isEnable) {
      throw new BadRequestException('Current document template is in used');
    }
    return await this.reservationService.reactiveAddingDocumentUrl(existedAddition.id);
  }

  @Put(routes.terry.reservation.confirmReservation)
  @AdminAuthorization('Consumer', 'edit')
  async confirmReservation(@Param('id', new ParseUUIDPipe()) id: string, @Req() request: any) {
    const reservationDetail = await this.reservationService.getReservationDetail(id);
    if (!reservationDetail) {
      throw new NotFoundException('Reservation not found');
    }
    // TODO: create invoice url
    // TODO: send email and notification to user
    return await this.reservationService.updateReservation(id, { status: ReservationStatus.INPROGRESS });
  }

  @Put(routes.terry.reservation.cancelReservation)
  @AdminAuthorization('Consumer', 'edit')
  async cancelReservation(@Param('id', new ParseUUIDPipe()) id: string) {
    const reservationDetail = await this.reservationService.getReservationDetail(id);
    if (!reservationDetail) {
      throw new NotFoundException('Reservation not found');
    }
    // TODO: send email and notification to user
    return await this.reservationService.updateReservation(id, { status: ReservationStatus.CANCELED });
  }
}
