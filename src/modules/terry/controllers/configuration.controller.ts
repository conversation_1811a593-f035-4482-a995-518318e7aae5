import { Body, Controller, Param, ParseUUIDPipe, Put, UseGuards } from '@nestjs/common';
import { routes } from '../../../common/routes';
import { AdminAuthenticatedGuard, AdminAuthorization } from '@vietnam/cnga-middleware';
import { ConfigurationService } from '../../shared/configuration/configuration.service';
import { UpdateConfigurationDto } from '../../shared/configuration/dtos/update-configuration.dto';

@Controller()
@UseGuards(AdminAuthenticatedGuard)
export class ConfigurationController {
  constructor(private readonly configurationService: ConfigurationService) {}

  @Put(routes.terry.configuration.updateConfiguration)
  @AdminAuthorization('Consumer', 'edit')
  async updateConfiguration(@Param('id', new ParseUUIDPipe()) id: string, @Body() body: UpdateConfigurationDto) {
    return await this.configurationService.updateConfigurations(id, body);
  }
}
