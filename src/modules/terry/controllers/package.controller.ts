import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Post,
  Put,
  Query,
  Render,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { routes } from '@src/common/routes';
import { AdminActivePage } from '@src/common/enums/admin-active-page';
import { PackageService } from '@src/modules/shared/package/package.service';
import { AdminPaginatePackageDto } from '@src/modules/shared/package/dtos/admin-paging-package.dto';
import { CreatePackageDto, UpdatePackageDto } from '@src/modules/shared/package/dtos/create-package.dto';
import { AdminAuthenticatedGuard, AdminAuthorization } from '@vietnam/cnga-middleware';
import { PageAdminSetup } from '@src/common/decorators/page-admin-setup.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { ImportPackageDto } from '@src/modules/shared/package/dtos/export-package.dto';
import { PackType } from '@src/common/enums/pack-type';
import { AdminAllPackageDto } from '@src/modules/shared/package/dtos/admin-all-package.dto';
import { VIEW_TEMPLATES } from '@src/common/views';

@Controller()
@UseGuards(AdminAuthenticatedGuard)
export class PackageController {
  constructor(private readonly packageService: PackageService) {}

  @Get(routes.terry.packages.packageListPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.packageList)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.PACKAGES })
  async getListPackagesPage(@Query() query: AdminPaginatePackageDto) {
    const packType = !query.packType ? PackType.AIRCON_INSTALL : query.packType;
    const [{ status, packageType }, { packages, pagination }] = await Promise.all([
      this.packageService.getPackagePageCommonData(),
      this.packageService.getPackages({
        ...query,
        packType,
        offset: query.offset || 0,
        pageNumber: query.pageNumber || 1,
        perPage: query.perPage || 10,
      }),
    ]);
    return {
      data: {
        packType,
        listPackageType: packageType,
        status,
        packages,
        pagination,
      },
    };
  }

  @Get(routes.terry.packages.pagingPackage)
  @AdminAuthorization('Consumer', 'edit')
  async getPagingListPackage(@Query() query: AdminPaginatePackageDto) {
    return await this.packageService.getPackages(query);
  }

  @Get(routes.terry.packages.allPackages)
  @AdminAuthorization('Consumer', 'edit')
  async getListAllPackage(@Query() query: AdminAllPackageDto) {
    const packages = await this.packageService.getPackageList({
      needSort: true,
      conditions: { packType: query.packType },
    });
    return { packages };
  }

  @Get(routes.terry.packages.newPackagePage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.packageDetail)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.PACKAGES })
  async getNewPackagePage() {
    const { status, nextCode, packageType } = await this.packageService.getPackagePageCommonData();
    return {
      data: {
        packageDetail: null,
        nextCode,
        status,
        packageType,
      },
    };
  }

  @Post(routes.terry.packages.createPackage)
  @AdminAuthorization('Consumer', 'edit')
  async createPackage(@Body() body: CreatePackageDto) {
    return this.packageService.createPackage(body);
  }

  @Post(routes.terry.packages.exportPackage)
  @AdminAuthorization('Consumer', 'edit')
  exportPackages(@Res() res, @Body() body: ImportPackageDto) {
    const { packageIds } = body;
    return this.packageService.exportPackages(packageIds, res);
  }

  @Post(routes.terry.packages.importPackage)
  @AdminAuthorization('Consumer', 'edit')
  @UseInterceptors(FileInterceptor('file'))
  importPackages(@UploadedFile() file: Express.Multer.File) {
    return this.packageService.importPackages(file);
  }

  @Get(routes.terry.packages.packageDetailPage)
  @AdminAuthorization('Consumer', 'edit')
  @Render(VIEW_TEMPLATES.terry.packageDetail)
  @PageAdminSetup({ activeAdminPage: AdminActivePage.PACKAGES })
  async getPackageDetailPage(@Param('id', new ParseUUIDPipe()) id: string) {
    const [{ status, nextCode, packageType }, packageDetail] = await Promise.all([
      this.packageService.getPackagePageCommonData(),
      this.packageService.getPackageDetail(id),
    ]);
    if (!packageDetail) {
      throw new NotFoundException('Package not found');
    }
    return {
      data: {
        packageDetail,
        status,
        nextCode,
        packageType,
      },
    };
  }

  @Put(routes.terry.packages.updatePackage)
  @AdminAuthorization('Consumer', 'edit')
  async updatePackage(@Param('id', new ParseUUIDPipe()) id: string, @Body() body: UpdatePackageDto) {
    const packageDetail = await this.packageService.getPackageDetail(id);
    if (!packageDetail) {
      throw new NotFoundException('Package not found');
    }
    return this.packageService.updatePackage(id, body);
  }

  @Delete(routes.terry.packages.deletePackage)
  @AdminAuthorization('Consumer', 'edit')
  async deletePackage(@Param('id', new ParseUUIDPipe()) id: string) {
    const packageDetail = await this.packageService.getPackageDetail(id);
    if (!packageDetail) {
      throw new NotFoundException('Package not found');
    }
    return this.packageService.deletePackage(id);
  }
}
