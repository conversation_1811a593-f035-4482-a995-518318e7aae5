import { Module } from '@nestjs/common';
import { CuramaPy3ApiModule } from '@vietnam/curama-py3-api';
import { TerryAuthModule } from '@vietnam/cnga-middleware';
import { QueueProvider } from '../../providers/queue.provider';
import { ReservationController } from './controllers/reservation.controller';
import { ReservationModule } from '../shared/reservation/reservation.module';
import { PackageModule } from '../shared/package/package.module';
import { PackageController } from './controllers/package.controller';
import { CalendarBlockController } from './controllers/calendar-block.controller';
import { CalendarBlockModule } from '../shared/calendar-block/calendar-block.module';
import { ConfigurationModule } from '../shared/configuration/configuration.module';
import { ConfigurationController } from './controllers/configuration.controller';
import { ReservationOptionController } from './controllers/reservation-option.controller';
import { ReservationOptionModule } from '../shared/reservation-option/reservation-option.module';
import { AdditionActivityModule } from '../shared/addition-activity/addition-activity.module';
import { AdditionalInformationController } from './controllers/additional-information.controller';
import { AdditionalInformationModule } from '../shared/additional-information/additional-information.module';
import { AdditionAttemptModule } from '../shared/addition-attempt/addition-attempt.module';

@Module({
  imports: [
    QueueProvider.register(),
    CuramaPy3ApiModule,
    TerryAuthModule,
    ReservationModule,
    PackageModule,
    CalendarBlockModule,
    ConfigurationModule,
    ReservationOptionModule,
    AdditionActivityModule,
    AdditionalInformationModule,
    AdditionAttemptModule,
  ],
  controllers: [
    ReservationController,
    PackageController,
    CalendarBlockController,
    ConfigurationController,
    ReservationOptionController,
    AdditionalInformationController,
  ],
  providers: [],
  exports: [],
})
export class TerryModule {}
