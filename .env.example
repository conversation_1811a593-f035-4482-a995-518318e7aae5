APP_PORT=3000
APP_ENV=local
APP_DEBUG=true
APP_STATIC_BASE_URL=http://localhost:3000
APP_STATIC_PREFIX=

DATABASE_DRIVER=
DATABASE_HOST=
DATABASE_PORT=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_NAME=
DATABASE_SCHEMA=
DATABASE_POOL_SIZE=10
SESSION_KEY=curama
SESSION_TTL=86400

RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_PATH=/
RABBITMQ_SCHEME=amqp

PY3_BASE_URL=http://localhost:10081
PY3_API_KEY=****************************************************************
INTERNAL_API_DOMAIN=https://internal-api.curama.jp
API_FW_PRINCIPAL_KEY=

HTTP_TIMEOUT=60000
HTTP_MAX_RETRY=1
HTTP_STATUS_RETRY=
HTTP_RETRY_ENDPOINT=

# Auth redis
REDIS_URL=redis://localhost

TZ=Asia/Tokyo

SHOULD_REPLACE_INFRA_SETTING=true
INFRA_FRONTEND_STATIC_DOMAIN=d2uty6fis19a2l.cloudfront.net
INFRA_FRONTEND_EXTERNAL_STATIC_CDN=d2uty6fis19a2l.cloudfront.net/external
INFRA_FRONTEND_IMAGE_DOMAIN=d2uty6fis19a2l.cloudfront.net
INFRA_GOOGLE_ANALYTIC_TRACKING_ID=xxxxxxxx
INFRA_GOOGLE_TAGMANAGER_CONTAINER_ID=xxxxxxxx

TERRY_HOME_URL=
TERRY_LOGIN_URL=

GOOGLE_SERVICE_ACCOUNT_KEY=
GOOGLE_SPREADSHEET_ID=
GOOGLE_SPREADSHEET_WATER_HEATER_ID=
GOOGLE_SPREADSHEET_DISHWASHER_ID=
GOOGLE_SPREADSHEET_ADDITIONAL_INFORMATION_ENERGY_SAVING_2025_ID=
GOOGLE_SPREADSHEET_ADDITIONAL_INFORMATION_COUNTERTOP_DISHWASHER_ID=
GOOGLE_SPREADSHEET_ADDITIONAL_INFORMATION_EXISTING_ECOCUTE_ID=

CUSTOMER_SERVICE_EMAIL=

# (Optional) Config server keep alive timeout. Default is 65000 (65s)
SERVER_KEEP_ALIVE_TIMEOUT=65000
# (Optional) Config server headers timeout. Default is 70000 (70s)
SERVER_HEADERS_TIMEOUT=70000

# The s3 bucket name that used for upload file to s3
LABO_CONFIDENTIAL_S3_BUCKET_NAME=

# (Required) The base url of lambda function that used for upload file to s3
PRIVATE_FILE_HANDLER_URL=
# env that used for upload file to s3
PRIVATE_FILE_JWT_SECRET=
PRIVATE_FILE_JWT_AUD=
PRIVATE_FILE_JWT_EXPIRES_IN=

# Service cache redis
REDIS_CACHE_HOST=
REDIS_CACHE_PORT=
REDIS_CACHE_DB=
REDIS_CACHE_PREFIX=