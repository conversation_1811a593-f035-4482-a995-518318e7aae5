{% extends "layout/user/main_layout.njk" %}
{% import "shared/lp/featured-common-parts.njk" as commonParts %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}
{% from "user/pages/aircon-install/components/landing-page-hero.njk" import LandingPageHero %}
{% from "user/pages/aircon-install/components/landing-page-packages.njk" import LandingPagePackages %}
{% from "user/pages/aircon-install/components/category-card.njk" import CategoryCard %}
{% from "user/pages/aircon-install/components/landing-page-steps.njk" import LandingPageSteps %}
{% from "user/pages/aircon-install/components/landing-page-faq.njk" import LandingPageFaq %}
{% from "user/pages/aircon-install/components/landing-page-review.njk" import LandingPageReview %}
{% from "user/pages/aircon-install/components/landing-page-coupon.njk" import LandingPageCoupon %}

{% set breadcrumbTitle = "最新エアコン取付工事パック" %}
{% set sns_shareUrl = "https://curama.jp/aircon-install-pack/" %}

{% block head %}
  <meta property="og:url" content="{{ sns_shareUrl }}">
  <meta name="twitter:card" content="summary_large_image">
  <style>[v-cloak]{display:none !important}</style>
  <link rel="stylesheet" href="{{ assets.url("css/user/layout_aircon-sale.css") }}">
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+1:wght@100..900&family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
  <script type="application/ld+json">
    {{ productsJsonLd | safe }}
  </script>
{% endblock %}

{% block body %}
  {% set combined = {
    activePackages: activePackages,
    categoryTraits: categoryTraits,
    reservationPageUrl: reservationPageUrl,
    landingPageUrl: landingPageUrl,
    isMobile: req.isMobile()
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ LandingPageHero() }}
  {{ LandingPagePackages() }}
  {{ CategoryCard() }}
  {{ LandingPageSteps() }}
  {{ LandingPageFaq() }}
  {{ LandingPageReview() }}
  {{ LandingPageCoupon() }}
  {{ dataLayerScript() }}

  {% raw %}
  <div v-scope="LandingAircon()" @vue:mounted="mounted" v-cloak class="bac-c_bla02 mar-b_16">
    <section class="m_box-shadow mar-b_24" v-scope="LandingPageHero()"></section>

    <section id="list" class="m_box-shadow_-2-2-p2 bac-c_white" v-scope="LandingPagePackages()"></section>

    <section id="step" v-scope="LandingPageSteps()"></section>

    <section id="faq" v-scope="LandingPageFaq()"></section>

    <section id="review" v-scope="LandingPageReview()"></section>

    <section id="coupon" v-scope="LandingPageCoupon()"></section>
  </div>
  {% endraw %}
  <aside>
    <div class="bac-c_white">
      <ul class="m_list_none dis_t mar_0a mar-b_16 pad-t_40">
        <li class="dis_t-c pad-r_16">
          <div class="twitter-back">
            <a href="https://twitter.com/intent/tweet?url={{ sns_shareUrl }}%3futm_source%3dtwitter%26utm_medium%3dsocial%26utm_campaign%3dtwitter_share_button&hashtags=%e3%81%8f%e3%82%89%e3%81%97%e3%81%ae%e3%83%9e%e3%83%bc%e3%82%b1%e3%83%83%e3%83%88" target="_blank"><img src="{{ assets.url("image/user/common/social/icon_X.svg") }}" alt="X" width="40" height="40"></a>
            <script>
              !function (d, s, id) {
                var js,
                  fjs = d.getElementsByTagName(s)[0],
                  p = /^http:/.test(d.location)
                    ? 'http'
                    : 'https';
                if (!d.getElementById(id)) {
                  js = d.createElement(s);
                  js.id = id;
                  js.src = p + '://platform.twitter.com/widgets.js';
                  fjs
                    .parentNode
                    .insertBefore(js, fjs);
                }
              }(document, 'script', 'twitter-wjs');
            </script>
          </div>
        </li>
        <li class="dis_t-c pad-r_16">
          <div class="facebook-back">
            <a href="https://www.facebook.com/sharer/sharer.php?u={{ sns_shareUrl }}%3futm_source%3dfacebook%26utm_medium%3dsocial%26utm_campaign%3dfacebook_share_button" onclick="window.open(this.href,'FBwindow','width=650,height=450,menubar=no,toolbar=no,scrollbars=yes');return false;" title="Facebookでシェア"><img src="{{ assets.url("image/user/common/social/icon_facebook.svg") }}" alt="Facebook" width="40" height="40"></a>
          </div>
        </li>
        <li class="dis_t-c">
          <div class="line-back">
            <a href="https://timeline.line.me/social-plugin/share?url={{ sns_shareUrl }}%3futm_source%3dline%26utm_medium%3dsocial%26utm_campaign%3dline_share_button" target="_blank"><img src="{{ assets.url("image/user/common/social/icon_line.svg") }}" alt="LINE" width="40" height="40"></a>
          </div>
        </li>
      </ul>
      <div class="tex-a_c pad-b_40">
        <button id="copyLink" class="m_btn_bla_bor wid_a fon-s_14 dis_f ali-i_c mar_0a" data-clipboard-text="{{ title }} | {{ sns_shareUrl }}?utm_source=self_share&utm_medium=referral&utm_campaign=self_share_button"><svg class="m_icon-s_18 mar-r_8" role="img"><use xlink:href="#content_copy"></use></svg>URLをコピーする</button>
      </div>
    </div>
  </aside>
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const activePackages = initialData.activePackages;
    window.nk_params = {
      pagetype: "list",
      itemid: activePackages.map(p => p.id),
      price: [0],
      quantity: [0],
      orderid: "",
      totalprice: 0,
    };
    window.ecommerce_data = {
      pagetype: "list",
      items: activePackages.map((pack, idx) => {
        return {
          item_id: pack.id,
          item_name: pack.code,
          index: idx + 1,
          item_list_name: pack?.name,
          item_list_id: pack?.name,
          item_category: 'aircon-install-pack',
          item_category2: pack?.name,
          price: pack.fee || 0,
          currency: "JPY",
          quantity: 1,
        };
      }),
    };
    notifyEcommerceEventToApp(window.ecommerce_data);
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "initial_search_results_displayed",
    });
    function LandingAircon(props) {
      return {
        numberWithCommas: {{numberWithCommas|safe}},
        activePackages: initialData.activePackages,
        categoryTraits: initialData.categoryTraits,
        reservationPageUrl: initialData.reservationPageUrl,
        landingPageUrl: initialData.landingPageUrl,
        selectedPackageIds: {
          ...initialData.categoryTraits.reduce((acc, cat) => {
            acc[cat.category] = cat.packages[0].id;
            return acc;
          }, {})
        },
        isMobile: initialData.isMobile,

        getCategory(cat) {
          return this.categoryTraits.find(c => c.category === cat);
        },

        mounted() {
          window.addEventListener('pageshow', (event) => {
            let selectBoxes = document.querySelectorAll('.m_selectbox');
            selectBoxes.forEach((selectBox) => {
              selectBox.selectedIndex = 0;
            });
            if (event.persisted) {
              this.selectedPackageIds = {
                ...initialData.categoryTraits.reduce((acc, cat) => {
                  acc[cat.category] = cat.packages[0].id;
                  return acc;
                }, {})
              };
            }
          });
        },
      }
    };

    PetiteVue
      .createApp({LandingAircon})
      .mount();
  </script>

{% if not req.isUserApp() %}
  {{ commonParts.spBreadCrumb(breadcrumbTitle) }}
{% endif %}
{% endblock %}

{% block scripts %}
  <script src="{{ static.vendor("clipboard/dist/clipboard.min.js") }}"></script>
  <script>
    // クリップボードにコピー
    var clipboard = new Clipboard('#copyLink');
    clipboard.on('success', function (e) {
      $('#copyLink use')
        .attr("xlink:href", "#check")
        .delay(2000)
        .queue(function () {
          $(this)
            .attr("xlink:href", "#content_copy")
            .dequeue();
        });
      trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'リンクをコピー', 'eventLabel': location.href});
    });
    clipboard.on('error', function (e) {
      $("#copyLink").alert('お使いの端末はこの機能に対応していません');
    });

    // ga解析用
    $(function () {
      $('.twitter-back').tap(function (e) {
        trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'twitter', 'eventLabel': location.href});
      });
      $('.facebook-back').tap(function (e) {
        trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'facebook', 'eventLabel': location.href});
      });
      $('.line-back').tap(function (e) {
        trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'line', 'eventLabel': location.href});
      });
    });

    $('.trigger-expand-btn').on('click', (e) => {
      let expandInputs = document.querySelectorAll('.trigger-expand-input');
      let isChecked = expandInputs[0].checked;
      for (let i = 0; i < expandInputs.length; i++) {
        if (isChecked) {
          expandInputs[i].checked = false;
        } else {
          expandInputs[i].checked = true;
        }
      }
    });

  </script>
{% endblock %}