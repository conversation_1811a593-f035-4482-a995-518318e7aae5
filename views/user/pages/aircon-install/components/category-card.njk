{% macro CategoryCard() %}
  <script>
    function CategoryCard(category, activePackages) {
      const selectedPackageId = category?.packages?.[0]?.id || '';
      const selectedPackage = activePackages?.find(pack => pack.id === selectedPackageId);
      const packageTrait = category?.packages?.find(p => selectedPackageId === p.id);
      return {
        $template: '#category-card',
        category: category,
        categoryActivePackages: activePackages,
        imgSrc: packageTrait?.image ? `{{ assets.url('${packageTrait.image}') }}` : '',

        getPackageFee(id) {
          let package = this.categoryActivePackages.find(p => id === p.id);
          return package?.fee ? this.numberWithCommas(package.fee) : 0;
        },

        handleChangeOption() {
          const packageTrait = this.category?.packages?.find(p => this.selectedPackageIds[category.category] === p.id);
          this.imgSrc = packageTrait?.image ? `{{ assets.url('${packageTrait.image}') }}` : '';
        },

        getDisableButtonState(id) {
          let package = this.categoryActivePackages.find(p => id === p.id);
          return package?.outOfStock ? true : null;
        },

        handleRedirectDetail() {
          let selectedPackage = category?.packages?.find(p => this.selectedPackageIds[category.category] === p.id);
          return `${this.landingPageUrl}${selectedPackage.modelId}/`;
        },

        getPackageRibbonLabel() {
          let package = category?.packages.find(p => this.selectedPackageIds[category.category] === p.id);
          switch (package?.label) {
            case 1:
              return '売れ筋';
            case 2:
              return '担当の<br>おすすめ';
            case 3:
              return '新入荷';
            default:
              return '';
          }
        },

        getPackageRibbonStyle() {
          let package = category?.packages.find(p => this.selectedPackageIds[category.category] === p.id);
          switch (package?.label) {
            case 1:
              return 'grey';
            case 2:
              return 'yellow';
            case 3:
              return 'pink';
            default:
              return '';;
          }
        },

        getPackagePurchaseLink() {
          let selectedPackage = this.activePackages.find(p => this.selectedPackageIds[category.category] === p.id);
          return `${this.reservationPageUrl}?packageCode=${selectedPackage.code}`;
        },
      }
    };
  </script>
  {% raw %}
    <template id="category-card">
      <div v-if="getPackageRibbonLabel()" class="badge-ribbon hei_130 wid_130 top_-65 rig_-65" :class="getPackageRibbonStyle()"><div class="dis_f ali-i_c hei_30p" v-html="getPackageRibbonLabel()"></div></div>
      <div class="" :class="isMobile ? 'pad_16' : 'pad_24'">
        <img :src="imgSrc" width="100%" height="auto" :alt="category?.seriesName">
        <p class="mar-b_16"><span v-for="(pack, index) in category?.packages" :key="pack.id" class="dis_i-b">{{ index === 0 ? '[ ' : ''}}{{ pack.tatamiSize }}畳用{{ index === category?.packages.length - 1 ? ' ] ' : '・'}}</span></p>
        <p class="fon-s_18 fon-w_700 mar-b_16">{{ category?.seriesName }}</p>
        <div class="dis_f">
          <dl class="fon-s_16 wid_45p">
            <dd class="m_clearfix cur_p jus-c_c m_selectwrap">
              <select class="m_selectbox form-passed" v-model="selectedPackageIds[category.category]" @change="handleChangeOption" placeholder="畳数を選択">
                <option v-for="pack in category?.packages" :key="pack.id" :value="pack.id">{{ pack.tatamiSize }}畳用</option>
              </select>
            </dd>
          </dl>
          <div class="wid_55p ali-i_f-e dis_f jus-c_c fle-d_c">
            <div class="fon-s_10 col_red10 fon-w_700">標準工事代込み</div>
            <div class="fon-w_700 tex-a_r lin-h_1p2">
              <span class="fon-s_16 col_red10">¥</span><span class="fon-s_28 col_red10">{{ getPackageFee(selectedPackageIds[category.category]) }}</span>
              <span class="fon-s_10 col_red10 dis_i-b">税込</span>
            </div>
          </div>
        </div>
        <p class="fon-w_700 pad-t_16">{{ category?.generalDescription }}</p>
        <div class="pad-t_16 mar-b_24">
          <a 
            class="m_btn_ora10 mar-b_12 dis_f jus-c_c ali-i_c" 
            :class="getDisableButtonState(selectedPackageIds[category.category]) ? 'bor_1_bla06' : 'bor_1_ora10'" 
            :disabled="getDisableButtonState(selectedPackageIds[category.category])" 
            onclick="trackGAEvent('aircon-sale', '1stCVボタン', 'パナソニックFシリーズ')" 
            :href="getPackagePurchaseLink()">
            {{ getDisableButtonState(selectedPackageIds[category.category]) ? '売り切れ' : '予約する' }}
          </a>
          <a class="m_btn_bla_bor dis_f jus-c_c ali-i_c" :href="handleRedirectDetail()">詳細を見る</a>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}