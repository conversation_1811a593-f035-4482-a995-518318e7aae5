{% macro LandingPageSteps() %}
  <script>
    function LandingPageSteps() {
      return {
        $template: '#landing-page-steps',
        imgFlowSurveyReserveSrc: "{{ assets.url('image/user/common/step/img_flow_survey_reserve.svg') }}",
        imgFlowTelSrc: "{{ assets.url('image/user/common/step/img_flow_tel.svg') }}",
        imgFlowPaymentSrc: "{{ assets.url('image/user/common/step/img_flow_payment.svg') }}",
        imgFlowDeliverySrc: "{{ assets.url('image/user/common/step/img_flow_delivery.svg') }}",
        imgFlowInstallationSrc: "{{ assets.url('image/user/common/step/img_flow_installation.svg') }}",
        iconTriangleRoundedSrc: "{{ assets.url('image/user/common/icon/icon_triangle-rounded.svg') }}",
      }
    };
  </script>
  {% raw %}
    <template id="landing-page-steps">
      <div class="mar-b_16 pad-l_16 pad-r_16 pad-t_40 pad-b_32 bac-c_white">
        <h2 class="fon-w_700 fon-s_24 mar-t_0 mar-b_24 tex-a_c">予約〜取付の流れ</h2>
        <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_460" :class="isMobile ? 'pad_16' : 'pad_24'">
          <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
            <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">1</span></span></p>
          <div class="tex-a_c wid_100p">
            <img :src="imgFlowSurveyReserveSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
          </div>
          <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">予約申し込み</p>
          <p>「予約する」ボタンからフォームに進み、必要事項を入力して申し込み。</p>
        </div>
        <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_460" :class="isMobile ? 'pad_16' : 'pad_24'">
          <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
            <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">2</span></span></p>
          <div class="tex-a_c wid_100p pad-b_4">
            <img :src="imgFlowTelSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
          </div>
          <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8 lin-h_1p4">1週間以内に<br>お電話で打ち合わせ</p>
          <p>訪問日時と工事内容について、お電話にて事前に打ち合わせを行います。</p>
        </div>
        <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_460" :class="isMobile ? 'pad_16' : 'pad_24'">
          <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
            <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">3</span></span></p>
          <div class="tex-a_c wid_100p pad-b_8">
            <img :src="imgFlowPaymentSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
          </div>
          <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">事前決済</p>
          <p class="mar-b_8">入金案内のメールが届きましたら、内容をご確認の上、決済用のメールから事前決済の手続きをお願いします。</p>
          <p class="fon-s_12">※お支払いはクレジットカード決済のみ</p>
        </div>
        <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_460" :class="isMobile ? 'pad_16' : 'pad_24'">
          <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
            <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">4</span></span></p>
          <div class="tex-a_c wid_100p">
            <img :src="imgFlowDeliverySrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
          </div>
          <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">製品の事前配送</p>
          <p class="mar-b_8">工事日の2日前を目安にご指定の配送先に届きます。</p>
          <p class="fon-s_12 mar-b_4">※工事日に配送と取り付けをまとめて希望される場合は+3,300円で承ります。</p>
          <p class="fon-s_12">※受取日時の変更は発送通知メールに記載の送り状番号から運送会社へお問い合わせください。</p>
        </div>
        <div class="tex-a_c mar-b_16 pad-t_4">
          <img :src="iconTriangleRoundedSrc" width="24" height="auto">
        </div>
        <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_460" :class="isMobile ? 'pad_16' : 'pad_24'">
          <p class="fon-s_18 fon-w_700 tex-a_c">工事日にプロが取り付けに伺います！</p>
          <div class="tex-a_c wid_100p">
            <img :src="imgFlowInstallationSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
          </div>
          <p class="fon-s_12">※申し込みから最短11日</p>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
