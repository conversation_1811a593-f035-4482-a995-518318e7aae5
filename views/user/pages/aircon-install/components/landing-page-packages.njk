{% macro LandingPagePackages() %}
  <script>
    function LandingPagePackages() {
      return { 
        $template: '#landing-page-packages',
      }
    };
  </script>
  {% raw %}
    <template id="landing-page-packages">
      <h2 class="bac-c_white fon-w_700 fon-s_24 pad-t_40 pad-b_24 mar_0 tex-a_c">製品一覧</h2>
      <div class="dis_f bac-c_white mar-b_40">
        <a href="#simple" class="pad-t_4 wid_trisect tex-a_c fon-w_700 fon-s_14 lin-h_18 col_bla10 bor-r_1_das_bla05">コスパ重視<br>シンプル<br>
          <svg class="hei_24 wid_24 fil_ora10" role="img">
            <use xlink:href="#expand_more"></use>
          </svg>
        </a>
        <a href="#standard" class="pad-t_4 wid_trisect tex-a_c fon-w_700 fon-s_14 lin-h_18 col_bla10 bor-r_1_das_bla05">バランス重視<br>スタンダード<br>
          <svg class="hei_24 wid_24 fil_ora10" role="img">
            <use xlink:href="#expand_more"></use>
          </svg>
        </a>
        <a href="#premium" class="pad-t_4 wid_trisect tex-a_c fon-w_700 fon-s_14 lin-h_18 col_bla10">機能重視<br>プレミアム<br>
          <svg class="hei_24 wid_24 fil_ora10" role="img">
            <use xlink:href="#expand_more"></use>
          </svg>
        </a>
      </div>

      <div id="simple" class="bac-c_white pad-l_16 pad-r_16 pad-b_24 pad-t_40">
        <div class="mar-b_32 wid_100p tex-a_c">
          <p class="fon-w_700 fon-s_14 col_ora10 mar-b_8">\ 基本的な機能で十分なあなたに /</p>
          <h3 class="fon-w_700 fon-s_24 mar_0 lin-h_30">コスパ重視のシンプルモデル</h3>
        </div>
        <div class="bac-c_white bor-r_8 bor_1_bla08 mar_0a mar-b_24 max-w_460 category-card" v-scope="CategoryCard(getCategory('mitsubishiGV'), activePackages)"></div>
        <div class="bac-c_white bor-r_8 bor_1_bla08 mar_0a max-w_460 category-card" v-scope="CategoryCard(getCategory('panasonicF'), activePackages)"></div>
      </div>

      <div id="standard" class="bac-c_bla02 pad-l_16 pad-r_16 pad-b_24 pad-t_40">
        <div class="mar-b_32 wid_100p tex-a_c">
          <p class="fon-w_700 fon-s_14 col_ora10 mar-b_8">\ 十分な機能でお買い得価格 /</p>
          <h3 class="fon-w_700 fon-s_24 mar_0 lin-h_30">バランスが良い<br>スタンダードモデル</h3>
        </div>
        <div class="bac-c_white bor-r_8 bor_1_bla08 mar_0a mar-b_24 max-w_460 category-card" v-scope="CategoryCard(getCategory('panasonicJ'), activePackages)"></div>
        <div class="bac-c_white bor-r_8 bor_1_bla08 mar_0a max-w_460 category-card" v-scope="CategoryCard(getCategory('mitsubishiAXV'), activePackages)"></div>
      </div>

      <div id="premium" class="bac-c_white pad-l_16 pad-r_16 pad-b_24 pad-t_40">
        <div class="mar-b_32 wid_100p tex-a_c">
          <p class="fon-w_700 fon-s_14 col_ora10 mar-b_8">\ 住空間の居心地を高める /</p>
          <h3 class="fon-w_700 fon-s_24 mar_0 lin-h_30">機能性に優れた<br>プレミアムモデル</h3>
        </div>
        <div class="bac-c_white bor-r_8 bor_1_bla08 mar_0a mar-b_24 max-w_460 category-card" v-scope="CategoryCard(getCategory('daikinCX'), activePackages)"></div>
        <div class="bac-c_white bor-r_8 bor_1_bla08 mar_0a max-w_460 category-card" v-scope="CategoryCard(getCategory('panasonicEX'), activePackages)"></div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}