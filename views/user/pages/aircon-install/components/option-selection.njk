{% macro OptionSelection() %}
  <script>
    function OptionSelection() {
      return {
        $template: '#option-selection',
        image1: "{{ assets.url('image/user/option/1.jpg') }}",
        image2: "{{ assets.url('image/user/option/2.jpg') }}",
        image3: "{{ assets.url('image/user/option/3.jpg') }}",
        image4: "{{ assets.url('image/user/option/4.jpg') }}",
        genArrayOptions(max) {
          return Array.from({
            length: max
          }, (_, i) => i + 1)
        },
        changeProductDeliveryType(e) {
          const selectedType = event
            .target
            .options[event.target.selectedIndex];
          if (event.target.selectedIndex === 0) {
            this.errors.productDeliveryType = '';
          } else {
            this.errors.productDeliveryType = '';
          }
          this.calculateTotalFee();
        }
      }
    };
  </script>
  {% raw %}
    <template id="option-selection">
      <div class="bac-c_white pad-b_48">
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_24 dis_f ali-i_c">{{ getTitle('extendedWarranty') }}<span class="bac-c_bla05 fon-w_n dis_i-b fon-s_10 bor-r_5 pad_4 mar-l_16">任意</span></h2>
        <dl class="mar-b_8">
          <dt class="mar-b_24">
            加入すると予約する全台に対して取り付けから5年間の保証が付帯されます。<br>詳細は<a class="col_bla10 tex-d_u" href="https://faq.curama.jp/--6839307f875afdeb19426046" target="_blank" rel="noopener noreferrer">延長保証について</a>をご確認ください。予約が確定した時点で、<a class="col_bla10 tex-d_u" href="https://www.sompo-swt.com/hosyo_kitei/kitei_saishin/kitei/jyusetsu_kitei.pdf" :target="usingApp ? '_self' : '_blank'" rel="noopener noreferrer">SOMPOワランティ住宅設備機器延長保証サービス規定</a>に同意したものとみなします。
          </dt>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.extendedWarranty" class="m_selectbox form-passed" placeholder="-----" :class="!forms.extendedWarranty && forms.extendedWarranty !== 0 && errors.extendedWarranty ? 'bor_1_red10' : ''">
              <option v-for="opt in optionsData.extendedWarranty?.detail?.listOptions" :data-key="opt.title" :key="opt.title+opt.price" :value="opt.title">{{opt.title}}{{ opt.price ? `（+${opt.price.toLocaleString('ja-JP')}円 / 台）` : ''}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.extendedWarranty && errors.extendedWarranty">{{ errors.extendedWarranty }}</p>
        </dl>
      </div>
      <div class="bac-c_white">
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_16 dis_f ali-i_c">オプション<span class="bac-c_bla05 fon-w_n dis_i-b fon-s_10 bor-r_5 pad_4 mar-l_16">任意</span></h2>
        <dl class="mar-b_8">
          <dt class="mar-b_4">{{getTitle('existingAirConditionerRemoval')}}（{{formatCurrency(optionsData.existingAirConditionerRemoval?.detail?.pricePerUnit)}}/1{{optionsData.existingAirConditionerRemoval?.detail?.unit}}）</dt>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.existingAirConditionerRemoval" class="m_selectbox form-passed" placeholder="-----" :class="!forms.existingAirConditionerRemoval && errors.existingAirConditionerRemoval ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in genArrayOptions(optionsData.existingAirConditionerRemoval?.detail?.maxQuantity)" :key="opt" :value="opt">{{opt}}{{optionsData?.existingAirConditionerRemoval?.detail?.unit}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.existingAirConditionerRemoval && errors.existingAirConditionerRemoval">{{ errors.existingAirConditionerRemoval }}</p>
        </dl>
        <dl class="mar-b_8">
          <dt class="mar-b_4">{{getTitle('existingAirConditionerRetrieval')}}（{{formatCurrency(optionsData.existingAirConditionerRetrieval?.detail?.pricePerUnit)}}/1{{optionsData.existingAirConditionerRetrieval?.detail?.unit}}）</dt>
          <p class="fon-s_12 mar-b_8">※本料金は既設エアコンの収集運搬料金となり、別途<a class="col_bla10" href="https://www.rkc.aeha.or.jp/recycle_price_compact.html" target="_blank" rel="noopener noreferrer">
              <span class="tex-d_u">リサイクル料金</span>
              <svg class="dis_i-b hei_18 wid_18 fil_bla10 ver-a_b" role="img">
                <use xlink:href="#open_in_new"></use>
              </svg>
            </a>が発生します。</p>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.existingAirConditionerRetrieval" class="m_selectbox form-passed" placeholder="-----" :class="!forms.existingAirConditionerRetrieval && errors.existingAirConditionerRetrieval ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in genArrayOptions(optionsData.existingAirConditionerRetrieval?.detail?.maxQuantity)" :key="opt" :value="opt">{{opt}}{{optionsData.existingAirConditionerRetrieval?.detail?.unit}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.existingAirConditionerRetrieval && errors.existingAirConditionerRetrieval">{{ errors.existingAirConditionerRetrieval }}</p>
        </dl>
        <dl class="mar-b_8">
          <input id="image1" class="m_acd-check dis_n" type="checkbox">
          <dt class="m_acd-wrapper mar-b_4">{{getTitle('outdoorPipeDecorativeCover')}}（{{formatCurrency(optionsData.outdoorPipeDecorativeCover?.detail?.pricePerUnit)}}〜/1{{optionsData.outdoorPipeDecorativeCover?.detail?.unit}}）
            <label class="m_acd-title dis_i" for="image1">
              <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-r_4 ver-a_t_t" role="img">
                <use xlink:href="#help"></use>
              </svg>
            </label>
          </dt>
          <div class="mar-b_8 pad_16 bor-r_8 bac-c_bla02 fon-s_12 m_acd-content dis_n">
            <p class="mar-b_8">紫外線、風雨、ホコリなどから配管を保護でき、テープ仕上げに比べて破損や漏れの原因となる劣化防止に効果的です。</p>
            <div class="tex-a_c lin-h_0">
              <img :src="image1" class="wid_90p hei_a bor-r_8 mar-b_4"/>
            </div>
            <div class="dis_f jus-c_s-a wid_90p mar_0a fon-s_12">
              <p>配管テープ仕上げ</p>
              <p>配管カバー仕上げ</p>
            </div>
          </div>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.outdoorPipeDecorativeCover" class="m_selectbox form-passed" placeholder="-----" :class="!forms.outdoorPipeDecorativeCover && errors.outdoorPipeDecorativeCover ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in genArrayOptions(optionsData.outdoorPipeDecorativeCover?.detail?.maxQuantity)" :key="opt" :value="opt">{{opt}}{{optionsData.outdoorPipeDecorativeCover?.detail?.unit}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.outdoorPipeDecorativeCover && errors.outdoorPipeDecorativeCover">{{ errors.outdoorPipeDecorativeCover }}</p>
        </dl>
        <dl class="mar-b_8">
          <input id="image2" class="m_acd-check dis_n" type="checkbox">
          <dt class="m_acd-wrapper mar-b_4">{{getTitle('indoorPipeDecorativeCover')}}（{{formatCurrency(optionsData.indoorPipeDecorativeCover?.detail?.pricePerUnit)}}〜/1{{optionsData.indoorPipeDecorativeCover?.detail?.unit}}）
            <label class="m_acd-title dis_i" for="image2">
              <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-r_4 ver-a_t_t" role="img">
                <use xlink:href="#help"></use>
              </svg>
            </label>
          </dt>
          <div class="mar-b_8 pad_16 bor-r_8 bac-c_bla02 fon-s_12 m_acd-content dis_n">
            <p class="mar-b_8">化粧カバーをつけると見た目がスッキリするだけでなく、劣化からも配管を守ります。配管の凹凸がなくなることでホコリが溜まりにくく、お掃除も楽になります。</p>
            <div class="tex-a_c lin-h_0">
              <img :src="image2" class="wid_90p hei_a bor-r_8 mar-b_4"/>
            </div>
          </div>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.indoorPipeDecorativeCover" class="m_selectbox form-passed" placeholder="-----" :class="!forms.indoorPipeDecorativeCover && errors.indoorPipeDecorativeCover ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in genArrayOptions(optionsData.indoorPipeDecorativeCover?.detail?.maxQuantity)" :key="opt" :value="opt">{{opt}}{{optionsData.indoorPipeDecorativeCover?.detail?.unit}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.indoorPipeDecorativeCover && errors.indoorPipeDecorativeCover">{{ errors.indoorPipeDecorativeCover }}</p>
        </dl>
        <dl class="mar-b_8">
          <input id="image3" class="m_acd-check dis_n" type="checkbox">
          <dt class="m_acd-wrapper mar-b_4">{{getTitle('outdoorUnitMountWithBrackets')}}（{{formatCurrency(optionsData.outdoorUnitMountWithBrackets?.detail?.pricePerUnit)}}/1{{optionsData.outdoorUnitMountWithBrackets?.detail?.unit}}）
            <label class="m_acd-title dis_i" for="image3">
              <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-r_4 ver-a_t_t" role="img">
                <use xlink:href="#help"></use>
              </svg>
            </label>
          </dt>
          <div class="mar-b_8 pad_16 bor-r_8 bac-c_bla02 fon-s_12 m_acd-content dis_n">
            <p class="mar-b_8">室外機を直置きできない場合に、金具を使用して設置することができます。</p>
            <div class="tex-a_c lin-h_0">
              <img :src="image3" class="wid_90p hei_a bor-r_8 mar-b_4"/>
            </div>
          </div>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.outdoorUnitMountWithBrackets" class="m_selectbox form-passed" placeholder="-----" :class="!forms.outdoorUnitMountWithBrackets && errors.outdoorUnitMountWithBrackets ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in genArrayOptions(optionsData.outdoorUnitMountWithBrackets?.detail?.maxQuantity)" :key="opt" :value="opt">{{opt}}{{optionsData.outdoorUnitMountWithBrackets?.detail?.unit}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.outdoorUnitMountWithBrackets && errors.outdoorUnitMountWithBrackets">{{ errors.outdoorUnitMountWithBrackets }}</p>
        </dl>
        <dl class="mar-b_8">
          <input id="image4" class="m_acd-check dis_n" type="checkbox">
          <dt class="m_acd-wrapper mar-b_4">{{getTitle('twoLevelOutdoorUnitWithBrackets')}}（{{formatCurrency(optionsData.twoLevelOutdoorUnitWithBrackets?.detail?.pricePerUnit)}}/1{{optionsData.twoLevelOutdoorUnitWithBrackets?.detail?.unit}}）
            <label class="m_acd-title dis_i" for="image4">
              <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-r_4 ver-a_t_t" role="img">
                <use xlink:href="#help"></use>
              </svg>
            </label>
          </dt>
          <div class="mar-b_8 pad_16 bor-r_8 bac-c_bla02 fon-s_12 m_acd-content dis_n">
            <p class="mar-b_8">既存の室外機の上に、金具を使用し重ねて設置することができます。</p>
            <div class="tex-a_c lin-h_0">
              <img :src="image4" class="wid_90p hei_a bor-r_8 mar-b_4"/>
            </div>
          </div>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="calculateTotalFee" v-model="forms.twoLevelOutdoorUnitWithBrackets" class="m_selectbox form-passed" placeholder="-----" :class="!forms.twoLevelOutdoorUnitWithBrackets && errors.twoLevelOutdoorUnitWithBrackets ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in genArrayOptions(optionsData.twoLevelOutdoorUnitWithBrackets?.detail?.maxQuantity)" :key="opt" :value="opt">{{opt}}{{optionsData.twoLevelOutdoorUnitWithBrackets?.detail?.unit}}</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.twoLevelOutdoorUnitWithBrackets && errors.twoLevelOutdoorUnitWithBrackets">{{ errors.twoLevelOutdoorUnitWithBrackets }}</p>
        </dl>
        <dl class="mar-t_16 mar-b_8">
          <input id="productDeliveryType" class="m_acd-check dis_n" type="checkbox">
          <dt class="m_acd-wrapper mar-b_8 fon-w_700">{{getTitle('productDeliveryType')}}
            <label class="m_acd-title dis_i" for="productDeliveryType">
              <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-r_4 ver-a_t_t" role="img">
                <use xlink:href="#help"></use>
              </svg>
            </label>
          </dt>
          <div class="m_acd-content dis_n">
            <p class="mar-t_8 mar-b_8 fon-s_12">「事前配送」：設置日の3〜4日前を目安に指定の住所へ製品を配送します。作業日までにお受け取りの上、適切に保管をお願いします。</p>
            <p class="mar-t_8 mar-b_8 fon-s_12">「当日持込」：工事担当が作業当日に製品をお持ちします。製品保管の必要はありません。</p>
          </div>
          <p class="fon-s_12 mar-b_8">※「当日持込」の対応エリアは、東京都、千葉県、埼玉県、神奈川県、愛知県、大阪府、京都府、兵庫県です。</p>
          <dd class="m_clearfix mar-b_8 cur_p jus-c_c m_selectwrap">
            <select @change="changeProductDeliveryType" v-model="forms.productDeliveryType" class="m_selectbox form-passed" placeholder="-----" :class="!forms.productDeliveryType && forms.productDeliveryType !== 0 && errors.productDeliveryType ? 'bor_1_red10' : ''">
              <option value="">
                -----
              </option>
              <option v-for="opt in optionsData.productDeliveryType?.detail?.listOptions" :data-key="opt.title" :key="opt.title+opt.price" :value="opt.title">{{opt.title}}（+{{opt.price.toLocaleString('ja-JP')}}円）</option>
            </select>
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.productDeliveryType && forms.productDeliveryType !== 0 && errors.productDeliveryType">{{ errors.productDeliveryType }}</p>
        </dl>

        <h2 class="fon-s_16 fon-w_700 mar-t_8 mar-b_16 pad-t_8">料金</h2>
        <dl>
          <dt class="dis_t wid_100p">
            <span class="dis_t-c fon-s_12">合計（税込）</span>
            <span class="dis_t-c tex-a_r fon-s_20 fon-w_700" data-test-id="total-price-label" id="inner-total-price">{{formatCurrency(forms.totalFee)}}</span>
          </dt>
        </dl>
      </div>
    </template>
  {% endraw %}
{% endmacro %}