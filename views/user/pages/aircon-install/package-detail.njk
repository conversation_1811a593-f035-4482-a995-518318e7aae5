{% extends "layout/user/main_layout.njk" %}
{% import "shared/lp/featured-common-parts.njk" as commonParts %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}

{% set breadcrumbTitle = "最新エアコン取付工事パック" %}
{% set sns_shareUrl = "https://curama.jp/aircon-install-pack/" %}

{% block head %}
  <meta property="og:url" content="{{ sns_shareUrl }}">
  <meta name="twitter:card" content="summary_large_image">
  <style>[v-cloak]{display:none !important}</style>
  <link rel="stylesheet" href="{{ assets.url("css/user/layout_aircon-sale.css") }}">
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link href="https://fonts.googleapis.com/css2?family=M+PLUS+1:wght@100..900&family=M+PLUS+Rounded+1c&display=swap" rel="stylesheet">
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
{% endblock %}

{% block body %}
  {% set combined = {
    packageData: packageData,
    categoryTrait: categoryTrait,
    packageTrait: packageTrait,
    activePackages: activePackages,
    reservationPageUrl: reservationPageUrl,
    landingPageUrl: landingPageUrl,
    isMobile: req.isMobile()
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ dataLayerScript() }}

  {% raw %}
  <div v-scope="LandingAirconDetail()" @vue:mounted="mounted" v-cloak class="mar-b_16 bac-c_white">
    <div class="bac-c_white bor-r_8 mar_0a mar-b_24 max-w_460">
      <div :class="isMobile ? 'pad_16' : 'pad_24'">
        <div class="dis_f mar-b_16">
          <a data-backdrop="true" data-target="#detailsModal" data-toggle="modal" class="dis_f jus-c_c ali-i_c fon-w_700 fon-s_12 col_white bac-c_blu12 pad_5 pad-r_12 pad-l_12 bor-r_4 mar-r_8">標準工事代込<svg class="hei_18 wid_18 fil_white mar-l_4" role="img">
              <use xlink:href="#help"></use>
            </svg>
          </a>
          <span class="dis_f jus-c_c ali-i_c fon-w_700 fon-s_12 col_white bac-c_gre01 pad_5 pad-r_12 pad-l_12 bor-r_4">製品保証1年</span>
        </div>
        <img :src="imgSrc" width="100%" height="auto" :alt="categoryTrait?.seriesName">
        <p class="fon-s_18 fon-w_700 mar-b_16">{{ categoryTrait?.seriesName }}　{{ packageTrait.tatamiSize }}畳用</p>
        <p class="fon-s_12 fon-w_300 mar-b_12 col_bla08">その他のラインナップ</p>
        <div class="mar-b_16 gap_8 dis_f fle-w_w">
          <a 
          v-for="(pack, index) in categoryTrait?.packages?.filter(p => p.id !== packageTrait.id)" 
          :key="pack.id" 
          class="dis_i-b pad-t_4 pad-b_4 pad-l_12 pad-r_12 bor_1_bla05 fon-s_12 col_bla10 bor-r_16"
          :href="generateRedirectPackageLink(pack.modelId)">
            {{ pack.tatamiSize }}畳用
          </a>
        </div>
        <div class="dis_f">
          <div class="tex-a_r wid_100p ali-i_c dis_f jus-c_f-e">
            <p class="fon-w_700">
              <span class="fon-s_16 col_red10">¥</span>
              <span class="fon-s_28 col_red10">{{ numberWithCommas(packageData.fee) }}</span>
              <span class="fon-s_10 col_red10 dis_i-b">税込</span>
            </p>
          </div>
        </div>

        <div class="mar-t_24 mar-b_24">
          <a class="dis_f jus-c_c ali-i_c m_btn_ora10" :class="disabledReservationButton ? 'bor_1_bla06' : 'bor_1_ora10'" :disabled="disabledReservationButton" onclick="trackGAEvent('aircon-sale', '1stCVボタン', 'パナソニックFシリーズ')" :href="generatePackagePurchaseLink()">{{ disabledReservationButton ? '売り切れ' : '予約する' }}</a>
        </div>

        <div class="infor-wrap">
          <div class="tex-a_l lin-h_22">
            <div class="mar-b_32" v-html="categoryTrait?.detailDescription">
            </div>
            <div class="mar-b_16">
              <table class="wid_100p bor_1_bla06 bor-c_c mar-b_16">
                <tr class="bor-b_1bla06">
                  <th class="wid_35p bor_1_bla06 lin-h_14 pad_8">畳数目安</th>
                  <th class="wid_65p bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.tatamiSize }}畳用</th>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">型番</th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.model }}</td>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">冷房能力<span class="fon-s_10 fon-w_700">（kw）</span></th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.coolingCapacity }}</td>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">暖房能力<span class="fon-s_10 fon-w_700">（kw）</span></th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.heatingCapacity }}</td>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">電源</th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.powerSupply }}</td>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">室内機サイズ<span class="fon-s_10 fon-w_700">（mm）</span></th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.indoorUnitSize }}</td>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">室外機サイズ<span class="fon-s_10 fon-w_700">（mm）</span></th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.outdoorUnitSize }}</td>
                </tr>
                <tr class="bor-b_1bla06">
                  <th class="bor_1_bla06 lin-h_14 pad_8">APF</th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.apf }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06 lin-h_14 pad_8">省エネ基準達成率<span class="fon-s_10 fon-w_700">（%）</span></th>
                  <td class="bor_1_bla06 lin-h_14 pad_8">{{ packageTrait.energyEfficiencyRating }}</td>
                </tr>
              </table>
            </div>
            <div class="dis_f jus-c_f-e">
              <a :href="categoryTrait?.manufacturerSite" target="_blank" rel="noopener noreferrer" class="dis_f lin-h_18 col_bla10 tex-d_u">
                メーカーサイト
                <svg class="dis_i-b hei_18 wid_18 mar-l_4 fil_bla10" role="img">
                  <use xlink:href="#open_in_new"></use>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div class="mar-t_40 mar-b_24">
          <a class="dis_f jus-c_c ali-i_c m_btn_bla_bor" href="javascript:void(0);" @click="history.back();">戻る</a>
        </div>
      </div>
    </div>


    <div aria-labelledby="filtersModalLabel" class="modal dis_n" id="detailsModal" role="dialog" tabindex="-1" style="display: none;">
      <div class="mar_0 pos_f bot_0 top_a hei_100p-100 wid_100p bac-c_white bor-r_top10 ove-y_s pc_fullsize">
        <div class="min-h_56 pos_r wid_100p">
          <a type="button" class="pos_a top_16 rig_16" data-dismiss="modal">
            <svg class="wid_24 hei_24 fil_bla06" role="img">
              <use xlink:href="#close"></use>
            </svg>
          </a>
        </div>
        <div class="hei_a pad-r_16 pad-l_16 pad-b_24 bac-c_white">
          <p class="fon-w_700 fon-s_18 mar-b_24">標準工事について</p>
          <p class="mar-b_16">以下の作業は「標準工事」として工事料金に含まれています。</p>
          <ul>
            <li>設置箇所の構造（隠ぺい配管・室外機設置箇所など）の事前確認</li>
            <li>作業内容の事前説明</li>
            <li>室内機設置</li>
            <li>室外機設置（大地置き・ベランダ置き）</li>
            <li>配管パイプ・連絡電線・ドレンホースの設置</li>
            <li>室外機据え置き台（プラロックなど）の設置</li>
            <li>壁の配管穴開け（木造・モルタル・石膏・ALC）</li>
            <li>配管類の用意（4m）</li>
            <li>配管類の取り付け</li>
            <li>アース接続</li>
            <li>真空引き作業（エアパージ作業）</li>
            <li>動作確認</li>
            <li>作業場所の簡易清掃</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  {% endraw %}
  <aside>
    <div class="bac-c_white">
      <ul class="m_list_none dis_t mar_0a mar-b_16 pad-t_40">
        <li class="dis_t-c pad-r_16">
          <div class="twitter-back">
            <a href="https://twitter.com/intent/tweet?url={{ sns_shareUrl }}%3futm_source%3dtwitter%26utm_medium%3dsocial%26utm_campaign%3dtwitter_share_button&hashtags=%e3%81%8f%e3%82%89%e3%81%97%e3%81%ae%e3%83%9e%e3%83%bc%e3%82%b1%e3%83%83%e3%83%88" target="_blank"><img src="{{ assets.url("image/user/common/social/icon_X.svg") }}" alt="X" width="40" height="40"></a>
            <script>
              !function (d, s, id) {
                var js,
                  fjs = d.getElementsByTagName(s)[0],
                  p = /^http:/.test(d.location)
                    ? 'http'
                    : 'https';
                if (!d.getElementById(id)) {
                  js = d.createElement(s);
                  js.id = id;
                  js.src = p + '://platform.twitter.com/widgets.js';
                  fjs
                    .parentNode
                    .insertBefore(js, fjs);
                }
              }(document, 'script', 'twitter-wjs');
            </script>
          </div>
        </li>
        <li class="dis_t-c pad-r_16">
          <div class="facebook-back">
            <a href="https://www.facebook.com/sharer/sharer.php?u={{ sns_shareUrl }}%3futm_source%3dfacebook%26utm_medium%3dsocial%26utm_campaign%3dfacebook_share_button" onclick="window.open(this.href,'FBwindow','width=650,height=450,menubar=no,toolbar=no,scrollbars=yes');return false;" title="Facebookでシェア"><img src="{{ assets.url("image/user/common/social/icon_facebook.svg") }}" alt="Facebook" width="40" height="40"></a>
          </div>
        </li>
        <li class="dis_t-c">
          <div class="line-back">
            <a href="https://timeline.line.me/social-plugin/share?url={{ sns_shareUrl }}%3futm_source%3dline%26utm_medium%3dsocial%26utm_campaign%3dline_share_button" target="_blank"><img src="{{ assets.url("image/user/common/social/icon_line.svg") }}" alt="LINE" width="40" height="40"></a>
          </div>
        </li>
      </ul>
      <div class="tex-a_c pad-b_40">
        <button id="copyLink" class="m_btn_bla_bor wid_a fon-s_14 dis_f ali-i_c mar_0a" data-clipboard-text="{{ title }} | {{ sns_shareUrl }}?utm_source=self_share&utm_medium=referral&utm_campaign=self_share_button"><svg class="m_icon-s_18 mar-r_8" role="img"><use xlink:href="#content_copy"></use></svg>URLをコピーする</button>
      </div>
    </div>
  </aside>
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const packageData = initialData.packageData;
    window.nk_params = {
      pagetype: 'detail',
      itemid: [packageData.id],
      price: [packageData.fee],
      quantity: [0],
      orderid: '',
      totalprice: 0,
    }
    window.ecommerce_data = {
      pagetype: "detail",
      items: [{
        item_id: packageData.id,
        item_name: packageData.code,
        item_brand: 'AirconInstallPack',
        item_category: 'aircon-install-pack',
        item_category2: packageData?.name,
        item_list_name: packageData?.name,
        item_list_id: packageData?.name,
        price: packageData.fee,
        currency: 'JPY',
        quantity: 1
      }]
    }
    notifyEcommerceEventToApp(window.ecommerce_data);
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "initial_search_results_displayed",
    });
    function LandingAirconDetail(props) {
      const initPackageTrait = initialData.categoryTrait?.packages?.find(pack => pack.id === initialData.packageData?.id) || {};
      const selectedPackage = initialData.activePackages?.find(pack => pack.id === initPackageTrait?.id);
      return {
        numberWithCommas: {{numberWithCommas|safe}},
        activePackages: initialData.activePackages,
        packageData: initialData.packageData,
        categoryTrait: initialData.categoryTrait,
        packageTrait: initPackageTrait,
        selectedPackage: selectedPackage,
        disabledReservationButton: selectedPackage?.outOfStock || null,
        imgSrc: initPackageTrait?.image ? `{{ assets.url('${initPackageTrait?.image}') }}` : '',
        reservationPageUrl: initialData.reservationPageUrl,
        landingPageUrl: initialData.landingPageUrl,
        isMobile: initialData.isMobile,

        generatePackagePurchaseLink() {
          return `${this.reservationPageUrl}?packageCode=${selectedPackage.code}`;
        },

        generateRedirectPackageLink(modelId) {
          return `${this.landingPageUrl}${modelId}/`;
        },

        mounted() {},
      }
    };

    PetiteVue
      .createApp({LandingAirconDetail})
      .mount();
  </script>

{% if not req.isUserApp() %}
  {{ commonParts.spBreadCrumb(breadcrumbTitle, modelId, landingPageUrl) }}
{% endif %}
{% endblock %}

{% block scripts %}
  <script src="{{ static.vendor("clipboard/dist/clipboard.min.js") }}"></script>
{% endblock %}