{% extends "layout/user/main_layout.njk" %}
{% import "components/enhanced-ec/_macro.njk" as enhancedEC %}
{% import "components/structured-data/_macro.njk" as structuredData %}
{% import "components/review-text/_macro.njk" as reviewText %}
{% from "partials/service/service-share.njk" import getMarketPriceTbody %}
{% import "components/dynamic-links-script/_macro.njk" as dynamicLinksScript %}
{% import "components/security-macros/_macro.njk" as security %}
{% from "user/common/progress-bar.njk" import ProgressBar %}
{% from "user/pages/aircon-install/components/reservation-form.njk" import ReservationForm %}
{% from "user/pages/aircon-install/components/reservation-review.njk" import ReservationReview %}
{% from "user/common/contact-information.njk" import ContactInformation %}
{% from "user/common/reserve-datetime.njk" import ReserveDatetime %}
{% from "user/common/package-selection.njk" import PackageSelection %}
{% from "user/pages/aircon-install/components/option-selection.njk" import OptionSelection %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}
{% set ReEnableButtons_selector = "button:not(#js-submit-button), input" %}
{% set noFooter = true %}

{% block metas %}{% endblock %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
  {% include "../../../assets/dayjs.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>

{% endblock %}
{% block styles %}
  <style>[v-cloak]{display:none !important}</style>
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link rel="stylesheet" href="{{ static.legacy("static/sp/css/curama-datepicker-theme.css") }}">
  {% if usingCalendarApp %}
    <link rel="stylesheet" href="{{ static.legacy("static/sp/css/calendar_user.css") }}">
  {% endif %}
  <link href="{{ assets.url("css/user/styles.css") }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
  <script src="{{ static.legacy("common/js/jquery-ui.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/jquery.ui.datepicker-ja.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/common.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block body %}
  {% set combined = {
    user: user,
    reservationConfirmPageUrl: reservationConfirmPageUrl,
    defaultDate: defaultDate,
    blockedDates: blockedDates,
    activePackages: activePackages,
    reservationOptions: reservationOptions,
    userId: userId,
    maxPackageQuantity: maxPackageQuantity,
    randomIdempotencyKey: randomIdempotencyKey,
    errorMessages: errorMessages,
    isMobile: req.isMobile(),
    usingApp: usingApp
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ ProgressBar() }}
  {{ ReservationForm() }}
  {{ ReservationReview() }}
  {{ ContactInformation() }}
  {{ ReserveDatetime() }}
  {{ PackageSelection() }}
  {{ OptionSelection() }}
  {{ dataLayerScript() }}
  {% raw %}
    <div v-scope="NewReservation()" @vue:mounted="mounted" v-cloak class="pos_r">
      <div v-if="isLoading" class="reservation-loading-wrap">
        <div class="sl-loading">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div v-scope="ReservationForm()"></div>
    </div>
  {% endraw %}
  <div class="pad-t_32 pad-r_16 pad-b_96 pad-l_16">
    <img 
      src="{{ static.staticImage("frontend/loading_SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.gif") }}"
      data-src="{{ static.staticImage("SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.webp") }}" 
      data-not-webp-img="{{ static.staticImage("SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.jpg") }}"
      data-loading-lazy="true"
      height="58" width="100" alt="ISMS-AC 認定シンボル"/>
    <p class="tex-a_c pad-t_16 fon-s_12">{{ copyright.getCopyright() }}</p>
  </div>
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    function NewReservation(props) {
      return {
        currentStep: 1,
        isMobile: initialData.isMobile,
        usingApp: initialData.usingApp,
        isLoading: false,
        cacheKey: "aircon-install-pack-form",
        reservationConfirmPageUrl: initialData.reservationConfirmPageUrl,
        maxPackageSelected: Number(initialData.maxPackageQuantity),
        packageQuantityOptions: Array.from({
          length: 10
        }, (_, i) => {
          return {
            value: i + 1,
            label: `${i + 1}台`
          };
        }),
        defaultDate: initialData.defaultDate,
        blockedDates: initialData.blockedDates,
        activePackages: initialData.activePackages,
        reservationOptions: initialData.reservationOptions,
        errorMessages: initialData.errorMessages,
        optionsData: {
          existingAirConditionerRemoval: initialData
            .reservationOptions
            .find(e => e.name === 'existingAirConditionerRemoval'),
          existingAirConditionerRetrieval: initialData
            .reservationOptions
            .find(e => e.name === 'existingAirConditionerRetrieval'),
          outdoorPipeDecorativeCover: initialData
            .reservationOptions
            .find(e => e.name === 'outdoorPipeDecorativeCover'),
          indoorPipeDecorativeCover: initialData
            .reservationOptions
            .find(e => e.name === 'indoorPipeDecorativeCover'),
          outdoorUnitMountWithBrackets: initialData
            .reservationOptions
            .find(e => e.name === 'outdoorUnitMountWithBrackets'),
          twoLevelOutdoorUnitWithBrackets: initialData
            .reservationOptions
            .find(e => e.name === 'twoLevelOutdoorUnitWithBrackets'),
          productDeliveryType: initialData
            .reservationOptions
            .find(e => e.name === 'productDeliveryType'),
          extendedWarranty: initialData
            .reservationOptions
            .find(e => e.name === 'extendedWarranty'),
        },
        calculateTotalFee() {
          const [packageFees, packageCount] = this
            .forms
            .packages
            .reduce((acc, cur) => [acc[0] + cur.fee * cur.quantity, acc[1] + cur.quantity], [0, 0]);
          const optionsFees = Object
            .keys(this.optionsData)
            .reduce((acc, cur) => {
              const option = this.optionsData[cur];
              if (option?.type === 'quantity') {
                return acc + (option.detail.pricePerUnit || 1) * (this.forms[cur] || 0);
              }
              if (option?.type === 'text') {
                const optionSelected = option
                  .detail
                  .listOptions
                  .find(e => e.title === this.forms[cur]);
                if (option.name === 'extendedWarranty') {
                  return this.forms[cur] === '加入する' ? acc + (optionSelected?.price * packageCount) : acc;                  
                }
                return acc + (optionSelected?.price || 0);
              }
              return acc;
            }, 0);
          this.forms.totalFee = packageFees + optionsFees;
        },
        forms: {
          idempotencyKey: initialData.randomIdempotencyKey,
          userId: initialData.userId,
          totalFee: 0,
          workingDate1: "",
          workingDate2: "",
          packages: [
            {
              id: "",
              name: "",
              fee: 0,
              quantity: 0
            }
          ],
          lastname: initialData.user
            ?.last_name ?? "",
          firstname: initialData.user
            ?.first_name ?? "",
          postalcode: initialData.user
            ?.address
              ?.postal_code ?? "",
          prefecture: initialData.user
            ?.address
              ?.prefecture ?? "",
          city: initialData.user
            ?.address
              ?.city ?? "",
          address: initialData.user
            ?.address
              ?.address ?? "",
          phoneNumber: initialData.user
            ?.phone_number ?? "",
          email: initialData.user
            ?.mail_address ?? "",
          existingAirConditionerRemoval: "",
          existingAirConditionerRetrieval: "",
          indoorPipeDecorativeCover: "",
          outdoorPipeDecorativeCover: "",
          outdoorUnitMountWithBrackets: "",
          twoLevelOutdoorUnitWithBrackets: "",
          productDeliveryType: "",
          extendedWarranty: "加入しない",
        },
        requiredFields: [
          'lastname',
          'firstname',
          'postalcode',
          'prefecture',
          'city',
          'address',
          'phoneNumber',
          'productDeliveryType',
          'email'
        ],
        defaultFieldKeyMapping: {
          dates: "全ての希望日時",
          lastname: "お名前（姓）",
          firstname: "お名前（名）",
          postalcode: "郵便番号",
          prefecture: "都道府県",
          city: "市区町村",
          address: "それ以降の住所",
          phoneNumber: "電話番号",
          email: "メールアドレス",
          packages: "製品と数量",
          productDeliveryType: "製品配送方法",
          default: "このフィールド"
        },
        errors: {
          datesError: "",
          packagesError: "",
          productDeliveryType: "",
          lastname: "",
          firstname: "",
          postalcode: "",
          prefecture: "",
          city: "",
          address: "",
          phoneNumber: "",
          email: ""
        },
        formatCurrency: {{formatCurrency | safe}},
        validateRequired(value, field, message) {
          if (!value.trim()) {
            this.errors[field] = message;
            return;
          }
          this.errors[field] = "";
        },
        validateReservationDate() {
          const dateRequire = !this.forms[`workingDate1`] || !this.forms[`workingDate2`];
          const dateConflict = this.forms[`workingDate1`] === this.forms[`workingDate2`];
          this.errors.datesError = dateRequire
            ? `${this.defaultFieldKeyMapping["dates"]}を入力してください。`
            : dateConflict
              ? "訪問日の希望はずらして設定してください。"
              : "";
        },
        validateSelectedPackages() {
          for (const packageSelected of this.forms.packages) {
            if (!packageSelected.id) {
              packageSelected.errorPackageId = `${this.defaultFieldKeyMapping.packages}を入力してください。`;
            } else {
              packageSelected.errorPackageId = '';
            }
            const package = this
              .activePackages
              .find(p => p.id === packageSelected?.id);
            if (package?.outOfStock) {
              packageSelected.errorPackageStock = `製品は在庫切れです。`;
            } else {
              packageSelected.errorPackageStock = '';
            }
            if (!packageSelected.quantity) {
              packageSelected.errorQuantity = `${this.defaultFieldKeyMapping.packages}を入力してください。`;
            } else {
              packageSelected.errorQuantity = '';
            }
          }
        },
        getTitle(option) {
          switch (option) {
            case "existingAirConditionerRemoval":
              return "既設エアコン取り外し";
            case "existingAirConditionerRetrieval":
              return "既設エアコン回収";
            case "outdoorPipeDecorativeCover":
              return "室外配管化粧カバー";
            case "indoorPipeDecorativeCover":
              return "室内配管化粧カバー";
            case "outdoorUnitMountWithBrackets":
              return "室外機の屋根・ 壁面・天吊り置き｜金具込み";
            case "twoLevelOutdoorUnitWithBrackets":
              return "室外機の二段置き｜金具込み";
            case "productDeliveryType":
              return "製品配送方法";
            case "extendedWarranty":
              return "延長保証";
            default:
              return "";
          }
        },
        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
          window.onbeforeunload = function (e) {
            e.returnValue = 'まだ登録が完了していません。\nこのページを離れると、入力した内容は破棄されます。';
          };

          const cachedData = sessionStorage.getItem(this.cacheKey);
          const {timestamp, currentStep, forms} = cachedData
            ? JSON.parse(cachedData)
            : {};
          const cachedForm = forms || {};
          const urlParams = new URLSearchParams(window.location.search);
          const packageCode = urlParams.get('packageCode');
          if (packageCode) {
            const package = this
              .activePackages
              .find(e => e.code === packageCode);
            if (package && !package.outOfStock) {
              this.forms = {
                ...this.forms,
                packages: [
                  {
                    id: package?.id,
                    name: package?.name,
                    fee: package?.fee,
                    quantity: 1
                  }
                ]
              };
            } else {
              this.forms = {
                ...this.forms,
                packages: [
                  {
                    id: "",
                    name: "",
                    fee: 0,
                    quantity: 0
                  }
                ]
              };
            }

            if (package) {
              window.nk_params = {
                pagetype: 'detail',
                itemid: [package.id],
                price: [package.fee],
                quantity: [0],
                orderid: '',
                totalprice: 0,
              }
              window.ecommerce_data = {
                pagetype: "detail",
                items: [{
                  item_id: package.id,
                  item_name: package.code,
                  item_brand: 'AirconInstallPack',
                  item_category: 'aircon-install-pack',
                  item_category2: package?.name,
                  item_list_name: package?.name,
                  item_list_id: package?.name,
                  price: package.fee,
                  currency: 'JPY',
                  quantity: 1
                }]
              }
              notifyEcommerceEventToApp(window.ecommerce_data);
            }
            this.calculateTotalFee();
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), currentStep: 1, forms: this.forms}));
            window
              .history
              .replaceState({}, document.title, window.location.pathname);
            return;
          }
          if (!timestamp || !forms || !currentStep) 
            return
          if (initialData.userId === cachedForm.userId && (new Date().getTime() - timestamp) < 1000 * 60 * 30) {
            if (this.blockedDates.includes(cachedForm.workingDate1)) {
              cachedForm.workingDate1 = "";
            }
            if (this.blockedDates.includes(cachedForm.workingDate2)) {
              cachedForm.workingDate2 = "";
            }
            const activePackageIds = this
              .activePackages
              .map(e => e.id);
            cachedForm.packages = cachedForm
              .packages
              .filter(e => !e.id || activePackageIds.includes(e.id));
            if (cachedForm.packages.length === 0) {
              delete cachedForm.packages;
            }
            this.forms = {
              ...this.forms,
              ...cachedForm
            };
            this.validateSelectedPackages();
            this.calculateTotalFee();
          } else {
            sessionStorage.removeItem(this.cacheKey);
          }

        }
      }
    };

    PetiteVue
      .createApp({NewReservation})
      .mount();
  </script>
{% endblock %}

{% block tracking %}{% endblock %}