{% extends "layout/user/main_layout.njk" %}
{% import "shared/lp/featured-common-parts.njk" as commonParts %}
{% from "user/common/progress-bar-adding-information.njk" import ProgressBar %}
{% from "user/common/upload-identity-files.njk" import UploadIdentityFiles %}
{% from "user/pages/additional-information/hotWaterEnergySaving2025/components/upload-form.njk" import UploadAdditionalInformationForm %}
{% from "user/pages/additional-information/hotWaterEnergySaving2025/components/confirm-information.njk" import ConfirmAdditionalInformation %}

{% set title = "給湯省エネ2025事業申請フォーム" %}
{% set ReEnableButtons_selector = "button:not(#js-submit-button), input" %}
{% set noFooter = true %}
{% set breadcrumbTitle = "給湯省エネ2025事業申請フォーム" %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
  {% include "../../../../assets/dayjs.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
{% endblock %}

{% block styles %}
  <style>[v-cloak]{display:none !important}</style>
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link rel="stylesheet" href="{{ static.legacy("static/sp/css/curama-datepicker-theme.css") }}">
  {% if usingCalendarApp %}
    <link rel="stylesheet" href="{{ static.legacy("static/sp/css/calendar_user.css") }}">
  {% endif %}
  <link href="{{ assets.url("css/user/styles.css") }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
  <script src="{{ static.legacy("common/js/jquery-ui.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/jquery.ui.datepicker-ja.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("js/compressor.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/common.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block body %}
  {% set combined = {
    user: user,
    userId: userId,
    usingApp: usingApp,
    packType: packType,
    templateType: templateType,
    addingInformationThankPageUrl: addingInformationThankPageUrl,
    additionalInformationAccountType: AdditionalInformationAccountType,
    verificationToken: verificationToken,
    isMobile: req.isMobile()
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ ProgressBar() }}
  {{ UploadAdditionalInformationForm() }}
  {{ ConfirmAdditionalInformation() }}
  {{ UploadIdentityFiles() }}

  {% raw %}
    <div v-scope="UploadAdditionalInformation()" @vue:mounted="mounted" v-cloak class="bac-c_bla02 mar-b_16 pos_r">
      <div v-if="isLoading" class="reservation-loading-wrap">
        <div class="sl-loading">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div v-if="currentStep === 1" v-scope="UploadAdditionalInformationForm()"></div>
      <div v-if="currentStep === 2" v-scope="ConfirmAdditionalInformation()"></div>
    </div>
  {% endraw %}
  <div class="pad-t_32 pad-r_16 pad-b_96 pad-l_16">
    <img 
      src="{{ static.staticImage("frontend/loading_SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.gif") }}"
      data-src="{{ static.staticImage("SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.webp") }}" 
      data-not-webp-img="{{ static.staticImage("SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.jpg") }}"
      data-loading-lazy="true"
      height="58" width="100" alt="ISMS-AC 認定シンボル"/>
    <p class="tex-a_c pad-t_16 fon-s_12">{{ copyright.getCopyright() }}</p>
  </div>
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const activePackages = initialData.activePackages;
    function UploadAdditionalInformation(props) {
      return {
        numberWithCommas: {{numberWithCommas|safe}},
        pdfIcon: "{{ assets.url('image/common/pdf-icon.png') }}",
        currentStep: 1,
        isLoading: false,
        cacheKey: "HotWaterEnergySaving2025Form",
        addingInformationThankPageUrl: initialData.addingInformationThankPageUrl,
        listAccountType: initialData.additionalInformationAccountType,
        inputFiles: [],
        forms: {
          packType: initialData.packType,
          templateType: initialData.templateType,
          bankName: "",
          branchName: "",
          accountType: initialData.additionalInformationAccountType?.[0] || "",
          accountNumber: "",
          accountHolderNameKana: "",
        },

        errors: {
          bankName: "",
          branchName: "",
          accountType: "",
          accountNumber: "",
          accountHolderNameKana: "",
          inputFiles: "",
        },

        requiredFields: [
          'bankName',
          'branchName',
          'accountType',
          'accountNumber',
          'accountHolderNameKana',
        ],

        defaultFieldKeyMapping: {
          bankName: "銀行名",
          branchName: "支店名",
          accountType: "口座区分",
          accountNumber: "口座番号",
          accountHolderNameKana: "口座名義人（カナ）",
          default: "このフィールド"
        },

        getSubmitErrors() {
          return [...Object.values(this.errors)].filter(e => !!e);
        },

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
          const cachedData = sessionStorage.getItem(this.cacheKey);
          const { timestamp, forms } = cachedData
            ? JSON.parse(cachedData)
            : {};
          const cachedForm = forms || {};
          if (!timestamp || !forms) 
            return
          if (initialData.userId === cachedForm.userId && (new Date().getTime() - timestamp) < 1000 * 60 * 30) {
            this.forms = {
              ...this.forms,
              ...cachedForm
            };
          } else {
            sessionStorage.removeItem(this.cacheKey);
          }
        },
      }
    };

    PetiteVue
      .createApp({UploadAdditionalInformation})
      .mount();
  </script>
{% endblock %}

{% block tracking %}{% endblock %}
