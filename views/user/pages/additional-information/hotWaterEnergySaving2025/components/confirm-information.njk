{% macro ConfirmAdditionalInformation() %}
  <script>
    function ConfirmAdditionalInformation() {
      return {
        $template: '#confirm-additional-information',
        maxIdentityFiles: 2,
        async submit() {
          try {
            this.isLoading = true;
            for (const key in this.forms) {
              if (this.forms[key] === "") {
                delete this.forms[key];
              }
            }
            const formData = new FormData();
            const filenameSet = new Set();
            const listFileNames = [];
            this.inputFiles.forEach((inputFile, index) => {
              const fileName = this.generateFileName(filenameSet, inputFile.file.name);
              listFileNames.push({name: fileName, position: `identityFiles-${index}`});
              formData.append('identityFiles', new Blob([inputFile.file], { type: inputFile.file.type }), fileName);
            });
            for (const key in this.forms) {
              formData.append(key, this.forms[key]);
            }
            formData.append('listFileNames', JSON.stringify(listFileNames));
            formData.append('verificationToken', initialData.verificationToken);
            const response = await axiosClient.post(`/additional-information/confirm/`, formData, {
              headers: {
                'Content-Type': 'multipart/form-data; charset=UTF-8'
              }
            });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }
            sessionStorage.removeItem(this.cacheKey)
            window.location.href = `${this.addingInformationThankPageUrl}?token=${initialData.verificationToken}`;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
        generateFileName(filenameSet, fileName) {
          if (!filenameSet.has(fileName)) {
            filenameSet.add(fileName);
            return fileName;
          }
          const fileExtension = fileName.split('.').pop();
          const baseName = fileName.slice(0, -(fileExtension.length + 1));
          let counter = 1;
          let newFileName = `${baseName}(${counter}).${fileExtension}`;
          while (filenameSet.has(newFileName)) {
            counter++;
            newFileName = `${baseName}(${counter}).${fileExtension}`;
          }
          filenameSet.add(newFileName);
          return newFileName;
        },
        back() {
          this.currentStep = 1;
        },
      };
    }
  </script>
  {% raw %}
    <template id="confirm-additional-information">
      <div class="m_box-shadow mar-b_24">
        <div class="bac-c_white pad_16">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 mar_0">給湯省エネ2025事業<br>入力内容の確認</h1>
        </div>
        <div class="pad_16 bac-c_white">
          <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">本人確認書類</h2>
          <div v-if="inputFiles?.length" class="dis_f mar-b_40">
            <div v-for="(inputFile, index) in inputFiles" :key="'preview-' + index" class="mar-r_16 m_3n-mar-r-0 bor_1_dot_bla06 bor-r_4 wid_100p box-siz_c square">
              <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="inputFile.type === 1 ? inputFile?.url : pdfIcon">
            </div>
            <div v-for="(_, index) in (maxIdentityFiles - inputFiles.length)" :key="'blank-' + index" class="wid_100p mar-r_16 bor_1_tra"></div>
          </div>
          <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_16">補助金の振込先口座情報</h2>
          <p class="mar-b_56 wor-b_b-w">
            {{defaultFieldKeyMapping['bankName']}}：{{forms.bankName}}<br>
            {{defaultFieldKeyMapping['branchName']}}：{{forms.branchName}}<br>
            {{defaultFieldKeyMapping['accountType']}}：{{forms.accountType}}<br>
            {{defaultFieldKeyMapping['accountNumber']}}：{{forms.accountNumber}}<br>
            {{defaultFieldKeyMapping['accountHolderNameKana']}}：{{forms.accountHolderNameKana}}
          </p>
          <div class="pad-b_32">
            <button @click="submit" class="m_btn_ora10 fon-s_16" data-test-id="reservation-confirmation-page-btn" id="id_btn_confirm">この内容で申請する</button>
          </div>
          <div class="tex-a_c pad-b_48">
            <a class="no-alert col_bla10 tex-d_u" href="javascript:void(0);" @click="back">
              <svg class="dis_i-b hei_18 wid_18 fil_bla08 ver-a_m mar-r_8" role="img">
                <use xlink:href="#chevron_left"></use>
              </svg>
              内容を修正する
            </a>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
