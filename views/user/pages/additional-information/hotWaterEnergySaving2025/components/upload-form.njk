{% macro UploadAdditionalInformationForm() %}
  <script>
    function UploadAdditionalInformationForm() {
      return {
        $template: '#upload-additional-information-form',
        maxIdentityFiles: 2,
        maxLength: {
          bankName: 255,
          branchName: 255,
          accountNumber: 255,
          accountHolderNameKana: 255,
        },

        validateRequired(value, field, message) {
          if (!value.trim()) {
            this.errors[field] = message;
            return;
          }
          this.errors[field] = "";
        },

        validateField(field, value) {
          if (!value || (typeof value === "string" && !value.trim())) {
            this.errors[field] = `${this.defaultFieldKeyMapping[field]}を入力してください。`;
            return;
          }

          if (typeof value === "string" && value.length > this.maxLength[field]) {
            this.errors[field] = `${this.defaultFieldKeyMapping[field] || this.defaultFieldKeyMapping.default}は${this.maxLength[field]}文字以内で入力してください。`;
            return;
          }
          this.errors[field] = "";
        },

        validateInput(e) {
          this.validateField(e.target.name, e.target.value);
        },

        formatHalfSizeNumber(num) {
          return num
            .replace(/[０-９]/g, function (s) {
              return String.fromCharCode(s.charCodeAt(0) - 0xfee0);
            })
            .replace(/[^0-9]/g, "");
        },

        validateKatakana(input) {
          const regexKatakanaFullWidth = /^[\u30a0-\u30ff　]+$/;
          const regexSpecialChar = /[！＠＃＄％＾＆＊（）＿＋＝「」『』；’：”￥｜、。•＜＞？]+/;
          const regexAlphaNumeric = /[a-zA-Z0-9]/;
          const regexFullWidthAlphaNumeric = /[Ａ-Ｚａ-ｚ０-９]/;
          return regexKatakanaFullWidth.test(input) && !regexSpecialChar.test(input) && !regexFullWidthAlphaNumeric.test(input) && !regexAlphaNumeric.test(input);
        },

        validateInputKana(e) {
          this.validateFieldKana(e.target.name, e.target.value);
        },

        validateFieldKana(field, value) {
          if (!value.trim()) {
            this.errors[field] = `${this.defaultFieldKeyMapping[field]}を入力してください。`;
            return;
          }
          if (value.length > this.maxLength[field]) {
            this.errors[field] = `${this.defaultFieldKeyMapping[field] || this.defaultFieldKeyMapping.default}は${this.maxLength[field]}文字以内で入力してください。`;
            return;
          }
          if (!this.validateKatakana(value)) {
            this.errors[field] = '全角カタカナで入力してください。';
            return;
          }
          this.errors[field] = "";
        },

        replaceToHankaku() {
          const accountNumber = this.formatHalfSizeNumber(this.forms.accountNumber);
          this.forms.accountNumber = accountNumber;
        },

        handleFormChange() {
          setTimeout(() => {
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
          }, 0);
        },

        trimString(field) {
          this.forms[field] = this.forms[field].replace(/^[\u0020\u3000]+|[\u0020\u3000]+$/g, '').trim();
        },

        async handleRedirectToConfirm() {
          try {
            this.isLoading = true;
            if (!this.inputFiles.length) {
              this.errors.inputFiles = '本人確認書類をアップロードしてください。';
            }
            this.validateField('bankName', this.forms.bankName, );
            this.validateField('branchName', this.forms.branchName, );
            this.validateField('accountNumber', this.forms.accountNumber, );
            this.validateFieldKana('accountHolderNameKana', this.forms.accountHolderNameKana, );
            if (Object.values(this.errors).some(e => !!e)) {
              this.isLoading = false;
              return;
            }
            const response = await axiosClient.post(`/additional-information/`, { verificationToken: initialData.verificationToken });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }
            this.currentStep = 2;
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
            this.isLoading = false;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
      };
    }
  </script>
  {% raw %}
    <template id="upload-additional-information-form">
      <div class="m_box-shadow mar-b_24 bac-c_bla02">
        <div class="bac-c_white pad-l_16 pad-r_16 pad-t_16 pad-b_32 mar-b_16 m_box-shadow">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 pad-b_16 mar_0">給湯省エネ2025事業<br>申請情報を入力</h1>
          <p>補助金の適用を希望する方は必須の手続きです。<br>給湯省エネ2025事業の詳細は<a href="https://faq.curama.jp/--67568e46e6e824625cc30e26" target="_blank" class="col_bla10 tex-d_u">こちら<svg class="dis_i-b hei_16 wid_16 fil_bla10 ver-a_m" role="img"><use xlink:href="#open_in_new"></use></svg></a>をご確認ください。</p>
        </div>
        <form @input="handleFormChange">
          <div class="m_box-shadow pad-t_32 pad-b_32 pad-l_16 pad-r_16 mar-b_24">
            <div class="dis_t pad-b_16">
              <div class="dis_f ali-i_c jus-c_c bac-c_bla08 bor-r_50p flo_l tex-a_c hei_30 wid_30 col_white fon-s_16 fon-w_700">1</div>
              <div class="dis_t-c ver-a_m pad-l_8">
                <p class="fon-w_700 fon-s_16 mar-b_0">本人確認書類をアップロード</p>
              </div>
            </div>
            <p class="mar-b_16">以下のいずれか1点をご提出ください。</p>
            <p class="mar-b_16">＜提出可能な書類＞</p>
            <ul class="m_listnone pad-l_16 mar_0 pad_0">
              <li>運転免許証（裏面記載がある場合は裏面も必要）</li>
              <li>マイナンバーカード（表面のみ）</li>
              <li>住民票の写し（マイナンバーが記載されていないもの）</li>
              <li>在留カードまたは特別永住者証明書</li>
              <li>健康保険被保険者証または後期高齢者医療被保険者証</li>
            </ul>
            <div v-scope="UploadIdentityFiles(maxIdentityFiles)"></div>
          </div>

          <div class="m_box-shadow pad-t_32 pad-b_32 pad-l_16 pad-r_16 mar-b_24">
            <div class="dis_t pad-b_16">
              <div class="dis_f ali-i_c jus-c_c bac-c_bla08 bor-r_50p flo_l tex-a_c hei_30 wid_30 col_white fon-s_16 fon-w_700">2</div>
              <div class="dis_t-c ver-a_m pad-l_8">
                <p class="fon-w_700 fon-s_16 mar-b_0">補助金の振込先口座情報を入力</p>
              </div>
            </div>
            <p class="mar-b_32">振込を希望する銀行口座の情報を入力してください。振込時期の目安は工事完了から半年程度です。</p>
            <dl>
              <dt class="mar-b_4">{{defaultFieldKeyMapping['bankName']}}</dt>
              <dd class="mar-b_16">
                <div class="dis_f jus-c_f-s">
                  <input 
                    v-model="forms.bankName" 
                    type="text" 
                    name="bankName" 
                    class="m_textbox wid_65p"
                    :class="errors.bankName ? 'bor_1_red10' : ''"
                    placeholder="三井住友" 
                    @change="validateInput"
                    @blur="trimString('bankName')"
                    maxlength="255"/>
                  <div class="dis_f jus-c_c ali-i_c wid_15p">銀行</div>
                </div>
                <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.bankName">{{ errors.bankName }}</p>
              </dd>
            </dl>
            <dl>
              <dt class="mar-b_4">{{defaultFieldKeyMapping['branchName']}}</dt>
              <dd class="mar-b_16">
                <div class="dis_f jus-c_f-s">
                  <input 
                    v-model="forms.branchName" 
                    type="text" 
                    name="branchName" 
                    class="m_textbox wid_65p"
                    :class="errors.branchName ? 'bor_1_red10' : ''"
                    placeholder="鶴見" 
                    @change="validateInput"
                    @blur="trimString('branchName')"
                    maxlength="255"/>
                  <div class="dis_f jus-c_c ali-i_c wid_15p">支店</div>
                </div>
                <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.branchName">{{ errors.branchName }}</p>
              </dd>
            </dl>
            <dl class="mar-b_8">
              <dt class="mar-b_4">{{defaultFieldKeyMapping['accountType']}}</dt>
              <dd class="mar-b_16">
                <div class="dis_f">
                  <div v-for="(item, index) in listAccountType" :key="index" class="dis_f jus-c_c ali-i_c">
                    <label class="mar-r_48 dis_f jus-c_c ali-i_c">
                      <input 
                        type="radio" 
                        v-bind:value="item" 
                        name="accountType"
                        class="mar_0"
                        v-model="forms.accountType" 
                        v-on:change="validateRequired(forms.accountType, 'accountType', `${defaultFieldKeyMapping['accountType']}を入力してください。`)"
                        :checked="forms.accountType === item">
                      <div class="mar-l_4">{{ item }}</div>
                    </label>
                  </div>
                </div>
                <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.accountType">{{ errors.accountType }}</p>
              </dd>
            </dl>
            <dl>
              <dt class="mar-b_4">{{defaultFieldKeyMapping['accountNumber']}}</dt>
              <dd class="mar-b_16">
                <input 
                  v-model="forms.accountNumber" 
                  type="tel" 
                  name="accountNumber" 
                  class="m_textbox"
                  :class="errors.accountNumber ? 'bor_1_red10' : ''"
                  placeholder="0123456" 
                  @change="validateInput"
                  @blur="replaceToHankaku"
                  maxlength="255"/>
                <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.accountNumber">{{ errors.accountNumber }}</p>
              </dd>
            </dl>
            <dl>
              <dt class="mar-b_4">{{defaultFieldKeyMapping['accountHolderNameKana']}}</dt>
              <dd class="mar-b_16">
                <input 
                  v-model="forms.accountHolderNameKana" 
                  type="text" 
                  name="accountHolderNameKana" 
                  class="m_textbox"
                  :class="errors.accountHolderNameKana ? 'bor_1_red10' : ''"
                  placeholder="ヤマダ ハナコ" 
                  @change="validateInputKana"
                  @blur="trimString('accountHolderNameKana')"
                  maxlength="255"/>
                <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.accountHolderNameKana">{{ errors.accountHolderNameKana }}</p>
              </dd>
            </dl>
            <div class="pad-t_32">
              <button type="button" @click="handleRedirectToConfirm" class="m_btn_ora10 fon-s_16" data-test-id="upload-adding-information-btn" id="id_btn_confirm">確認画面に進む</button>
              <p class="fon-s_14 col_red10 mar-t_4" v-for="error in getSubmitErrors()">{{ error }}</p>
            </div>
          </div>
        </form>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
