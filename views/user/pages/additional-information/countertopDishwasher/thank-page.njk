{% extends "layout/user/main_layout.njk" %}
{% import "components/data-layer/_macro.njk" as dataLayer %}
{% import "components/security-macros/_macro.njk" as security %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}

{% set title = "卓上食器洗い食洗機の設置工事用情報提供フォーム" %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
{% endblock %}
{% block scripts %}
  <script>
    window.addEventListener('pageshow', (event) => {
      if (event.persisted) {
        location.reload();
      }
    });
  </script>
{% endblock %}
{% block body %}
  {{ security.renderTemplate("currentForm", currentForm | dump) }}
  {{ security.renderTemplate("activePackages", activePackages | dump) }}

  <div class="pos_r m_box-shadow_2-2-p12 bac-c_white pad_16 mar-b_16">
    <ol class="rsv_flow step dis_f m_list_none ove_h mar-b_32">
      <li>入力</li>
      <li>確認</li>
      <li class="is-current">完了</li>
    </ol>
    <div class="pad-b_16">
      <h1 class="fon-s_20 fon-w_700 pad-b_32 mar_0">情報提供の完了</h1>
      <p class="fon-s_14 mar-b_56">ご提供ありがとうございます。<br>
        打ち合わせのご連絡をお待ちください。</p>
      <a href="/" class="m_btn_bla_bor">トップページに戻る</a>
    </div>
  </div>
  <script>
    const currentForm = {{ security.getTemplateContent("currentForm") }}
    const activePackages = {{ security.getTemplateContent("activePackages") }}
  </script>
{% endblock %}
