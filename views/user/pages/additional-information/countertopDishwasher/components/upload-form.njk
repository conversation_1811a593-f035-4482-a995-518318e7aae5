{% macro UploadAdditionalInformationForm() %}
  <script>
    function UploadAdditionalInformationForm() {
      return {
        $template: '#upload-additional-information-form',
        maxLength: {
          faucetModelNumber: 30,
        },

        validateField(field, value) {
          if (!value || (typeof value === "string" && !value.trim())) {
            this.errors[field] = `${this.defaultFieldKeyMapping[field]}を入力してください。`;
            return;
          }

          if (typeof value === "string" && value.length > this.maxLength[field]) {
            this.errors[field] = `${this.defaultFieldKeyMapping[field] || this.defaultFieldKeyMapping.default}は${this.maxLength[field]}文字以内で入力してください。`;
            return;
          }
          this.errors[field] = "";
        },

        validateInput(e) {
          this.validateField(e.target.name, e.target.value);
        },

        handleFormChange() {
          setTimeout(() => {
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
          }, 0);
        },

        trimString(field) {
          this.forms[field] = this.forms[field].replace(/^[\u0020\u3000]+|[\u0020\u3000]+$/g, '').trim();
        },

        async handleRedirectToConfirm() {
          try {
            this.isLoading = true;
            this.validateField('faucetModelNumber', this.forms.faucetModelNumber);
            if (Object.values(this.errors).some(e => !!e)) {
              this.isLoading = false;
              return;
            }
            const response = await axiosClient.post(`/additional-information/`, { verificationToken: initialData.verificationToken });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }
            this.currentStep = 2;
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
            this.isLoading = false;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
      };
    }
  </script>
  {% raw %}
    <template id="upload-additional-information-form">
      <div class="m_box-shadow mar-b_24 bac-c_bla02">
        <div class="c_white pad-l_16 pad-r_16 pad-t_16 pad-b_32 mar-b_16 m_box-shadow">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 pad-b_32 mar_0">卓上食器洗い食洗機の設置工事用<br>情報提供フォーム</h1>
          <p>卓上食器洗い食洗機の設置工事には、分岐水栓を選ぶための情報が必要です。</p>
        </div>
        <form @input="handleFormChange">
          <div class="m_box-shadow pad-t_32 pad-b_32 pad-l_16 pad-r_16 mar-b_24">
            <h2 class="fon-s_20 fon-w_700 mar-t_0 mar-b_16">水栓のラベルを調べる</h2>
            <div class="mar_0a min-w_100p mar-b_16">
              <img :src="imgDishwasher1Src" width="100%" height="auto" alt="銘板を撮影した画像">
            </div>
            <p class="mar-b_24">水栓本体に貼られているラベルをご確認ください。ラベルがなかったり文字が読めない場合は、取扱説明書を確認ください。</p>
            <h2 class="fon-s_20 fon-w_700 mar-t_0 mar-b_16">{{defaultFieldKeyMapping['faucetModelNumber']}}</h2>
            <input
              class="m_textbox ng-untouched ng-pristine ng-valid form-passed"
              v-model="forms.faucetModelNumber"
              maxlength="30"
              placeholder="品番を入力"
              type="text"
              @change="validateInput"
              @blur="trimString('faucetModelNumber')"
              name="faucetModelNumber">
            <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.faucetModelNumber">{{ errors.faucetModelNumber }}</p>
            <p class="mar-t_32 mar-b_32">ラベルに書かれている品番を記載してください。「JWWA」は「日本水道協会」の認定品であることを表すマークであり、品番ではありません。</p>
            <a class="no-alert col_bla10 tex-d_u" href="https://faq.curama.jp/--67b2db26d3a57fe63b11894f" target="_blank">
              品番がわからない方へ<svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-l_4 ver-a_m" role="img"><use xlink:href="#open_in_new"></use></svg>
            </a>
            <div class="pad-b_16 pad-t_32">
              <button type="button" @click="handleRedirectToConfirm" class="m_btn_ora10 fon-s_16" data-test-id="upload-adding-information-btn" id="id_btn_confirm">確認画面に進む</button>
              <p class="fon-s_14 col_red10 mar-t_4" v-for="error in getSubmitErrors()">{{ error }}</p>
            </div>
          </div>
        </form>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
