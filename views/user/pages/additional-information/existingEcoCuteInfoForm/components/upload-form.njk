{% macro UploadAdditionalInformationForm() %}
  <script>
    function UploadAdditionalInformationForm() {
      return {
        $template: '#upload-additional-information-form',
        maxIdentityFiles: 2,

        validateRequired(value, field, message) {
          if (!value.trim()) {
            this.errors[field] = message;
            return;
          }
          this.errors[field] = "";
        },

        handleFormChange() {
          setTimeout(() => {
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
          }, 0);
        },

        imageCompressor(file) {
          return new Promise((resolve, reject) => {
            new Compressor(file, {
              quality: 0.65,
              maxWidth: 3840,
              maxHeight: 3840,
              convertSize: 0,
              success(blob) {
                resolve(blob);
              },
              error() {
                reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
              }
            });
          });
        },

        async handleChangeEcoCuteFile(event, index, field) {
          if (!event.target.files[0]) {
            return;
          }
          const SIZE_5MB = 5 * 1000 * 1000;
          const SIZE_25MB = 25 * 1000 * 1000;

          let file = event.target.files[0];
          if (!new RegExp(/^(image\/(png|jpe?g)|application\/pdf)$/).exec(file.type)) {
            this.errors.ecoCuteAttachments[index] = 'jpg、pngまたはpdf以外のファイルが含まれているため、登録できませんでした。';
            return;
          }

          if (file.type !== "application/pdf") {
            if (file.size > SIZE_25MB) {
              this.errors.ecoCuteAttachments[index] = "サイズが大きすぎる画像が含まれているため、登録できませんでした。\n 25MB以下の画像を選択してください。";
              return;
            }
            if (file.size > SIZE_5MB) {
              const compressedFile = await this.imageCompressor(file);
              if (compressedFile.size > SIZE_5MB) {
                this.errors.ecoCuteAttachments[index] = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                return;
              }
              file = compressedFile;
            }
          }

          this.ecoCuteAttachments[index][field] = {
            name: file.name,
            file: file,
            url: URL.createObjectURL(file),
            type: file.type === 'application/pdf' ? 2 : 1,
          };
          this.errors.ecoCuteAttachments[index] = "";
        },

        removeEcoCuteFile(index, field) {
          this.ecoCuteAttachments[index][field] = {
            name: "",
            file: null,
            url: "",
            type: 1,
          };
        },

        addMoreEcoCute() {
          if (this.ecoCuteAttachments.length < 5) {
            this.ecoCuteAttachments.push({
              nameplate: {
                name: "",
                file: null,
                url: "",
                type: 1,
              },
              fullView: {
                name: "",
                file: null,
                url: "",
                type: 1,
              },
            });
            this.errors.ecoCuteAttachments.push("");
          }
        },

        removeEcoCuteSlot(index) {
          if (this.ecoCuteAttachments.length > 1) {
            this.ecoCuteAttachments.splice(index, 1);
            this.errors.ecoCuteAttachments.splice(index, 1);
          }
        },

        canSubmit() {
          // Check if at least one complete set of images is uploaded
          const hasCompleteSet = this.ecoCuteAttachments.some(attachment =>
            attachment.nameplate.file && attachment.fullView.file
          );
          return hasCompleteSet;
        },

        validateEcoCuteFiles() {
          let hasError = false;
          let hasAtLeastOneValidSet = false;

          this.ecoCuteAttachments.forEach((attachment, index) => {
            // Check if this slot has any files
            const hasNameplate = attachment.nameplate.file;
            const hasFullView = attachment.fullView.file;

            if (hasNameplate || hasFullView) {
              // If any file is uploaded, both must be uploaded
              if (!hasNameplate || !hasFullView) {
                this.errors.ecoCuteAttachments[index] = "銘板画像と全体画像の両方をアップロードしてください。";
                hasError = true;
              } else {
                this.errors.ecoCuteAttachments[index] = "";
                hasAtLeastOneValidSet = true;
              }
            } else {
              // No files in this slot is OK
              this.errors.ecoCuteAttachments[index] = "";
            }
          });

          // Must have at least one complete set of images
          if (!hasAtLeastOneValidSet) {
            this.errors.ecoCuteAttachments[0] = "少なくとも1台のエコキュートの画像をアップロードしてください。";
            hasError = true;
          }

          return !hasError;
        },

        async handleRedirectToConfirm() {
          try {
            this.isLoading = true;

            // Validate EcoCute files (this is the main requirement for this form)
            const ecoCuteFilesValid = this.validateEcoCuteFiles();

            // Check for EcoCute attachment errors
            if (!ecoCuteFilesValid) {
              this.isLoading = false;
              return;
            }

            const response = await axiosClient.post(`/additional-information/`, { verificationToken: initialData.verificationToken });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }

            this.currentStep = 2;
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
            this.isLoading = false;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
      };
    }
  </script>
  {% raw %}
    <template id="upload-additional-information-form">
      <div class="m_box-shadow mar-b_24 bac-c_bla02">
        <div class="bac-c_white pad-l_16 pad-r_16 pad-t_16 pad-b_32 mar-b_16 m_box-shadow">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 pad-b_16 mar_0">
            エコキュート交換工事パック<br>
            情報提供フォーム
          </h1>
          <p>
            エコキュート交換工事パックの利用には、既設エコキュートの情報が必要です。
          </p>
        </div>
        <form @input="handleFormChange">
          <div class="m_box-shadow pad-t_32 pad-b_32 pad-l_16 pad-r_16 mar-b_24">
            <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_16">
                既設エコキュートの写真をアップロード
            </h2>
            <p class="mar-b_32">
                ご自宅にすでに設置されているエコキュートについて、写真のアップロードをお願いします。
            </p>
            <div class="dis_t pad-b_16">
              <div class="dis_t-c bac-c_bla08 bor-r_50p flo_l tex-a_c hei_30 wid_30">
                <span class="col_white fon-s_16 fon-w_700 lin-h_30">1</span>
              </div>
              <div class="dis_t-c ver-a_m pad-l_8">
                <p class="fon-w_700 fon-s_16 mar-b_0">銘板・シールなど品番がわかる画像</p>
              </div>
            </div>
            <p class="mar-b_16">エコキュートの品番が記載されている銘板、シールなどを撮影してください。</p>
            <p class="fon-s_12">例）</p>
            <div class="bac-c_bla02 wid_100p dis_b mar-b_32">
              <img :src="imgEcoCute1Src" width="100%" height="auto" alt="銘板を撮影した画像">
            </div>
            <div class="dis_t pad-b_16">
              <div class="dis_t-c bac-c_bla08 bor-r_50p flo_l tex-a_c hei_30 wid_30">
                <span class="col_white fon-s_16 fon-w_700 lin-h_30">2</span>
              </div>
              <div class="dis_t-c ver-a_m pad-l_8">
                <p class="fon-w_700 fon-s_16 mar-b_0">エコキュート全体と周辺環境がわかる画像</p>
              </div>
            </div>
            <p class="mar-b_16">エコキュートの品番が記載されている銘板、シールなどを撮影してください。</p>
            <p class="fon-s_12">例）</p>
            <div class="bac-c_bla02 wid_100p dis_b mar-b_32">
              <img :src="imgEcoCute2Src" width="100%" height="auto" alt="銘板を撮影した画像">
            </div>

            <div v-for="(attachments, index) in ecoCuteAttachments" :key="index" class="pad_16 bor_1_bla06 bor-r_8 mar-b_16">
                <div class="dis_f jus-c_sb ali-i_c mar-b_16">
                  <p class="fon-s_16 mar-b_0 fon-w_700">回収するエコキュート（{{index + 1}}台目）</p>
                  <button v-if="ecoCuteAttachments.length > 1" type="button" @click="removeEcoCuteSlot(index)" class="m_btn-r cur_p pad_4 lin-h_0 bac-c_red10 bor-r_4" aria-label="削除">
                    <svg class="wid_16 hei_16 fil_white" role="img"><use xlink:href="#close"></use></svg>
                  </button>
                </div>

                <!-- Nameplate Image Upload -->
                <div class="dis_f wid_100p ali-i_c mar-b_16">
                    <div class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r_0 fle-s_0 wid_150 hei_150">
                        <div v-if="!ecoCuteAttachments[index]?.nameplate?.file" class="square wid_100p">
                            <label :for="'nameplate-' + index" class="pos_a top_0 dis_f jus-c_c ali-i_c wid_100p hei_100p cur_p">
                                <div class="tex-a_c">
                                    <svg class="m_icon-s_30 fil_bla08" role="img">
                                        <use xlink:href="#add_a_photo"></use>
                                    </svg>
                                    <p class="fon-s12 col_bla08">上限5MB<br>jpg/png/pdf</p>
                                </div>
                                <input type="file" :id="'nameplate-' + index" :name="'nameplate-' + index" class="wid_0 hei_0 opa_0" accept=".pdf, application/pdf, image/png, image/jpeg, image/jpg" @change="handleChangeEcoCuteFile($event, index, 'nameplate')">
                            </label>
                        </div>
                        <div v-else class="square wid_100p">
                            <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="ecoCuteAttachments[index]?.nameplate?.type === 2 ? pdfIcon : ecoCuteAttachments[index]?.nameplate?.url" @error="removeEcoCuteFile(index, 'nameplate')">
                            <button type="button" @click="removeEcoCuteFile(index, 'nameplate')" class="m_btn-r pos_a rig_0 top_0 cur_p pad_0 lin-h_0 bac-c_04 bor-t-r-r_4" aria-label="閉じる">
                                <svg class="wid_24 hei_24 fil_white" role="img"><use xlink:href="#close"></use></svg>
                            </button>
                        </div>
                    </div>
                    <p>①銘板・シールなど品番がわかる画像</p>
                </div>

                <!-- Full View Image Upload -->
                <div class="dis_f wid_100p ali-i_c mar-b_16">
                    <div class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r_0 fle-s_0 wid_150 hei_150">
                        <div v-if="!ecoCuteAttachments[index]?.fullView?.file" class="square wid_100p">
                            <label :for="'fullView-' + index" class="pos_a top_0 dis_f jus-c_c ali-i_c wid_100p hei_100p cur_p">
                                <div class="tex-a_c">
                                    <svg class="m_icon-s_30 fil_bla08" role="img">
                                        <use xlink:href="#add_a_photo"></use>
                                    </svg>
                                    <p class="fon-s12 col_bla08">上限5MB<br>jpg/png/pdf</p>
                                </div>
                                <input type="file" :id="'fullView-' + index" :name="'fullView-' + index" class="wid_0 hei_0 opa_0" accept=".pdf, application/pdf, image/png, image/jpeg, image/jpg" @change="handleChangeEcoCuteFile($event, index, 'fullView')">
                            </label>
                        </div>
                        <div v-else class="square wid_100p">
                            <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="ecoCuteAttachments[index]?.fullView?.type === 2 ? pdfIcon : ecoCuteAttachments[index]?.fullView?.url" @error="removeEcoCuteFile(index, 'fullView')">
                            <button type="button" @click="removeEcoCuteFile(index, 'fullView')" class="m_btn-r pos_a rig_0 top_0 cur_p pad_0 lin-h_0 bac-c_04 bor-t-r-r_4" aria-label="閉じる">
                                <svg class="wid_24 hei_24 fil_white" role="img"><use xlink:href="#close"></use></svg>
                            </button>
                        </div>
                    </div>
                    <p>②エコキュート全体と周辺環境がわかる画像</p>
                </div>

                <!-- Error Display -->
                <p v-if="errors.ecoCuteAttachments[index]" class="fon-s_14 col_red10 mar-t_4">{{ errors.ecoCuteAttachments[index] }}</p>
            </div>
            <div class="tex-a_r" v-if="ecoCuteAttachments.length < 5">
              <a class="fon-s_14 lin-h_18 col_bla10 dis_i-b m_clearfix cur_p" @click="addMoreEcoCute">
              回収するエコキュートを追加する
              <svg class="dis_i-b wid_18 hei_18 flo_r fil_bla10 mar-l_4" role="img">
                <use xlink:href="#add_circle"></use>
              </svg>
              </a>
            </div>
            <div class="pad-t_56">
              <button type="button" @click="handleRedirectToConfirm" class="m_btn_ora10 fon-s_16 mar-b_16" :disabled="isLoading || !canSubmit()" data-test-id="upload-adding-information-btn" id="id_btn_confirm">
                <span v-if="!isLoading">確認画面に進む</span>
                <span v-if="isLoading">処理中...</span>
              </button>
              <p class="fon-s_14 col_red10 mar-t_4" v-for="error in getSubmitErrors()">{{ error }}</p>
            </div>
          </div>
        </form>
      </div>
    </template>
  {% endraw %}
{% endmacro %}