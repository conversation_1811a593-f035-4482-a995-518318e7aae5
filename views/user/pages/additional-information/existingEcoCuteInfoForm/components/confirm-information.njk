{% macro ConfirmAdditionalInformation() %}
  <script>
    function ConfirmAdditionalInformation() {
      return {
        $template: '#confirm-additional-information',
        maxIdentityFiles: 2,
        isSubmitting: false,
        csrfToken: document.querySelector('meta[name="_csrf"]')?.content || '',
        verificationToken: initialData.verificationToken,
        cacheKey: "ExistingEcoCuteInfoForm",
        addingInformationThankPageUrl: initialData.addingInformationThankPageUrl,

        async submit() {
          console.log('Submit function called');
          console.log('Available properties:', {
            addingInformationThankPageUrl: this.addingInformationThankPageUrl,
            cacheKey: this.cacheKey,
            verificationToken: initialData.verificationToken,
            ecoCuteAttachments: this.ecoCuteAttachments,
            inputFiles: this.inputFiles
          });

          if (this.isSubmitting) return;

          this.isSubmitting = true;
          this.isLoading = true;

          try {
            const formData = new FormData();
            const filenameSet = new Set();
            const listFileNames = [];

            // Handle identity files if any
            if (this.inputFiles && this.inputFiles.length > 0) {
              this.inputFiles.forEach((inputFile, index) => {
                const fileName = this.generateFileName(filenameSet, inputFile.file.name);
                listFileNames.push({name: fileName, position: `identityFiles-${index}`});
                formData.append('identityFiles', new Blob([inputFile.file], { type: inputFile.file.type }), fileName);
              });
            }

            // Handle EcoCute attachment files (main requirement) - use additionalFiles field name
            console.log('Processing EcoCute attachments:', this.ecoCuteAttachments);

            if (!this.ecoCuteAttachments || this.ecoCuteAttachments.length === 0) {
              console.error('No EcoCute attachments found!');
              alert('エラー: エコキュートの画像が見つかりません。ページを再読み込みして再度アップロードしてください。');
              location.reload();
              return;
            }

            this.ecoCuteAttachments.forEach((attachment, index) => {
              if (attachment.nameplate.file) {
                const fileName = this.generateFileName(filenameSet, attachment.nameplate.file.name);
                listFileNames.push({name: fileName, position: `additionalFiles-${index}-nameplate`});
                formData.append('additionalFiles', new Blob([attachment.nameplate.file], { type: attachment.nameplate.file.type }), fileName);
                console.log(`Added nameplate file for slot ${index}:`, fileName);
              }
              if (attachment.fullView.file) {
                const fileName = this.generateFileName(filenameSet, attachment.fullView.file.name);
                listFileNames.push({name: fileName, position: `additionalFiles-${index}-fullView`});
                formData.append('additionalFiles', new Blob([attachment.fullView.file], { type: attachment.fullView.file.type }), fileName);
                console.log(`Added fullView file for slot ${index}:`, fileName);
              }
            });

            console.log('Total files to upload:', listFileNames.length);
            console.log('File names list:', listFileNames);

            if (listFileNames.length === 0) {
              console.error('No files to upload!');
              alert('エラー: アップロードするファイルがありません。');
              this.isSubmitting = false;
              this.isLoading = false;
              return;
            }

            // Add any form data if exists
            if (this.forms) {
              for (const key in this.forms) {
                if (this.forms[key] !== "") {
                  formData.append(key, this.forms[key]);
                }
              }
            }

            formData.append('listFileNames', JSON.stringify(listFileNames));
            formData.append('verificationToken', initialData.verificationToken);
            formData.append('packType', initialData.packType);
            formData.append('templateType', initialData.templateType);

            // Submit to server
            const response = await axiosClient.post(`/additional-information/confirm/`, formData, {
              headers: {
                'Content-Type': 'multipart/form-data; charset=UTF-8'
              }
            });

            if (response?.data?.error) {
              console.error('Server returned error:', response.data.error);
              location.reload();
              return;
            }

            console.log('Form submission successful, proceeding to redirect...');

            // Clear cache and redirect
            sessionStorage.removeItem(this.cacheKey);
            window.onbeforeunload = null;

            // Debug logging
            console.log('Redirecting to thank page...');
            console.log('addingInformationThankPageUrl:', this.addingInformationThankPageUrl);
            console.log('verificationToken:', initialData.verificationToken);

            const thankPageUrl = `${this.addingInformationThankPageUrl}?token=${initialData.verificationToken}`;
            console.log('Full redirect URL:', thankPageUrl);

            window.location.href = thankPageUrl;

          } catch (e) {
            console.error('Submission error:', e);
            location.reload();
          } finally {
            this.isSubmitting = false;
            this.isLoading = false;
          }
        },
        generateFileName(filenameSet, fileName) {
          if (!filenameSet.has(fileName)) {
            filenameSet.add(fileName);
            return fileName;
          }
          const fileExtension = fileName.split('.').pop();
          const baseName = fileName.slice(0, -(fileExtension.length + 1));
          let counter = 1;
          let newFileName = `${baseName}(${counter}).${fileExtension}`;
          while (filenameSet.has(newFileName)) {
            counter++;
            newFileName = `${baseName}(${counter}).${fileExtension}`;
          }
          filenameSet.add(newFileName);
          return newFileName;
        },

        back() {
          this.currentStep = 1;
        },
      };
    }
  </script>
  {% raw %}
    <template id="confirm-additional-information">
      <div class="m_box-shadow pad-t_16 pad-b_16 pad-r_16 pad-l_16 mar-b_16">
        <ol class="rsv_flow step dis_f m_list_none ove_h mar-b_32">
          <li>入力</li>
          <li class="is-current">確認</li>
          <li>完了</li>
        </ol>
        <h1 class="fon-s_20 fon-w_700 pad-b_24 mar_0">エコキュート交換工事パック<br>入力内容の確認</h1>

        <!-- EcoCute Images Section -->
        <div v-if="ecoCuteAttachments?.length">
          <div v-for="(attachment, index) in ecoCuteAttachments" :key="'ecocute-' + index" class="mar-b_56">
            <p class="fon-s_16 mar-b_16 fon-w_700">回収するエコキュート（{{index + 1}}台目）</p>

            <!-- Nameplate Image -->
            <div v-if="attachment.nameplate?.file" class="dis_f wid_100p ali-i_c mar-b_16">
              <div class="wid_100p bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0" style="width: 161.5px; height: 161.5px;">
                <div class="mar-r_16 m_3n-mar-r-0 bor_1_dot_bla06 bor-r_4 wid_100p box-siz_c square">
                  <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="attachment.nameplate.type === 1 ? attachment.nameplate?.url : pdfIcon" @error="$event.target.style.display='none'">
                </div>
              </div>
              <p>①銘板・シールなど品番がわかる画像</p>
            </div>

            <!-- Full View Image -->
            <div v-if="attachment.fullView?.file" class="dis_f wid_100p ali-i_c">
              <div class="wid_100p bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0" style="width: 161.5px; height: 161.5px;">
                <div class="mar-r_16 m_3n-mar-r-0 bor_1_dot_bla06 bor-r_4 wid_100p box-siz_c square">
                  <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="attachment.fullView.type === 1 ? attachment.fullView?.url : pdfIcon" @error="$event.target.style.display='none'">
                </div>
              </div>
              <p>②エコキュート全体と周辺環境がわかる画像</p>
            </div>
          </div>
        </div>

        <!-- Form Submission -->
        <form @submit.prevent="submit" method="post">
          <input type="hidden" name="_csrf" :value="csrfToken">
          <input type="hidden" name="verificationToken" :value="verificationToken">

          <div class="tex-a_c">
            <button v-if="!isSubmitting" type="submit" class="m_btn_ora10 fon-s_16 mar-b_16 no-alert" :disabled="isSubmitting">送信する</button>
            <button v-if="isSubmitting" type="button" class="m_btn_ora10 fon-s_16 mar-b_24 no-alert cur_d" :disabled="true">
              <div class="loading-animation">
                <span class="mar-r_4"></span>
                <span class="mar-r_4"></span>
                <span></span>
              </div>
            </button>
          </div>
        </form>

        <div class="tex-a_c pad-b_32 pad-t_16">
          <a class="no-alert col_bla10 tex-d_u" href="javascript:void(0);" @click="back">
            <svg class="dis_i-b hei_18 wid_18 fil_bla08 ver-a_m mar-r_8" role="img">
              <use xlink:href="#chevron_left"></use>
            </svg>
            内容を修正する
          </a>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}