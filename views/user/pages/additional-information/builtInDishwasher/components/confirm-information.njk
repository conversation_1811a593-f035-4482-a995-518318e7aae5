{% macro ConfirmAdditionalInformation() %}
  <script>
    function ConfirmAdditionalInformation() {
      return {
        $template: '#confirm-additional-information',
        async submit() {
          try {
            this.isLoading = true;
            for (const key in this.forms) {
              if (this.forms[key] === "") {
                delete this.forms[key];
              }
            }
            const formData = new FormData();
            for (const key in this.forms) {
              formData.append(key, this.forms[key]);
            }
            formData.append('verificationToken', initialData.verificationToken);
            const response = await axiosClient.post(`/additional-information/confirm/`, formData, {
              headers: {
                'Content-Type': 'multipart/form-data; charset=UTF-8'
              }
            });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }
            sessionStorage.removeItem(this.cacheKey)
            window.location.href = `${this.addingInformationThankPageUrl}?token=${initialData.verificationToken}`;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
        back() {
          this.currentStep = 1;
        },
      };
    }
  </script>
  {% raw %}
    <template id="confirm-additional-information">
      <div class="m_box-shadow mar-b_24">
        <div class="bac-c_white pad_16">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 pad-b_16 mar_0">ビルトイン食洗機交換工事パック<br>情報提供フォーム</h1>
        </div>
        <div class="bac-c_white pad_16">
          <h2 class="fon-s_20 fon-w_700 mar-t_0 mar-b_8">既設ビルトイン食洗機の品番</h2>
          <p class="mar-b_40 wor-w_b-w">{{forms.faucetModelNumber}}</p>
          <div class="pad-b_32">
            <button @click="submit" class="m_btn_ora10 fon-s_16" data-test-id="reservation-confirmation-page-btn" id="id_btn_confirm">この内容で申請する</button>
          </div>
          <div class="tex-a_c pad-b_48">
            <a class="no-alert col_bla10 tex-d_u" href="javascript:void(0);" @click="back">
              <svg class="dis_i-b hei_18 wid_18 fil_bla08 ver-a_m mar-r_8" role="img">
                <use xlink:href="#chevron_left"></use>
              </svg>
              内容を修正する
            </a>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
