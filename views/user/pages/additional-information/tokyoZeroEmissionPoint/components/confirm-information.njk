{% macro ConfirmAdditionalInformation() %}
  <script>
    function ConfirmAdditionalInformation() {
      return {
        $template: '#confirm-additional-information',
        maxIdentityFiles: 2,
        async submit() {
          try {
            this.isLoading = true;
            for (const key in this.forms) {
              if (this.forms[key] === "") {
                delete this.forms[key];
              }
            }
            const formData = new FormData();
            const filenameSet = new Set();
            const listFileNames = [];
            this.inputFiles.forEach((inputFile, index) => {
              const fileName = this.generateFileName(filenameSet, inputFile.file.name);
              listFileNames.push({name: fileName, position: `identityFiles-${index}`});
              formData.append('identityFiles', new Blob([inputFile.file], { type: inputFile.file.type }), fileName);
            });
            this.additionalAttachments.forEach((attachment, index) => {
              if (attachment?.nameplate?.file) {
                const fileName = this.generateFileName(filenameSet, attachment.nameplate.file.name);
                listFileNames.push({name: fileName, position: `additionalFiles-${index}-nameplate`});
                formData.append('additionalFiles', new Blob([attachment.nameplate.file], { type: attachment.nameplate.file.type }), fileName);
              }
              if (attachment?.indoorUnit?.file) {
                const fileName = this.generateFileName(filenameSet, attachment.indoorUnit.file.name);
                listFileNames.push({name: fileName, position: `additionalFiles-${index}-indoorUnit`});
                formData.append('additionalFiles', new Blob([attachment.indoorUnit.file], { type: attachment.indoorUnit.file.type }), fileName);
              }
              if (attachment?.otherFile?.file) {
                const fileName = this.generateFileName(filenameSet, attachment.otherFile.file.name);
                listFileNames.push({name: fileName, position: `additionalFiles-${index}-otherFile`});
                formData.append('additionalFiles', new Blob([attachment.otherFile.file], { type: attachment.otherFile.file.type }), fileName);
              }
            });
            for (const key in this.forms) {
              formData.append(key, this.forms[key]);
            }
            formData.append('listFileNames', JSON.stringify(listFileNames));
            formData.append('verificationToken', initialData.verificationToken);
            formData.append('packType', initialData.packType);
            formData.append('templateType', initialData.templateType);
            const response = await axiosClient.post(`/additional-information/confirm/`, formData, {
              headers: {
                'Content-Type': 'multipart/form-data; charset=UTF-8'
              }
            });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }
            sessionStorage.removeItem(this.cacheKey)
            window.location.href = `${this.addingInformationThankPageUrl}?token=${initialData.verificationToken}`;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
        generateFileName(filenameSet, fileName) {
          if (!filenameSet.has(fileName)) {
            filenameSet.add(fileName);
            return fileName;
          }
          const fileExtension = fileName.split('.').pop();
          const baseName = fileName.slice(0, -(fileExtension.length + 1));
          let counter = 1;
          let newFileName = `${baseName}(${counter}).${fileExtension}`;
          while (filenameSet.has(newFileName)) {
            counter++;
            newFileName = `${baseName}(${counter}).${fileExtension}`;
          }
          filenameSet.add(newFileName);
          return newFileName;
        },
        back() {
          this.currentStep = 1;
        },
      };
    }
  </script>
  {% raw %}
    <template id="confirm-additional-information">
      <div class="m_box-shadow mar-b_24">
        <div class="bac-c_white pad_16">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 pad-b_16 mar_0">東京ゼロエミポイント<br>申請内容の確認</h1>
        </div>
        <div class="bac-c_white pad_16">
          <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">本人確認書類</h2>
          <div v-if="inputFiles?.length" class="dis_f mar-b_40">
            <div v-for="(inputFile, index) in inputFiles" :key="'preview-' + index" class="mar-r_16 m_3n-mar-r-0 bor_1_dot_bla06 bor-r_4 wid_100p box-siz_c square">
              <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="inputFile.type === 1 ? inputFile?.url : pdfIcon">
            </div>
            <div v-for="(_, index) in (maxIdentityFiles - inputFiles.length)" :key="'blank-' + index" class="wid_100p mar-r_16 bor_1_tra"></div>
          </div>
        </div>
        <div v-for="(attachments, index) in additionalAttachments" :key="index">
          <div class="bac-c_white pad_16" v-if="attachments?.nameplate?.file || attachments?.indoorUnit?.file || attachments?.otherFile?.file">
            <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">回収する工アコン（{{ index+1 }}台目）</h2>
            <div v-if="attachments?.nameplate?.file" id="attachment-container" class="dis_f wid_100p ali-i_c mar-b_16">
              <div id="attachment-input-wrap" class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0 wid_150 hei_150">
                <div class="square wid_100p">
                  <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="attachments?.nameplate?.type === 2 ? pdfIcon : attachments?.nameplate?.url">
                </div>
              </div>
              <p>①銘板の写真</p>
            </div>
            <div v-if="attachments?.indoorUnit?.file" id="attachment-container" class="dis_f wid_100p ali-i_c mar-b_16">
              <div id="attachment-input-wrap" class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0 wid_150 hei_150">
                <div class="square wid_100p">
                  <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="attachments?.indoorUnit?.type === 2 ? pdfIcon : attachments?.indoorUnit?.url">
                </div>
              </div>
              <p>②室内機の写真</p>
            </div>
            <div v-if="attachments?.otherFile?.file" id="attachment-container" class="dis_f wid_100p ali-i_c mar-b_16">
              <div id="attachment-input-wrap" class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0 wid_150 hei_150">
                <div class="square wid_100p">
                  <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="attachments?.otherFile?.type === 2 ? pdfIcon : attachments?.otherFile?.url">
                </div>
              </div>
              <p>③その他の製造年がわかる写真</p>
            </div>
          </div>
        </div>
        <div class="pad-r_16 pad-b_32 pad-l_16">
          <button @click="submit" class="m_btn_ora10 fon-s_16" data-test-id="reservation-confirmation-page-btn" id="id_btn_confirm">この内容で申請する</button>
        </div>
        <div class="tex-a_c pad-b_48">
          <a class="no-alert col_bla10 tex-d_u" href="javascript:void(0);" @click="back">
            <svg class="dis_i-b hei_18 wid_18 fil_bla08 ver-a_m mar-r_8" role="img">
              <use xlink:href="#chevron_left"></use>
            </svg>
            内容を修正する
          </a>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
