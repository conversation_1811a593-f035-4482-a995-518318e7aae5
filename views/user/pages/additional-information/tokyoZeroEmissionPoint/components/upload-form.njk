{% macro UploadAdditionalInformationForm() %}
  <script>
    function UploadAdditionalInformationForm() {
      return {
        $template: '#upload-additional-information-form',
        maxAdditionSlot: 10,
        maxIdentityFiles: 2,

        validateRequired(value, field, message) {
          if (!value.trim()) {
            this.errors[field] = message;
            return;
          }
          this.errors[field] = "";
        },

        handleFormChange() {
          setTimeout(() => {
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
          }, 0);
        },

        addMoreSlot() {
          if (this.additionalAttachments.length < this.maxAdditionSlot) {
            this.additionalAttachments.push({
              nameplate: {
                name: "",
                file: null,
                url: "",
                type: 1,
              },
              indoorUnit: {
                name: "",
                file: null,
                url: "",
                type: 1,
              },
              otherFile: {
                name: "",
                file: null,
                url: "",
                type: 1,
              },
            });
            this.errors.additionalAttachments.push("");
          }
        },

        removeSlot(index) {
          this.additionalAttachments.splice(index, 1);
          this.errors.additionalAttachments.splice(index, 1);
        },

        getFieldError(index, field) {
          const existError = this.errors.additionalAttachments.find(e => e.id === index);
          if (existError) {
            return existError[field];
          }
          return "";
        },

        async handleRedirectToConfirm() {
          try {
            this.isLoading = true;
            if (!this.inputFiles.length) {
              this.errors.inputFiles = '本人確認書類をアップロードしてください。';
            }
            for (const field of this.requiredFields) {
              if (!this.forms[field]) {
                this.errors[field] = this.errors[field] || `${this.defaultFieldKeyMapping[field] || this.defaultFieldKeyMapping.default}を入力してください。`;
              } else {
                this.errors[field] = "";
              }
            }
            if (this.getSubmitErrors().length) {
              this.isLoading = false;
              return;
            }
            const response = await axiosClient.post(`/additional-information/`, { verificationToken: initialData.verificationToken });
            if (response?.data?.error) {
              alert('エラーが発生しました')
              location.reload();
              return;
            }
            this.currentStep = 2;
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), forms: this.forms}));
            this.isLoading = false;
          } catch (e) {
            alert('エラーが発生しました')
            location.reload();
          }
        },
      };
    }
  </script>
  {% raw %}
    <template id="upload-additional-information-form">
      <div class="m_box-shadow mar-b_24 bac-c_bla02">
        <div class="bac-c_white pad-l_16 pad-r_16 pad-t_16 pad-b_32 mar-b_16 m_box-shadow">
          <div v-scope="ProgressBar()"></div>
          <h1 class="fon-s_20 fon-w_700 pad-b_16 mar_0">東京ゼロエミポイント<br>申請情報を入力</h1>
          <p>制度の適用を希望する方は必須の手続きです。<br>東京ゼロエミポイントの詳細は<a :href="packType == 1 ? 'https://faq.curama.jp/--6715d2c92bb680c013bd7653' : 'https://faq.curama.jp/--675a6983d58c37849921e002'" target="_blank" class="col_bla10 tex-d_u">こちら<svg class="dis_i-b hei_16 wid_16 fil_bla10 ver-a_m" role="img"><use xlink:href="#open_in_new"></use></svg></a>をご確認ください。</p>
        </div>
        <form @input="handleFormChange">
          <div class="m_box-shadow pad-t_32 pad-b_32 pad-l_16 pad-r_16 mar-b_24">
            <div class="dis_t pad-b_16">
              <div v-if="packType == 1" class="dis_f ali-i_c jus-c_c bac-c_bla08 bor-r_50p flo_l tex-a_c hei_30 wid_30 col_white fon-s_16 fon-w_700">1</div>
              <div class="dis_t-c ver-a_m" :class="packType == 1 ? 'pad-l_8' : ''">
                <p class="fon-w_700 fon-s_16 mar-b_0">本人確認書類をアップロード（全員）</p>
              </div>
            </div>
            <p class="mar-b_16">以下のいずれか1点をご提出ください。</p>
            <p class="mar-b_16">＜提出可能な書類＞</p>
            <ul class="m_listnone pad-l_16 mar_0 pad_0">
              <li>運転免許証（裏面記載がある場合は裏面も必要）</li>
              <li>マイナンバーカード（表面のみ）</li>
              <li>住民基本台帳カード</li>
              <li>健康保険被保険者証/国民健康保険被保険者証</li>
              <li>共済組合 組合会員証</li>
              <li>船員保険被保険者証</li>
              <li>在留カード</li>
              <li>特別永住者証明書</li>
              <li>障がい者手帳</li>
            </ul>
            <div v-scope="UploadIdentityFiles(maxIdentityFiles)"></div>
            <div v-if="packType != 1" class="pad-t_56">
              <button type="button" @click="handleRedirectToConfirm" class="m_btn_ora10 fon-s_16 mar-b_16" data-test-id="upload-adding-information-btn" id="id_btn_confirm">確認画面に進む</button>
              <p class="fon-s_14 col_red10 mar-t_4" v-for="error in getSubmitErrors()">{{ error }}</p>
            </div>
          </div>

          <div v-if="packType == 1" class="m_box-shadow pad-t_32 pad-b_32 pad-l_16 pad-r_16 mar-b_24">
            <div class="dis_t pad-b_16">
              <div class="dis_f ali-i_c jus-c_c bac-c_bla08 bor-r_50p flo_l tex-a_c hei_30 wid_30 col_white fon-s_16 fon-w_700">2</div>
              <div class="dis_t-c ver-a_m pad-l_8">
                <p class="fon-w_700 fon-s_16 mar-b_0">回収するエアコンの写真をアップロード（長期値引の対象になる場合のみ）</p>
              </div>
            </div>
            <div class="bac-c_bla02 pad_16 bor-r_16 mar-b_24">
              <div class="dis_f ali-i_c mar-b_8 lin-h_0 fon-s_14">
                <svg class="wid_18 hei_18 fil_bla10 mar-r_4" role="img"><use xlink:href="#help"></use></svg>長期値引とは？
              </div>
              <p class="fon-s_14">長期値引とは、製造年から15年以上経過したエアコンを交換する場合に値引き額がアップする仕組みです。当てはまる場合は、回収するエアコンについて<span class="ttl_marker">1台につき下記の写真</span>をご提出ください。</p>
            </div>
            <p class="mar-b_16 fon-s_16 fon-w_700">①銘板を撮影した画像</p>
            <p class="mar-b_16 fon-s_12">※製造年が記載されていない場合は、別途「③その他の製造年がわかる画像」をご提出ください。</p>
            <p class="fon-s_12">例）</p>
            <div class="mar_0a min-w_100p mar-b_32">
              <img :src="imgAircon1Src" width="100%" height="auto" alt="銘板を撮影した画像">
            </div>
            <p class="mar-b_16 fon-s_16 fon-w_700">②室内機の全体が写った画像</p>
            <p class="fon-s_12">例）</p>
            <div class="mar_0a min-w_100p mar-b_32">
              <img :src="imgAircon2Src" width="100%" height="auto" alt="室内機の全体が写った画像">
            </div>
            <p class="mar-b_16 fon-s_16 fon-w_700">③その他の製造年がわかる画像</p>
            <p class="mar-b_16 fon-s_12 mar-b_40">※①銘板に製造年が載っていない場合は、製造年がわかる写真を添付してください。</p>

            <div v-for="(attachments, index) in additionalAttachments" :key="index+new Date().getTime()" class="pad_16 bor_1_bla06 bor-r_8 mar-b_24" v-scope="UploadAdditionalFiles(index)"></div>

            <div class="tex-a_r" v-if="additionalAttachments.length < maxAdditionSlot">
              <a class="fon-s_14 lin-h_18 col_bla10 dis_i-b m_clearfix" @click="addMoreSlot">回収するエアコンを追加する<svg class="dis_i-b wid_18 hei_18 flo_r fil_bla10 mar-l_4" role="img"><use xlink:href="#add_circle"></use></svg></a>
            </div>

            <div class="pad-t_56">
              <button type="button" @click="handleRedirectToConfirm" class="m_btn_ora10 fon-s_16 mar-b_16" data-test-id="upload-adding-information-btn" id="id_btn_confirm">確認画面に進む</button>
              <p class="fon-s_14 col_red10 mar-t_4" v-for="error in getSubmitErrors()">{{ error }}</p>
            </div>
          </div>
        </form>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
