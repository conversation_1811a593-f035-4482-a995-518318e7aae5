{% extends "layout/user/main_layout.njk" %}
{% import "shared/lp/featured-common-parts.njk" as commonParts %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}

{% set breadcrumbTitle = "卓上食洗機 給水不要パック" %}
{% set sns_shareUrl = "https://curama.jp/countertop-dishwasher-pack/" %}

{% block head %}
  <meta property="og:url" content="{{ sns_shareUrl }}">
  <meta name="twitter:card" content="summary_large_image">
  <link rel="stylesheet" href="{{ assets.url("css/user/layout_aircon-sale.css") }}">
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link rel="stylesheet" href="{{ assets.url("css/user/scroll-hint.css") }}"/>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&family=Noto+Serif+JP:wght@200..900&display=swap" rel="stylesheet">
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
  <script src="{{ assets.url("js/scroll-hint.js") }}"></script>
  <script>
    const merchantProducts = [
      {
        name: "siroca 卓上食洗機+分岐水栓+長期保証 SS-M151 シルバー(W/S)  少人数向け（1～3人用）ベーシックタイプ",
        image: "{{ assets.url("image/user/lp-dishwasher/img_ss-m151.jpg") }}",
        description: "独自機能の「トルネード除菌洗浄™75」と4つの洗浄コースを選べるベーシックタイプ。シンプルな製品が好きな方にオススメ。",
        sku: "PAK00027",
        gtin13: "4589919812431",
        brand: "siroca",
        price: 69800
      },
      {
        name: "siroca 卓上食洗機+分岐水栓+長期保証 SS-MU251 シルバー(W/S)  少人数向け（1～3人用）ベーシックタイプ",
        image: "{{ assets.url("image/user/lp-dishwasher/img_ss-mu251.jpg") }}",
        description: "独自機能の「トルネード除菌洗浄™75」に加え、UVライトを搭載。水を使わずマスクや哺乳瓶などの除菌ができる「UV除菌専用コース」なら、肌に触れるものをより清潔にご利用いただけます。",
        sku: "PAK00028",
        gtin13: "4589919820207",
        brand: "siroca",
        price: 85800
      },
      {
        name: "siroca 卓上食洗機+分岐水栓+長期保証 SS-MA251 シルバー(W/S)  少人数向け（1～3人用）ベーシックタイプ",
        image: "{{ assets.url("image/user/lp-dishwasher/img_ss-ma251.jpg") }}",
        description: "独自機能の「トルネード除菌洗浄™75」に加え、洗浄後に自動でドアが開く「オートオープン」機能を搭載。洗い終わりの結露が気になる方、自然乾燥させたい方にオススメ。",
        sku: "PAK00029",
        gtin13: "4589919820214",
        brand: "siroca",
        price: 85800
      },
    ]
    const productsJsonLd = merchantProducts.map(product => ({
      "@type": "Product",
      "name": product.name,
      "image": [product.image],
      "description": product.description,
      "sku": product.sku,
      "gtin13": product.gtin13,
      "brand": {
        "@type": "http://schema.org/Brand",
        "name": product.brand
      },
      "offers": {
        "@type": "Offer",
        "url": window.location.origin + "/countertop-dishwasher-pack/",
        "priceCurrency": "JPY",
        "price": product.price,
        "availability": "https://schema.org/InStock"
      }
    }))
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify({
      "@context": "https://schema.org/",
      "@graph": productsJsonLd
    });
    document.head.appendChild(script);
  </script>
{% endblock %}

{% block body %}
  {% set combined = {
    activePackages: activePackages,
    reservationPageUrl: reservationPageUrl,
    isMobile: req.isMobile()
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ dataLayerScript() }}

  {% raw %}
  <div v-scope="LandingDishwasher()" @vue:mounted="mounted" v-cloak class="bac-c_bla02 mar-b_16">
    <div class="bac-c_04 bot_0 pad_8 pos_f wid_100p z-i_999 preview_bottom_button">
      <a class="m_btn_ora10 fon-s_18" :href="reservationPageUrl">予約する</a>
    </div>
    <section class="bac-c_white">
      <div v-if="!isMobile" id="lp-header" class="dis_f wid_100p fle-d_c bac-p_l-b min-h_400 pad_24 jus-c_s-b" :style="'background-image: url('+ imgHeaderIntroPcBgSrc + ');'">
        <div class="dis_f ali-i_c">
          <h1 class="intro-text-dishwasher dis_f fle-d_c mar_0 fon-f_noto-serif">
            <span class="lin-h_1p2 fon-s_42">自由時間を</span>
            <span class="lin-h_1 fon-s_42">手に入れよう</span>
          </h1>
          <div class="dis_f jus-c_c ali-i_c">
            <div class="bor-r_50p bac-c_white tex-a_c dis_f ali-i_c jus-c_c fle-d_c wid_110 hei_110">
              <div class="fon-s_14 fon-w_700 lin-h_18 fon-f_noto-san">分岐水栓を<br>取り付けで</div>
              <span class="col_blu08 fon-w_700 fon-s_20 fon-f_noto-san">自動給水</span>
            </div>
          </div>
        </div>
        <div class="tex-a_r let-s_0_15 lin-h_20">
          <span class="mar-r_8 intro-description fon-f_noto-san pad_4 fon-s_20">
            <span class="fon-w_n">Siroca </span>卓上食洗機
          </span>
          <br>
          <span class="mar-r_8 intro-description fon-f_noto-san pad_4 fon-s_20">
            給水不要パック <span class="fon-w_n fon-s_21">¥69,800</span>
            <span class="fon-w_n fon-s_12">税込から</span>
          </span>
        </div>
      </div>

      <div v-if="isMobile" id="lp-header" class="dis_f wid_100p fle-d_c pos_r">
        <img :src="imgHeaderIntroSpBgSrc" class="wid_100p hei_a" alt="siroca 卓上食洗機">
        <div class="dis_f ali-i_c pos_a wid_100p hei_100p pad_5vw fle-d_c jus-c_f-s ali-i_f-s">
          <h1 class="intro-text-dishwasher dis_f fle-d_c fon-f_noto-serif mar-b_4vw">
            <span class="lin-h_1p2 fon-s_9vw">自由時間を</span>
            <span class="lin-h_1 fon-s_9vw">手に入れよう</span>
          </h1>
          <div class="dis_f jus-c_c ali-i_c pos_a bot_5vw rig_5vw">
            <div class="bor-r_50p bac-c_white tex-a_c dis_f ali-i_c jus-c_c fle-d_c wid_30vw hei_30vw">
              <div class="fon-s_4vw fon-w_700 lin-h_5vw fon-f_noto-san">分岐水栓を<br>取り付けで</div>
              <span class="col_blu08 fon-w_700 fon-s_5vw fon-f_noto-san">自動給水</span>
            </div>
          </div>
          <div class="let-s_0_1 lin-h_3vw">
            <span class="intro-description fon-f_noto-san pad_2vw fon-s_5vw">
              Siroca 卓上食洗機
            </span>
            <br>
            <span class="intro-description fon-f_noto-san pad_2vw fon-s_5vw">
              給水不要パック ¥69,800<span class="fon-w_n fon-s_2p5vw">税込から</span>
            </span>
          </div>
        </div>
      </div>

      <div class="dis_f ali-i_c bor-b_bla05 bac-c_white" :class="isMobile ? 'stickyNavigatorSp' : 'stickyNavigatorPc'" id="fixedNavigator">
        <a href="#list" class="dis_f ali-i_c jus-c_c pad-l_16 pad-r_16 min-h_56 wid_1_3p tex-a_c tex-d_u col_bla10" :class="isMobile ? '' : 'bor-r_bla05'">製品一覧</a>
        <a href="#step" class="dis_f ali-i_c jus-c_c pad-l_16 pad-r_16 min-h_56 wid_1_3p tex-a_c tex-d_u col_bla10" :class="isMobile ? '' : 'bor-r_bla05'">予約の流れ</a>
        <a href="#guarantee" class="dis_f ali-i_c jus-c_c pad-l_16 pad-r_16 min-h_56 wid_1_3p tex-a_c tex-d_u col_bla10">長期保証</a>
      </div>

      <div class="dis_f ali-i_c bor-b_bla05 bac-c_white pos_f" :class="isMobile ? 'stickyNavigatorSp' : 'stickyNavigatorPc'" id="stickyNavigator" style="display: none;">
        <a href="#list" class="dis_f ali-i_c jus-c_c pad-l_16 pad-r_16 min-h_56 wid_1_3p tex-a_c tex-d_u col_bla10" :class="isMobile ? '' : 'bor-r_bla05'">製品一覧</a>
        <a href="#step" class="dis_f ali-i_c jus-c_c pad-l_16 pad-r_16 min-h_56 wid_1_3p tex-a_c tex-d_u col_bla10" :class="isMobile ? '' : 'bor-r_bla05'">予約の流れ</a>
        <a href="#guarantee" class="dis_f ali-i_c jus-c_c pad-l_16 pad-r_16 min-h_56 wid_1_3p tex-a_c tex-d_u col_bla10">長期保証</a>
      </div>

      <div class="pad-t_8 pad-b_32 pad-r_16 pad-l_16">
        <div class="dis_f jus-c_c">
          <div class="mar-b_8 bor-r_12 bac-c_white max-w_400 wid_100p">
            <h2 class="fon-w_700 mar-t_32 mar-b_24 tex-a_c lin-h_32 fon-s_24">
              人気のsiroca食洗機＋<br>
              <span>取付工事＋5年保証パック</span>
            </h2>
            <div class="max-w_400 wid_100p mar_0a mar-b_8">
              <img :src="imgIntroBannerSrc" class="wid_100p">
            </div>
          </div>
        </div>
        <div class="bor_1_bla05 bac-c_white bor-r_8 mar-t_16 mar-b_32 lin-h_24 max-w_400 wid_100p mar_0a">
          <input id="detail_pack" class="m_acd-check dis_n" type="checkbox">
          <label class="m_acd-title" for="detail_pack">
            <div class="fon-s_14 pad_16 mar_0 dis_f wid_100p">
              <img :src="imgPersonPackSrc" class="hei_a wid_24 mar-r_8">
              <div class="dis_f ali-i_c fle_1 fon-s_16 fon-w_800">
                パック内容の詳細
              </div>
              <svg class="dis_i-b hei_24 wid_24 fil_bla06 mar-l_16 m_tf-180" role="img"><use xlink:href="#expand_more"></use></svg>
            </div>
          </label>
          <div class="m_acd-content pad-r_16 pad-l_16 dis_n">
            <div class="pad-b_24 tex-link_d">
              <div class="fon-w_700">以下がすべて含まれた料金です。</div>
              <div class="fon-w_700">・商品</div>
              <p>&emsp;&emsp;siroca卓上食洗機<br>&emsp;&emsp;分岐水栓<br>&emsp;&emsp;上記2点の配送料</p>
              <div class="fon-w_700">・付帯工事</div>
              <p>&emsp;&emsp;卓上食洗機取付工事<br>&emsp;&emsp;分岐水栓の取付工事<br>&emsp;&emsp;出張料*</p>
              <div class="fon-w_700">・５年間の長期保証</div>
              <p class="fon-s_12 mar-t_16 lin-h_18">必要な分岐水栓の種類や作業内容、訪問先（<a href="https://faq.curama.jp/--67adbef6bc61ca77b501702a" target="_blank" class="col_bla10 tex-d_u">エリア別追加料金</a>）によって追加費用が必要になる可能性がありますので、ご了承ください。</p>
            </div>
          </div>
        </div>
        <div class="max-w_400 wid_100p mar_0a">
          <img :src="imgIntro1Src" width="100%" height="auto" class="mar-b_8 bor-r_20">
          <p class="fon-s_14 mar-b_8 lin-h_26">1度の洗い物で10分かかるとして、1日30分。1年で考えると約8日間もの大切なあなたの時間が、洗い物に使われています。お子さんと遊んだり、家族で笑い合ったり、くらしのマーケットが届けるのは、そんなかけがえのない時間です。</p>
        </div>
      </div>
    </section>

    <section id="introduction" class="pad_16 pad-b_40 pad-t_40 scroll-anchor">
      <div class="mar-b_32 wid_100p tex-a_c">
        <h3 class="fon-w_700 mar_0 lin-h_32 fon-s_24">人気のsiroca製品を<br>取付工事とセットでご提供</h3>
      </div>
      <div class="max-w_400 wid_100p mar_0a">
        <div class="pad-b_32">
          <div class="dis_f ali-i_c fon-s_18 fon-w_800 mar-b_8">
            <img :src="iconCheckSrc" class="hei_a wid_30 mar-r_8">
            給水時間をゼロにする分岐水栓
          </div>
          <img :src="imgIntro2Src" width="100%" height="auto" class="mar-b_8 bor-r_20">
          <p class="fon-s_14 mar-b_8 lin-h_26">卓上食洗機を使う上で、意外に大変なのが給水作業。水を持ち上げて入れなくていいから、手間がかからず、疲れも残りません。</p>
        </div>
        <div class="pad-b_32">
          <div class="dis_f ali-i_c fon-s_18 fon-w_800 mar-b_8">
            <img :src="iconCheckSrc" class="hei_a wid_30 mar-r_8">
            独自技術で高い洗浄力のsiroca
          </div>
          <img :src="imgIntro3Src" width="100%" height="auto" class="mar-b_8 bor-r_20">
          <div class="infor-wrap">
            <input id="spec-intro3" class="acd-spec-check dis_n" type="checkbox">
            <label class="acd-spec-label" for="spec-intro3">
              <span class="acd-spec-txt">続きを読む<span class="acd-spec-icon"></span></span>
            </label>
            <div class="tex-a_j infor-content dishwasher-infor-content">
              <div class="lin-h_26">
                <p class="fon-s_14 mar-b_16">siroca独自の「トルネード除菌洗浄™75」は、最高75℃の高温＆高水圧で、360°あらゆる角度から洗浄。頑固な油汚れも溶かし、手間なくピカピカに。</p>
                <p class="fon-s_14 mar-b_16">さらに、高圧洗浄効果で99.9％除菌*されるので、見えないところまで安心安全にご利用いただけます。</p>
                <p class="fon-s_12">* ［試験機関］一般財団法人日本食品分析センター［試験方法］寒天平板培養法（菌の種類1種）。食器の種類、形状、位置、汚れの程度などにより結果は異なります。</p>
              </div>
            </div>
          </div>
        </div>
        <div class="pad-b_32">
          <div class="dis_f ali-i_c fon-s_18 fon-w_800 mar-b_8">
            <img :src="iconCheckSrc" class="hei_a wid_30 mar-r_8">
            購入から取付まで頼める手軽さ
          </div>
          <img :src="imgIntro4Src" width="100%" height="auto" class="mar-b_8 bor-r_20">
          <div class="infor-wrap">
            <input id="spec-intro4" class="acd-spec-check dis_n" type="checkbox">
            <label class="acd-spec-label" for="spec-intro4">
              <span class="acd-spec-txt">続きを読む<span class="acd-spec-icon"></span></span>
            </label>
            <div class="tex-a_j infor-content dishwasher-infor-content">
              <div class="lin-h_26">
                <p class="fon-s_14 mar-b_16">暮らしに関わる400種類以上のサービスを掲載するくらしのマーケットには、家電量販店やECサイトにはない「ユーザーの評価が高い工事ノウハウ」が集まっています。</p>
                <p class="fon-s_14">そんな工事品質のプロが取付に必要な分岐水栓や工事の手配を担当するので、手間なく高品質のサービスをご利用いただけます。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mar-b_24 mar-t_8 wid_100p tex-a_c">
        <h3 class="fon-w_700 mar_0 lin-h_32 fon-s_24">綺麗になるだけじゃない<br>嬉しい機能も充実</h3>
      </div>
      <div class="max-w_400 wid_100p mar_0a">
        <div class="pad-b_32">
          <div class="dis_f ali-i_c fon-s_18 fon-w_800 mar-b_8">
            <img :src="iconLightSrc" class="hei_a wid_30 mar-r_8">
            清潔が長続きするUVライト照射
          </div>
          <img :src="imgIntro5Src" width="100%" height="auto" class="mar-b_8 bor-r_20">
          <div class="infor-wrap">
            <input id="spec-intro5" class="acd-spec-check dis_n" type="checkbox">
            <label class="acd-spec-label" for="spec-intro5">
              <span class="acd-spec-txt">続きを読む<span class="acd-spec-icon"></span></span>
            </label>
            <div class="tex-a_j infor-content dishwasher-infor-content">
              <div class="lin-h_26">
                <p class="fon-s_14 mar-b_16">SS-MU251だけの新機能「UVライト照射」で「トルネード除菌洗浄™75」の99.9％除菌<sup class="fon-s_10">*１</sup>が、8時間以上も続きます。</p>
                <p class="fon-s_14 mar-b_16">スマートフォンケースやマスク、哺乳瓶など菌が気になる普段使いのモノも、水を使わない「UV除菌専用コース」なら、お手軽に99.99%<sup class="fon-s_10">*２</sup>除菌ができます。</p>
                <p class="fon-s_12 mar-b_16">*１［試験機関］一般財団法人日本食品分析センター。［試験方法］寒天平板培養法（菌の種類１種）。食器の種類、形状、位置、汚れの程度などにより結果は異なります。</p>
                <p class="fon-s_12">*２UVライト照射面のみ除菌できます。対象物全体を除菌する場合は、角度を変えたり裏返したりして全体を照射してください。<br>*２［試験機関］日本微生物クリニック株式会社［測定方法］寒天平板混釈法［試験方法］紫外線による除菌性能評価試験（菌の種類１種）素材、形状、位置、汚れの程度などにより結果は異なります。UVライトからの距離が遠い場所、UVライトが直接当たらない場所では、除菌効果を期待できない場合があります。</p>
              </div>
            </div>
          </div>
        </div>
        <div class="pad-b_32">
          <div class="dis_f ali-i_c fon-s_18 fon-w_800 mar-b_8">
            <img :src="iconAirSrc" class="hei_a wid_30 mar-r_8">
            乾燥効率を上げるオートオープン
          </div>
          <img :src="imgIntro6Src" width="100%" height="auto" class="mar-b_8 bor-r_20">
          <p class="fon-s_14 mar-b_8 lin-h_26">SS-MA251は「オートオープン」機能付き。洗い終わるとドアが自動で開き、高圧洗浄でこもった蒸気を逃して結露を抑えてくれます。</p>
        </div>
      </div>
    </section>

    <section id="list" class="bac-c_white pad_16 pad-b_40 pad-t_40 scroll-anchor">
      <div class="mar-b_32 wid_100p tex-a_c">
        <h3 class="fon-w_700 mar_0 lin-h_30 fon-s_24">希望に合わせて選べる<br>3つのラインナップ
          <p class="fon-w_800 mar-b_16 tex-a_c fon-s_18">少人数向け（1～3人用）</p>
        </h3>
      </div>
      <div v-for="pack in displayPackages" :key="pack.code" class="bac-c_white bor-r_8 bor_1_bla05 mar_0a mar-b_24 max-w_400">
        <div :class="isMobile ? 'pad_16' : 'pad_24'">
          <p class="fon-w_800 tex-a_c fon-s_18">{{ pack.name }}</p>
          <p class="fon-w_800 tex-a_c fon-s_12">（{{ pack.model }}）</p>
          <div class="dis_f jus-c_c ali-i_c">
            <img :src="pack.imgSrc" width="80%" height="auto" :alt="pack.name + '（' + pack.model + '）'">
          </div>
          <p class="fon-w_700 mar-t_8 tex-a_c fon-s_14">製品+設置工事+長期保証</p>
          <p class="fon-w_700 tex-a_c mar-b_16" :class="isMobile ? 'fon-s_10' : 'fon-s_12'">
            <span class="fon-s_16">¥</span>
            <span class="fon-s_28">{{numberWithCommas(pack.fee)}}</span>
            <span class="dis_i-b fon-s_10">税込</span>
          </p>
          <p class="fon-s_14 mar-b_8">{{ pack.description }}</p>
          <div class="dis_f jus-c_c ali-i_c min-h_30">
            <a href="#compare" class="tex-a_c tex-d_u col_bla10">機能を比較する</a>
          </div>
          <div class="pad-t_16 mar-b_24">
            <a class="m_btn_ora10 max-w_400" :disabled="pack.outOfStock || null" onclick="trackGAEvent('dishwasher', '1stCVボタン', 'パナソニックFシリーズ')" @click="submitPackage(pack.code)">{{ pack.outOfStock ? '売り切れ' : '予約する' }}</a>
          </div>
        </div>
      </div>
    </section>

    <section id="step" class="mar-b_16 pad-l_16 pad-r_16 pad-t_32 pad-b_32 scroll-anchor">
      <h2 class="fon-w_700 fon-s_24 mar-b_24 tex-a_c">予約から受付までの流れ</h2>
      <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_400" :class="isMobile ? 'pad_16' : 'pad_24'">
        <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
          <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">1</span></span>
        </p>
        <div class="tex-a_c wid_100p">
          <img :src="imgFlowSurveyReserveSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
        </div>
        <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">購入申し込み</p>
        <p>「予約する」ボタンからフォームに進み、必要事項を入力して申し込み。</p>
      </div>
      <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_400" :class="isMobile ? 'pad_16' : 'pad_24'">
        <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
          <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">2</span></span>
        </p>
        <div class="tex-a_c wid_100p pad-b_8">
          <img :src="imgFlowTapSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
        </div>
        <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">水栓の品番を送る</p>
        <p class="mar-b_8">予約完了後に届く専用フォームから、水栓の品番を送ってください。</p>
        <p class="fon-s_12">※品番が特定できない方へは工事ができません。</p>
      </div>
      <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_400" :class="isMobile ? 'pad_16' : 'pad_24'">
        <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
          <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">3</span></span>
        </p>
        <div class="tex-a_c wid_100p pad-b_4">
          <img :src="imgFlowTelSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
        </div>
        <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">分岐水栓が特定できたら<br>お電話で打ち合わせ</p>
        <p>訪問日時と工事内容について、お電話にて事前に打ち合わせを行います。</p>
      </div>
      <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_400" :class="isMobile ? 'pad_16' : 'pad_24'">
        <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
          <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">4</span></span>
        </p>
        <div class="tex-a_c wid_100p pad-b_8">
          <img :src="imgFlowPaymentSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
        </div>
        <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">事前決済</p>
        <p class="mar-b_8">入金案内のメールが届きましたら、内容をご確認の上、決済用のメールから事前決済の手続きをお願いします。</p>
        <p class="fon-s_12">※お支払いはクレジットカード決済のみ。</p>
      </div>
      <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_400" :class="isMobile ? 'pad_16' : 'pad_24'">
        <p class="mar-t_0 mar-b_16 fon-s_16 fon-w_700 tex-a_c">
          <span class="num-tag">ステップ<span class="fon-s_18 mar-l_2">5</span></span>
        </p>
        <div class="tex-a_c wid_100p pad-b_8">
          <img :src="imgFlowDeliverySrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
        </div>
        <p class="fon-s_18 fon-w_700 tex-a_c mar-b_8">商品の事前配送</p>
        <p class="mar-b_8">ご予約日の3〜4日前にご指定の配送先に届きます。</p>
        <p class="fon-s_12">※ご予約日に配送と取付をまとめて希望される場合は+1,100円で承ります。</p>
      </div>
      <div class="tex-a_c mar-b_16 pad-t_4">
        <img :src="iconTriangleRoundedSrc" width="24" height="auto">
      </div>
      <div class="white_card mar-b_16 mar_0a bor_1_bla05 max-w_400" :class="isMobile ? 'pad_16' : 'pad_24'">
        <p class="fon-s_18 fon-w_700 tex-a_c mar-b_16">ご予約日にプロが取り付けに伺います</p>
        <div class="tex-a_c wid_100p mar-b_16">
          <img :src="imgFlowInstallationSrc" :class="isMobile ? 'wid_90' : 'wid_110'" height="auto">
        </div>
        <p class="fon-s_12">※申し込みから最短2週間</p>
      </div>
    </section>

    <section id="guarantee" class="bac-c_white pad_16 pad-b_40 pad-t_40 mar-b_24 scroll-anchor">
      <div class="max-w_400 wid_100p mar_0a">
        <div class="dis_f bor-r_20 ali-i_c mar-b_24 bac-p_r-c">
          <img :src="imgSectionGuaranteeSrc" class="wid_100p" height="auto">
        </div>
        <div class="mar-b_24 lin-h_26">
          <p>長期保証期間中に起きた自然故障に対して、無償修理を何度でも受けることができます。</p>
          <p>※卓上食洗機 給水不要パックのみを対象としたサービスです。</p>
        </div>
        <div class="tex-a_c">
          <a href="https://faq.curama.jp/--67b3e92a9cf27bad21666566" class="m_btn_bla_bor fon-s_16" target="_blank">長期保証について</a>
        </div>
      </div>
    </section>

    <section id="compare" class="m_box-shadow_2-2-p2 bac-c_white pad-t_32 pad-b_32 scroll-anchor">
      <div class="pad-l_16 pad-r_16">
        <h2 class="fon-w_700 fon-s_24 mar_0 tex-a_c mar-b_24">機能比較</h2>
      </div>
      <input id="spec-compare-table" class="acd-spec-check dis_n" type="checkbox">
      <div class="scrollable mar-b_32" id="scrollable">
        <div class="compare-table-container cur_p bor_1_bla05 pad-l_16 pad-r_16 pad-t_32 pad-b_32 bor-r_12 mar-l_16 mar-r_16">
          <table class="compare-table mar-b_32">
            <tbody>
              <tr>
                <th></th>
                <td v-for="pack in packageSpecifications" :key="pack.code">          
                  <div class="tex-a_c fon-s_16 fon-w_800">{{ pack.name }}</div>
                  <div class="dis_f jus-c_c ali-i_c">
                    <img :src="pack.imgSrc" width="70%" height="auto">
                  </div>
                </td>
              </tr>
              <tr>
                <th class="bor_1_bla06">製品名</th>
                <td class="bor_1_bla06" v-for="pack in packageSpecifications" :key="pack.code">{{ pack.model }}</td>
              </tr>
              <tr>
                <th class="bor_1_bla06">カラー</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.color }}</td>
              </tr>
            </tbody>
          </table>

          <p class="fon-w_700 mar-b_8">洗浄コース・時間(約)</p>
          <table class="compare-table">
            <tbody>
              <tr>
                <th class="bor_1_bla06">標準</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.standardMode }}</td>
              </tr>
              <tr>
                <th class="bor_1_bla06">念入り</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.intensiveMode }}</td>
              </tr>
              <tr>
                <th class="bor_1_bla06">おいそぎ</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.quickMode }}</td>
              </tr>
              <tr>
                <th class="bor_1_bla06">ソフト</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.gentleMode }}</td>
              </tr>
              <tr>
                <th class="bor_1_bla06">高温すすぎ</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.highTempRinse }}</td>
              </tr>
              <tr>
                <th class="bor_1_bla06">庫内洗浄コース</th>
                <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.interiorCleaningCycle }}</td>
              </tr>
            </tbody>
          </table>

          <div class="compare-table-hidden">
            <p class="fon-w_700 mar-b_8">その他の機能</p>
            <table class="compare-table mar-b_32">
              <tbody>
                <tr>
                  <th class="bor_1_bla06">予約運転(約)</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.scheduledOperationApprox }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">チャイルドロック</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.childLock }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">使用水量（約）</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.waterConsumptionApprox }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">専用洗剤の標準使用量（約）</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.detergentUsageStandardApprox }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">標準収納容量(約)</th>
                  <td class="bor_1_bla06 whi-s_p-l" v-for="pack in packageSpecifications" :key="pack.code">{{ pack.standardLoadCapacityApprox }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">対応可能な大皿サイズ（約）</th>
                  <td class="bor_1_bla06 whi-s_p-l" v-for="pack in packageSpecifications" :key="pack.code">{{ pack.maxCompatiblePlateSizeApprox }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">UV除菌機能</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.uvSterilizationFunction }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">温風乾燥機能</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.heatedDryingFunction }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">オートオープン機能</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.autoOpenFunction }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">水道工事</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.plumbingWork }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">給水方式</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.waterSupplyMethod }}</td>
                </tr>
              </tbody>
            </table>

            <p class="fon-w_700 mar-b_8">製品仕様</p>
            <table class="compare-table">
              <tbody>
                <tr>
                  <th class="bor_1_bla06">本体サイズ</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.unitDimensions }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">本体重量</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.unitWeight }}</td>
                </tr>
                <tr>
                  <th class="bor_1_bla06">消費電力</th>
                  <td class="bor_1_bla06"v-for="pack in packageSpecifications" :key="pack.code">{{ pack.powerConsumption }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="compare-table-btn-wrap tex-a_c mar-t_16">
        <label class="acd-spec-label cur_p testCollapse" for="spec-compare-table" @click="handleGoToCollapseButton">
          <span class="acd-spec-txt">
            その他の機能<span class="acd-spec-icon"></span>
          </span>
        </label>
      </div>
      <div class="pad_16 dis_f jus-c_f-e mar-t_16">
        <a href="https://www.siroca.co.jp/support/%E9%A3%9F%E5%99%A8%E6%B4%97%E3%81%84%E4%B9%BE%E7%87%A5%E6%A9%9F%EF%BC%9A%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8-5f633314ea3b11001ea85b0f" target="_blank" rel="noopener noreferrer" class="dis_f lin-h_18 col_bla10 tex-d_u">メーカーサイト
          <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-l_4" role="img">
            <use xlink:href="#open_in_new"></use>
          </svg>
        </a>
      </div>
    </section>

    <section id="faq" class="mar-b_16 pad-l_16 pad-r_16 pad-t_32 pad-b_32 scroll-anchor">
      <div class="max-w_400 wid_100p mar_0a">
        <h2 class="fon-w_700 fon-s_24 mar-b_24 tex-a_c">よくある質問</h2>
        <div class="bac-c_white bor-r_12 mar-b_16 mar_0a bor_1_bla05">
          <dl>
            <dt class="dis_f ali-i_c bor-b_1_das_bla05 pad_16 lin-h_20">
              <span class="col_blu08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">Q</span>
              <p class="fon-w_700">対象エリアはどこですか？</p>
            </dt>
            <dd class="dis_f ali-i_f-s pad_16">
              <span class="col_ora08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">A</span>
              <p>全国に対応しています。エリアによっては追加料金が発生する場合がありますので、詳しくは<a class="col_bla10 tex-d_u" href="https://faq.curama.jp/--67adbef6bc61ca77b501702a" target="_blank">こちら</a>をご確認ください。</p>
            </dd>
          </dl>
        </div>
        <div class="bac-c_white bor-r_12 mar-b_16 mar_0a bor_1_bla05">
          <dl>
            <dt class="dis_f ali-i_c bor-b_1_das_bla05 pad_16 lin-h_20">
              <span class="col_blu08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">Q</span>
              <p class="fon-w_700">現在使っている分岐水栓の取り外しには対応してもらえますか？</p>
            </dt>
            <dd class="dis_f ali-i_f-s pad_16">
              <span class="col_ora08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">A</span>
              <p>はい。オプションとして有料で対応可能です。取り外した分岐水栓は、ご自身で処分してください。</p>
            </dd>
          </dl>
        </div>
        <div class="bac-c_white bor-r_12 mar-b_16 mar_0a bor_1_bla05">
          <dl>
            <dt class="dis_f ali-i_c bor-b_1_das_bla05 pad_16 lin-h_20">
              <span class="col_blu08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">Q</span>
              <p class="fon-w_700">運転中の音はどれぐらいの大きさですか？</p>
            </dt>
            <dd class="dis_f ali-i_f-s pad_16">
              <span class="col_ora08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">A</span>
              <p>56〜58デシベル（IEC基準）です。<br>銀行の窓口周辺程度になります。<br>※状況や条件等により感じ方や大きさは違いますので、あくまでも参考としてお考えください。</p>
            </dd>
          </dl>
        </div>
        <div class="bac-c_white bor-r_12 mar-b_16 mar_0a bor_1_bla05">
          <dl>
            <dt class="dis_f ali-i_c bor-b_1_das_bla05 pad_16 lin-h_20">
              <span class="col_blu08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">Q</span>
              <p class="fon-w_700">設置する時に気をつけることはありますか？</p>
            </dt>
            <dd class="dis_f ali-i_f-s pad_16">
              <span class="col_ora08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">A</span>
              <p>加熱すすぎを行うため、本体上面にある給水口（排気口）から蒸気が出ます。蒸気による変色の原因になりますので、近い位置にものを置かないようにしてください。</p>
            </dd>
          </dl>
        </div>
        <div class="bac-c_white bor-r_12 mar-b_16 mar_0a bor_1_bla05">
          <dl>
            <dt class="dis_f ali-i_c bor-b_1_das_bla05 pad_16 lin-h_20">
              <span class="col_blu08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">Q</span>
              <p class="fon-w_700">使用できる洗剤を教えてください。</p>
            </dt>
            <dd class="dis_f ali-i_f-s pad_16">
              <span class="col_ora08 fon-w_500 fon-s_24 mar-r_16 lin-h_1">A</span>
              <div>
                <p class="mar-b_8">必ず「食器洗い機専用洗剤」をご利用ください。専用洗剤であれば、液体・粉末・タブレットのどのタイプでもお使いいただけます。</p>
                <p>台所用洗剤は、故障の原因になるため、ご利用できません。もし、下洗いで利用された場合は、しっかりとすすいでください。</p>
              </div>
            </dd>
          </dl>
        </div>
        <p class="tex-a_c pad-t_12">
          <a class="col_bla10 tex-d_u" target="_blank" href="https://faq.curama.jp/--67a1ec0c13826d4378de7b79">その他の質問・注意事項</a>
        </p>
      </div>
    </section>
  </div>
  {% endraw %}
  <aside>
    <div class="bac-c_white">
      <ul class="m_list_none dis_t mar_0a mar-b_16 pad-t_40">
        <li class="dis_t-c pad-r_16">
          <div class="twitter-back">
            <a href="https://twitter.com/intent/tweet?url={{ sns_shareUrl }}%3futm_source%3dtwitter%26utm_medium%3dsocial%26utm_campaign%3dtwitter_share_button&hashtags=%e3%81%8f%e3%82%89%e3%81%97%e3%81%ae%e3%83%9e%e3%83%bc%e3%82%b1%e3%83%83%e3%83%88" target="_blank"><img src="{{ assets.url("image/user/common/social/icon_X.svg") }}" alt="X" width="40" height="40"></a>
            <script>
              !function (d, s, id) {
                var js,
                  fjs = d.getElementsByTagName(s)[0],
                  p = /^http:/.test(d.location)
                    ? 'http'
                    : 'https';
                if (!d.getElementById(id)) {
                  js = d.createElement(s);
                  js.id = id;
                  js.src = p + '://platform.twitter.com/widgets.js';
                  fjs
                    .parentNode
                    .insertBefore(js, fjs);
                }
              }(document, 'script', 'twitter-wjs');
            </script>
          </div>
        </li>
        <li class="dis_t-c pad-r_16">
          <div class="facebook-back">
            <a href="https://www.facebook.com/sharer/sharer.php?u={{ sns_shareUrl }}%3futm_source%3dfacebook%26utm_medium%3dsocial%26utm_campaign%3dfacebook_share_button" onclick="window.open(this.href,'FBwindow','width=650,height=450,menubar=no,toolbar=no,scrollbars=yes');return false;" title="Facebookでシェア"><img src="{{ assets.url("image/user/common/social/icon_facebook.svg") }}" alt="Facebook" width="40" height="40"></a>
          </div>
        </li>
        <li class="dis_t-c">
          <div class="line-back">
            <a href="https://timeline.line.me/social-plugin/share?url={{ sns_shareUrl }}%3futm_source%3dline%26utm_medium%3dsocial%26utm_campaign%3dline_share_button" target="_blank"><img src="{{ assets.url("image/user/common/social/icon_line.svg") }}" alt="LINE" width="40" height="40"></a>
          </div>
        </li>
      </ul>
      <div class="tex-a_c {% if req.isUserApp() %}{{ 'pad-b_144' }}{% else %}{{'pad-b_40'}}{% endif %}">
        <button id="copyLink" class="m_btn_bla_bor wid_a fon-s_14 dis_f ali-i_c mar_0a" data-clipboard-text="{{ title }} | {{ sns_shareUrl }}?utm_source=self_share&utm_medium=referral&utm_campaign=self_share_button"><svg class="m_icon-s_18 mar-r_8" role="img"><use xlink:href="#content_copy"></use></svg>URLをコピーする</button>
      </div>
    </div>
  </aside>
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const activePackages = initialData.activePackages;
    window.nk_params = {
      pagetype: "list",
      itemid: activePackages.map(p => p.id),
      price: [0],
      quantity: [0],
      orderid: "",
      totalprice: 0,
    };
    window.ecommerce_data = {
      pagetype: "list",
      items: activePackages.map((pack, idx) => {
        return {
          item_id: pack.id,
          item_name: pack.code,
          index: idx + 1,
          item_list_name: pack?.name,
          item_list_id: pack?.name,
          item_category: 'countertop-dishwasher-pack',
          item_category2: pack?.name,
          price: pack.fee || 0,
          currency: "JPY",
          quantity: 1,
        };
      }),
    };
    notifyEcommerceEventToApp(window.ecommerce_data);
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "initial_search_results_displayed",
    });
    function LandingDishwasher(props) {
      return {
        numberWithCommas: {{numberWithCommas|safe}},
        imgHeaderIntroPcBgSrc: "{{ assets.url('image/user/lp-dishwasher/img_header-pc.jpg') }}",
        imgHeaderIntroSpBgSrc: "{{ assets.url('image/user/lp-dishwasher/img_header-sp.jpg') }}",
        imgPersonPackSrc: "{{ assets.url('image/user/common/icon/icon_person_pack.svg') }}",
        imgIntroBannerSrc: "{{ assets.url('image/user/lp-dishwasher/img_intro_banner.svg') }}",
        imgIntro1Src: "{{ assets.url('image/user/lp-dishwasher/img_content01.jpg') }}",
        imgIntro2Src: "{{ assets.url('image/user/lp-dishwasher/img_content02.jpg') }}",
        imgIntro3Src: "{{ assets.url('image/user/lp-dishwasher/img_content03.jpg') }}",
        imgIntro4Src: "{{ assets.url('image/user/lp-dishwasher/img_content04.jpg') }}",
        imgIntro5Src: "{{ assets.url('image/user/lp-dishwasher/img_content05.jpg') }}",
        imgIntro6Src: "{{ assets.url('image/user/lp-dishwasher/img_content06.jpg') }}",
        imgDishwasherSSM151Src: "{{ assets.url('image/user/lp-dishwasher/img_ss-m151.jpg') }}",
        imgDishwasherSSMA251Src: "{{ assets.url('image/user/lp-dishwasher/img_ss-ma251.jpg') }}",
        imgDishwasherSSMU251Src: "{{ assets.url('image/user/lp-dishwasher/img_ss-mu251.jpg') }}",
        imgSectionGuaranteeSrc: "{{ assets.url('image/user/lp-dishwasher/img_section_guarantee.png') }}",
        imgFlowSurveyReserveSrc: "{{ assets.url('image/user/common/step/img_flow_survey_reserve.svg') }}",
        imgFlowTapSrc: "{{ assets.url('image/user/common/step/img_flow_tap.svg') }}",
        imgFlowTelSrc: "{{ assets.url('image/user/common/step/img_flow_tel.svg') }}",
        imgFlowPaymentSrc: "{{ assets.url('image/user/common/step/img_flow_payment.svg') }}",
        imgFlowDeliverySrc: "{{ assets.url('image/user/common/step/img_flow_delivery.svg') }}",
        imgFlowInstallationSrc: "{{ assets.url('image/user/common/step/img_flow_installation.svg') }}",
        iconPlusSrc: "{{ assets.url('image/user/common/icon/icon_plus.svg') }}",
        iconCheckSrc: "{{ assets.url('image/user/common/icon/icon_check.svg') }}",
        iconLightSrc: "{{ assets.url('image/user/common/icon/icon_light.svg') }}",
        iconAirSrc: "{{ assets.url('image/user/common/icon/icon_air.svg') }}",
        iconTriangleRoundedSrc: "{{ assets.url('image/user/common/icon/icon_triangle-rounded.svg') }}",
        activePackages: initialData.activePackages,
        reservationPageUrl: initialData.reservationPageUrl,
        isOpenCompare: false,
        displayPackages: [],
        packageSpecifications: [],
        isMobile: initialData.isMobile,

        submitPackage(packageCode) {
          let url = `${this.reservationPageUrl}?packageCode=${packageCode}`;
          window.location.href = url;
        },

        handleGoToCollapseButton() {
          if (this.isOpenCompare) {
            this.isOpenCompare = false;
            window.location.href = '#compare';
          } else {
            this.isOpenCompare = true;
          }
        },

        mounted() {
          const packageData = [
            {
              code: "PAK00027",
              model: "SS-M151",
              brand: "siroca",
              description: "独自機能の「トルネード除菌洗浄™75」と4つの洗浄コースを選べるベーシックタイプ。シンプルな製品が好きな方にオススメ。",
              imgSrc: this.imgDishwasherSSM151Src,
              color: "シルバー（W/S）",
              standardMode: "1時間30分", 
              intensiveMode: "2時間10分",
              quickMode: "39分",
              gentleMode: "1時間45分",
              highTempRinse: "-",
              interiorCleaningCycle: "-",
              scheduledOperationApprox: "1時間～6時間まで1時間単位",
              childLock: "×",
              waterConsumptionApprox: "5L",
              detergentUsageStandardApprox: "6g",
              standardLoadCapacityApprox: "16点\n（大皿2点、中皿2点、小皿2点、中鉢2点、小鉢2点、茶わん2点、汁わん2点、コップ2点、小物類（はし、スプーン等） ）",
              maxCompatiblePlateSizeApprox: "直径23cmまで",
              uvSterilizationFunction: "×",
              heatedDryingFunction: "×",
              autoOpenFunction: "×",
              plumbingWork: "不要",
              waterSupplyMethod: "タンク式（手動給水）/分岐水栓式",
              unitDimensions: "幅42×奥行43.5×高さ43.5cm",
              unitWeight: "13kg",
              powerConsumption: "512W／526W",
            },
            {
              code: "PAK00028",
              model: "SS-MU251",
              brand: "siroca",
              description: "UVライトと、水を使わずマスクや哺乳瓶などの除菌ができる「UV除菌専用コース」を追加したタイプ。肌に触れるものが気になる方にオススメ。",
              imgSrc: this.imgDishwasherSSMU251Src,
              color: "ホワイト（W/W）",
              standardMode: "1時間5分", 
              intensiveMode: "1時間12分",
              quickMode: "37分",
              gentleMode: "1時間2分",
              highTempRinse: "57分",
              interiorCleaningCycle: "1時間23分",
              scheduledOperationApprox: "1時間～6時間まで1時間単位",
              childLock: "◯",
              waterConsumptionApprox: "6L",
              detergentUsageStandardApprox: "4g",
              standardLoadCapacityApprox: "16点\n（大皿2点、中皿2点、小皿2点、中鉢2点、小鉢2点、茶わん2点、汁わん2点、コップ2点、小物類（はし、スプーン等） ）",
              maxCompatiblePlateSizeApprox: "直径27cmまで\n※小物かごを外した場合",
              uvSterilizationFunction: "◯",
              heatedDryingFunction: "×",
              autoOpenFunction: "×",
              plumbingWork: "不要",
              waterSupplyMethod: "タンク式（手動給水）/分岐水栓式",
              unitDimensions: "幅42×奥行44×高さ47cm",
              unitWeight: "13.5kg",
              powerConsumption: "726W／740W",
            },
            {
              code: "PAK00029",
              model: "SS-MA251",
              brand: "siroca",
              description: "洗浄後に自動でドアが開く「オートオープン」機能がついたタイプ。洗い終わりの結露が気になる方、自然乾燥させたい方にオススメ。",
              imgSrc: this.imgDishwasherSSMA251Src,
              color: "シルバー（W/S）",
              standardMode: "1時間5分", 
              intensiveMode: "1時間12分",
              quickMode: "37分",
              gentleMode: "1時間2分",
              highTempRinse: "57分",
              interiorCleaningCycle: "1時間",
              scheduledOperationApprox: "1時間～6時間まで1時間単位",
              childLock: "◯",
              waterConsumptionApprox: "6L",
              detergentUsageStandardApprox: "4g",
              standardLoadCapacityApprox: "16点\n（大皿2点、中皿2点、小皿2点、中鉢2点、小鉢2点、茶わん2点、汁わん2点、コップ2点、小物類（はし、スプーン等） ）",
              maxCompatiblePlateSizeApprox: "直径27cmまで\n※小物かごを外した場合",
              uvSterilizationFunction: "×",
              heatedDryingFunction: "×",
              autoOpenFunction: "◯",
              plumbingWork: "不要",
              waterSupplyMethod: "タンク式（手動給水）/分岐水栓式",
              unitDimensions: "幅42×奥行44×高さ47cm",
              unitWeight: "13.5kg",
              powerConsumption: "726W／740W",
            }
          ]
          this.displayPackages = packageData.map((pack) => {
            let activePackage = this.activePackages.find(p => pack.code === p.code);
            return {
              ...activePackage,
              model: `${pack.brand} ${pack.model}`,
              description: pack.description,
              imgSrc: pack.imgSrc,
            }
          });
          this.packageSpecifications = packageData.map((pack) => {
            let activePackage = this.activePackages.find(p => pack.code === p.code);
            return {
              ...pack,
              name: activePackage.name,
            }
          });
        },
      }
    };

    PetiteVue
      .createApp({LandingDishwasher})
      .mount();
  </script>
{% if not req.isUserApp() %}
  {{ commonParts.spBreadCrumb(breadcrumbTitle) }}
{% endif %}
{% endblock %}

{% block scripts %}
  <script src="{{ static.legacy("common/js/jquery-ui.min.js") }}"></script>
  <script src="{{ static.vendor("clipboard/dist/clipboard.min.js") }}"></script>
  <script>
    $(function () {
      const $header = $('header');
      const headerHeight = $header.outerHeight();
      let startPos = 0;
      let maxHeight = window.visualViewport.height;
      let minHeight = window.visualViewport.height;
      let initialHeight = window.visualViewport.height;
      let barHeightDifference = 0;

      const adjustScrollMargin = () => {
        const currentHeight = window.visualViewport.height;
        maxHeight = Math.max(maxHeight, currentHeight);
        minHeight = Math.min(minHeight, currentHeight);
      }

      const adjustBarHeightDifference = () => {
        const currentHeight = window.visualViewport.height;
        barHeightDifference = initialHeight - currentHeight;
        initialHeight = currentHeight;
      }

      window.visualViewport.addEventListener("resize", () => {
        adjustScrollMargin();
        adjustBarHeightDifference();
      });

      $(window).on('load scroll', function () {
        // anchor section margin
        adjustScrollMargin();
        adjustBarHeightDifference();
        // Calculate the height difference caused by the address bar
        const heightDifference = maxHeight - minHeight;
        const value = $(this).scrollTop();
        let stickyNavHeight = $('#fixedNavigator').outerHeight();
        let combinedMargin = headerHeight + stickyNavHeight;
        let userAgents = window.navigator.userAgent;
        let isUserApp = /(UserApp)/.test(userAgents);
        // Dynamically adjust scrollMarginTop based on viewport changes
        $('.scroll-anchor').each(function (i, el) {
          if (value < $('#fixedNavigator')[0]?.getBoundingClientRect().top + window.scrollY || isUserApp) {
            el.style.scrollMarginTop = stickyNavHeight + 'px';
          } else {
            var position = el.getBoundingClientRect().top + window.scrollY;
            let scrollMarginTop = (position < value + headerHeight ? combinedMargin : stickyNavHeight);
            el.style.scrollMarginTop = (barHeightDifference >= 0 ? scrollMarginTop : scrollMarginTop + heightDifference) + 'px';
          }
        });

        // sticky navigator position
        if (value > startPos && value > headerHeight) {
          if( value > $('#fixedNavigator')[0]?.getBoundingClientRect().top + window.scrollY ) {
            $('#stickyNavigator').css({display: 'flex', top: '0px'});
          } else {
            $('#stickyNavigator').css({display: 'none', top: '0px'});
          }
        } else {
          if( value + headerHeight > $('#fixedNavigator')[0]?.getBoundingClientRect().top + window.scrollY ) {
            $('#stickyNavigator').css({display: 'flex', top: headerHeight + 'px'});
          } else {
            $('#stickyNavigator').css({display: 'none', top: headerHeight + 'px'});
          }
        }
        startPos = value;
      });
    });

    // drag scroll by mouse
    $(function () {
      var _window = window;
      var _document = document;
      var mousemove = 'mousemove';
      var mouseup = 'mouseup';
      var mousedown = 'mousedown';
      var EventListener = 'EventListener';
      var addEventListener = 'add'+EventListener;
      var removeEventListener = 'remove'+EventListener;
      var newScrollX, newScrollY;

      var dragged = [];
      var reset = function(i, el) {
        for (i = 0; i < dragged.length;) {
          el = dragged[i++];
          el = el.container || el;
          el[removeEventListener](mousedown, el.md, 0);
          _window[removeEventListener](mouseup, el.mu, 0);
          _window[removeEventListener](mousemove, el.mm, 0);
        }

        // cloning into array since HTMLCollection is updated dynamically
        dragged = [].slice.call(_document.getElementsByClassName('scrollable'));
        for (i = 0; i < dragged.length;) {
          (function(el, lastClientX, lastClientY, pushed, scroller, cont){
            (cont = el.container || el)[addEventListener](
              mousedown,
              cont.md = function(e) {
                if (!el.hasAttribute('nochilddrag') ||
                  _document.elementFromPoint(
                      e.pageX, e.pageY
                  ) == cont
                ) {
                  pushed = 1;
                  lastClientX = e.clientX;
                  lastClientY = e.clientY;

                  e.preventDefault();
                }
              }, 0
            );

            _window[addEventListener](
              mouseup, cont.mu = function() {pushed = 0;}, 0
            );

            _window[addEventListener](
              mousemove,
              cont.mm = function(e) {
                if (pushed) {
                  (scroller = el.scroller||el).scrollLeft -=
                    newScrollX = (- lastClientX + (lastClientX=e.clientX));
                  scroller.scrollTop -=
                    newScrollY = (- lastClientY + (lastClientY=e.clientY));
                  if (el == _document.body) {
                    (scroller = _document.documentElement).scrollLeft -= newScrollX;
                    scroller.scrollTop -= newScrollY;
                  }
                }
              }, 0
            );
          })(dragged[i++]);
        }
      }

      if (_document.readyState == 'complete') {
        reset();
      } else {
        _window[addEventListener]('load', reset, 0);
      }
    });

    new ScrollHint('.scrollable', {
      i18n: {
        scrollable: '横スクロール可能です'
      }
    });

    // クリップボードにコピー
    var clipboard = new Clipboard('#copyLink');
    clipboard.on('success', function (e) {
      $('#copyLink use')
        .attr("xlink:href", "#check")
        .delay(2000)
        .queue(function () {
          $(this)
            .attr("xlink:href", "#content_copy")
            .dequeue();
        });
      trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'リンクをコピー', 'eventLabel': location.href});
    });
    clipboard.on('error', function (e) {
      $("#copyLink").alert('お使いの端末はこの機能に対応していません');
    });

    // ga解析用
    $(function () {
      $('.twitter-back').tap(function (e) {
        trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'twitter', 'eventLabel': location.href});
      });
      $('.facebook-back').tap(function (e) {
        trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'facebook', 'eventLabel': location.href});
      });
      $('.line-back').tap(function (e) {
        trackGAEvent({'eventCategory': 'SNSシェア', 'eventAction': 'line', 'eventLabel': location.href});
      });
    });
  </script>
{% endblock %}