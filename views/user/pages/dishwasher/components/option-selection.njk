{% macro OptionSelection() %}
  <script>
    function OptionSelection() {
      return {
        $template: '#option-selection',
        genArrayOptions(max) {
          return Array.from({
            length: max
          }, (_, i) => i + 1)
        },
        changeProductDeliveryType(e) {
          const selectedType = event
            .target
            .options[event.target.selectedIndex];
          if (event.target.selectedIndex === 0) {
            this.errors.productDeliveryType = '';
          } else {
            this.errors.productDeliveryType = '';
          }
          this.calculateTotalFee();
        }
      }
    };
  </script>
  {% raw %}
    <template id="option-selection">
      <dl class="mar-t_16 mar-b_8">
        <input id="productDeliveryType" class="m_acd-check dis_n" type="checkbox">
        <dt class="m_acd-wrapper mar-b_8 fon-w_700">{{getTitle('productDeliveryType')}}
          <label class="m_acd-title dis_i" for="productDeliveryType">
            <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-r_4 ver-a_t_t" role="img">
              <use xlink:href="#help"></use>
            </svg>
          </label>
        </dt>
        <div class="mar-b_8 pad_8 bor-r_8 bac-c_bla02 m_acd-content dis_n">
          <p class="mar-t_8 mar-b_8 fon-s_14"><span class="fon-w_700">事前配送</span>：設置日の3〜4日前を目安に指定の住所へ製品を配送します。作業日までにお受け取りの上、適切に保管をお願いします。</p>
          <p class="mar-t_8 mar-b_8 fon-s_14"><span class="fon-w_700">当日持込</span>：工事担当が作業当日に製品をお持ちします。製品保管の必要はありません。</p>
        </div>
        <p class="fon-s_14 mar-b_8">※ <span class="fon-w_700">当日持込</span>の対応エリアは、東京都、千葉県、埼玉県、神奈川県、愛知県、大阪府、京都府、兵庫県です。</p>
        <p class="fon-s_14 mar-b_8">※ <span class="fon-w_700">事前配送</span>の場合、食洗機本体と分岐水栓は別々に届きます。</p>
        <dd class="m_clearfix mar-b_8 mar-t_8 cur_p jus-c_c m_selectwrap">
          <select @change="changeProductDeliveryType" v-model="forms.productDeliveryType" class="m_selectbox form-passed" placeholder="-----" :class="!forms.productDeliveryType && forms.productDeliveryType !== 0 && errors.productDeliveryType ? 'bor_1_red10' : ''">
            <option value="">
              -----
            </option>
            <option v-for="opt in optionsData.productDeliveryType?.detail?.listOptions" :data-key="opt.title" :key="opt.title+opt.price" :value="opt.title">{{opt.title}}（+{{opt.price.toLocaleString('ja-JP')}}円）</option>
          </select>
        </dd>
        <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.productDeliveryType && forms.productDeliveryType !== 0 && errors.productDeliveryType">{{ errors.productDeliveryType }}</p>
      </dl>
    </template>
  {% endraw %}
{% endmacro %}