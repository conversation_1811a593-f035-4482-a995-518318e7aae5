{% macro ReservationReview() %}
  <script>
    function ReservationReview() {
      return {
        $template: '#reservation-review',
        isConfirmedPolicy: false,
        notes: [
          "予約リクエスト〜お支払いまで：予約リクエスト完了メールに「キャンセル希望」とご返信いただければ、キャンセルが可能です。", "お支払い後：キャンセル不可となりますのでご注意ください。"
        ],
        submit() {
          for (const key in this.forms) {
            if (this.forms[key] === "") {
              delete this.forms[key];
            }
          }
          const form = document.querySelector('form');
          Object.entries(this.forms).forEach(([key, value]) => {
            const newField = document.createElement('input');
            newField.type = 'hidden';
            newField.name = key;
            let formattedValue = value;
            if (key === 'phoneNumber') {
              formattedValue = value.replace(/-/g, "");
            }
            newField.value = formattedValue;
            form.appendChild(newField);
          });
          return true;
        },
        back() {
          sessionStorage.setItem(this.cache<PERSON>ey, JSON.stringify({timestamp: new Date().getTime(), currentStep: 1, forms: this.forms}));
          history.back();
        }
      }
    };
  </script>
  {% raw %}
    <template id="reservation-review" >
      <div class="bac-c_white pad-t_16 pad-r_16 pad-l_16">
        <div v-scope="ProgressBar()"></div>
        <h1 class="fon-s_20 fon-w_700 mar_0">ご予約内容の確認</h1>
      </div>
      <div class="m_box-shadow_2-2-p12 bac-c_white pad-t_32 pad-r_16 pad-b_48 pad-l_16">
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">お名前</h2>
        <p class="mar-b_56">{{ forms.lastname }}
          {{ forms.firstname }}</p>
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">訪問先</h2>
        <p class="mar-b_56">{{forms.postalcode}}<br>{{ forms.prefecture }}{{ forms.city }}{{ forms.address }}</p>
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">電話番号</h2>
        <p class="mar-b_56">{{forms.phoneNumber}}</p>

        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_8">メールアドレス</h2>
        <p class="mar-b_56">{{forms.email}}</p>

        <h2 class="fon-s_16 fon-w_700 mar-t_16 mar-b_8">キャンセルについて</h2>
        <dd class="fon-s_14 mar-b_56">
          <ul class="fon-s_14 mar-b_56 m_list pad-l_16">
            <li v-for="(note, index) in notes" :key="index">{{note}}</li>
          </ul>
        </dd>

        <label class="fon-s_12 wid_100p confirm-policy">
          <input v-model="isConfirmedPolicy" type="checkbox" class="ver-a_m dis_i-b mar-t_0 mar-r_4 mar-b_0 dis_n">
          <div class="ver-a_m dis_f jus-c_c ali-i_c mar-b_8">
            <svg xmlns="http://www.w3.org/2000/svg" class="fle-s_0" height="32px" viewBox="0 -960 960 960" width="32px" fill="undefined">
              <path :d="isConfirmedPolicy? 'm429.81-352.77 219.27-218.27-34.73-34.73-184.54 183.54-83.39-82.38-34.73 34.73 118.12 117.11Zm-198.36 181.5q-25.05 0-42.61-17.57-17.57-17.56-17.57-42.61v-497.1q0-25.05 17.57-42.61 17.56-17.57 42.61-17.57h497.1q25.05 0 42.61 17.57 17.57 17.56 17.57 42.61v497.1q0 25.05-17.57 42.61-17.56 17.57-42.61 17.57h-497.1Z' : 'M231.45-171.27q-25.05 0-42.61-17.57-17.57-17.56-17.57-42.61v-497.1q0-25.05 17.57-42.61 17.56-17.57 42.61-17.57h497.1q25.05 0 42.61 17.57 17.57 17.56 17.57 42.61v497.1q0 25.05-17.57 42.61-17.56 17.57-42.61 17.57h-497.1Zm.09-47.96h496.92q4.62 0 8.46-3.85 3.85-3.84 3.85-8.46v-496.92q0-4.62-3.85-8.46-3.84-3.85-8.46-3.85H231.54q-4.62 0-8.46 3.85-3.85 3.84-3.85 8.46v496.92q0 4.62 3.85 8.46 3.84 3.85 8.46 3.85Z'"/>
            </svg>
            <div class="tex-a_l"><a class="col_bla10 tex-d_u" target="_blank" rel="noopener noreferrer" href="https://faq.curama.jp/--68528d1f301c5522ed37d144">販売設置規約（ビルトイン食洗機）<svg class="fil_bla10 dis_i-b hei_18 wid_18 ver-a_m mar-l_0" role="img"><use xlink:href="#open_in_new"></use></svg></a>に同意します。</div>
          </div>
        </label>
        <form method="post" action="/built-in-dishwasher-pack/reserve/confirm/" @submit="submit">
          <input type="hidden" name="_csrf" :value="_csrf">
          <button type="submit" @click="isLoading = true" class="m_btn_ora10 fon-s_16 mar-t_16 mar-b_32" :class="isConfirmedPolicy? '': 'btn-disabled'" data-test-id="reservation-confirmation-page-btn" id="id_btn_confirm">予約リクエスト</button>
        </form>
        <div class="tex-a_c pad-b_48">
          <a class="no-alert col_bla10 tex-d_u" href="javascript:void(0);" @click="back">
            <svg class="dis_i-b hei_18 wid_18 fil_bla08 ver-a_m mar-r_8" role="img">
              <use xlink:href="#chevron_left"></use>
            </svg>
            内容を修正する
          </a>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}