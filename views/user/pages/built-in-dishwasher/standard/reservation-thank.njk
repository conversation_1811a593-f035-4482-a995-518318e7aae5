{% extends "layout/user/main_layout.njk" %}
{% import "components/data-layer/_macro.njk" as dataLayer %}
{% import "components/security-macros/_macro.njk" as security %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
{% endblock %}
{% block scripts %}
  <script>
    window.addEventListener('pageshow', (event) => {
      if (event.persisted) {
        location.reload();
      }
    });
  </script>
{% endblock %}
{% block body %}
  {{ security.renderTemplate("currentForm", currentForm | dump) }}
  {{ security.renderTemplate("selectedPackage", selectedPackage | dump) }}
  {{ dataLayerScript() }}

  <div class="pos_r m_box-shadow_2-2-p12 bac-c_white pad_16 mar-b_16">
    <ol class="rsv_flow step dis_f m_list_none ove_h mar-b_32">
      <li>予約</li>
      <li>確認</li>
      <li class="is-current">完了</li>
    </ol>
    <div class="pad-b_16">
      <h1 class="fon-s_20 fon-w_700 pad-b_32 mar_0">予約リクエスト完了</h1>
      <p class="fon-s_14 mar-b_16">ご予約ありがとうございます。</p>
      <p class="fon-s_14 mar-b_32 fon-w_700">打ち合わせに進む前に、既設ビルトイン食洗機の品番の情報提供が必要です。</p>
      <div class="mar-b_32">
        <a href="{{ additionalInformationUrl | safe }}"  {% if not usingApp %} target="_blank" {% endif %} rel="noopener noreferrer" class="m_btn_ora10">既設ビルトイン食洗機の品番を回答する</a>
      </div>
      <p class="fon-s_14">回答フォームのURLは予約受付メールにも記載されています。今すぐ回答ができない場合は、メールをご確認いただき後ほど回答してください。</p>
    </div>
  </div>
  <script>
    const currentForm = {{ security.getTemplateContent("currentForm") }}
    const selectedPackage = {{ security.getTemplateContent("selectedPackage") }}
    window.nk_params = {
      pagetype: 'cv',
      itemid: currentForm?.forms?.packages.map((pack) => pack.id),
      price: currentForm?.forms?.packages.map((pack) => pack.fee),
      quantity: currentForm?.forms?.packages.map((pack) => pack.quantity),
      orderid: "{{reservationCode}}",
      totalprice: currentForm?.forms?.totalFee
    }
    window.ecommerce_data = {
      pagetype: 'conversion',
      items: [{
        item_id: selectedPackage.id,
        item_name: selectedPackage?.code,
        item_brand: 'BuiltInDishwasherPack',
        item_category: 'built-in-dishwasher-pack',
        item_category2: selectedPackage?.name,
        item_list_name: selectedPackage?.name,
        item_list_id: selectedPackage?.name,
        price: selectedPackage.fee,
        currency: 'JPY',
        quantity: 1
      }],
      orderId: "{{reservationCode}}",
      value: currentForm?.forms?.totalFee,
      tax: 0,
      shipping: 0,
    }
    notifyEcommerceEventToApp(window.ecommerce_data);
    sessionStorage.removeItem(`built-in-dishwasher-pack-form_${selectedPackage.categoryCode}`);
  </script>
{% endblock %}
