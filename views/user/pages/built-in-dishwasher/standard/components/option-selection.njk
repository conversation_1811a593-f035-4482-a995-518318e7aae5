{% macro OptionSelection() %}
  <script>
    function OptionSelection() {
      return {
        $template: '#option-selection',

        handleChangeProductOption(field) {
          this.calculateTotalFee();
        },
      }
    };
  </script>
  {% raw %}
    <template id="option-selection">
      <div class="pad-b_32">
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_16">製品と数量</h2>
        <dl class="mar-b_24">
          <div> 
            <p class="mar-b_4"><span>{{ selectedPackage.name }}</span><span class="mar-l_4">1台</span></p> 
          </div> 
        </dl>

        <dt v-if="selectedPackage?.options" class="m_acd-wrapper mar-b_8 fon-w_700">ドアパネル<span class="bac-c_bla02 fon-w_n dis_i-b fon-s_12 pad_4 mar-l_4">任意</span></dt>
        <dl v-if="selectedPackage?.options" class="mar-b_24"> 
          <p class="mar-b_8">「ビューティホワイト」「ベージュ」「ダークグレー」の中から、お好きな色のドアパネルへ交換できます。</p>
          <div class="tex-a_c lin-h_0 mar-b_16"> 
            <img class="wid_100p hei_a bor-r_8" :src="productImageSrc" alt="ドアパネルの画像"> 
          </div> 
          <dd v-if="getProductOptionFields('color')" class="m_clearfixcur_p jus-c_c m_selectwrap mar-b_8"> 
            <select @change="handleChangeProductOption('color')" v-model="forms.color" class="m_selectbox form-passed" placeholder="-----" :class="!forms.color && errors.color ? 'bor_1_red10' : ''"> 
              <option value=""> ----- </option> 
              <option v-for="opt in getProductOptionFields('color').items" :value="opt.code">{{ opt.value }}（{{ formatCurrency(opt.price) }} /台）</option> 
            </select> 
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.color && errors.color">{{ errors.color }}</p>
        </dl>

        <dl class="mar-b_24"> 
          <dt class="m_acd-wrapper mar-b_8 fon-w_700">{{ getTitle('extendedWarranty') }}<span class="bac-c_bla02 fon-w_n dis_i-b fon-s_12 pad_4 mar-l_4">任意</span></dt>
          <p class="fon-s_12 mar-b_8">加入すると取り付けから5年間の保証が付帯されます。詳細は<a class="col_bla10" href="https://faq.curama.jp/--6855181e3c9b2b47e4690e34" target="_blank" rel="noopener noreferrer"><span class="tex-d_u">延長保証について</span><svg class="dis_i-b hei_18 wid_18 fil_bla10 ver-a_b" role="img"><use xlink:href="#open_in_new"></use></svg></a>をご確認ください。予約が確定した時点で、<a class="col_bla10" href="https://www.sompo-swt.com/hosyo_kitei/kitei_saishin/kitei/jyusetsu_kitei.pdf" :target="usingApp ? '_self' : '_blank'" rel="noopener noreferrer"><span class="tex-d_u">SOMPOワランティ住宅設備機器延長保証サービス規定</span><svg class="dis_i-b hei_18 wid_18 fil_bla10 ver-a_b" role="img"><use xlink:href="#open_in_new"></use></svg></a>に同意したものとみなします。</p> 
          <dd class="m_clearfixcur_p jus-c_c m_selectwrap"> 
            <select @change="calculateTotalFee" v-model="forms.extendedWarranty" class="m_selectbox form-passed" placeholder="-----" :class="!forms.extendedWarranty && forms.extendedWarranty !== 0 && errors.extendedWarranty ? 'bor_1_red10' : ''"> 
              <option v-for="opt in optionsData.extendedWarranty?.detail?.listOptions" :data-key="opt.title" :key="opt.title+opt.price" :value="opt.title">{{opt.title}}{{ opt.price ? `（+${opt.price.toLocaleString('ja-JP')}円 / 台）` : ''}}</option>
            </select> 
          </dd>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="!forms.extendedWarranty && errors.extendedWarranty">{{ errors.extendedWarranty }}</p>
        </dl> 
        <dl class="mar-b_16"> 
          <dt class="m_acd-wrapper mar-b_8 fon-w_700">商品配送方法 </dt>
          <p class="fon-s_14">事前配送 </p> 
          <ul class="m_list_none notice fon-s_12 mar-t_8">
            <li>設置日の3〜4日前を目安に指定の住所へ商品を配送します。</li>
            <li>ビルトイン食洗機本体とドアパネルは別々に届きます。</li>
          </ul> 
        </dl> 

        <h2 class="fon-s_16 fon-w_700 mar-t_8 mar-b_16 pad-t_8">料金</h2>
        <dl>
          <dt class="dis_t wid_100p">
            <span class="dis_t-c fon-s_12">合計（税込）</span>
            <span class="dis_t-c tex-a_r fon-s_20 fon-w_700" data-test-id="total-price-label" id="inner-total-price">{{formatCurrency(forms.totalFee)}}</span>
          </dt>
        </dl>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
