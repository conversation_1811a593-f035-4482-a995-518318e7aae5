
{% macro ReservationForm() %}
  <script>
    function ReservationForm() {
      return {
        $template: '#reservation-form',
        getSubmitErrors() {
          return [...Object.values(this.errors)].filter(e => !!e);
        },
        formatPostalCode(postalcode) {
          return postalcode
            .replace(/[０-９]/g, function (s) {
              return String.fromCharCode(s.charCodeAt(0) - 0xfee0);
            })
            .replace(/[^0-9]/g, "");
        },
        validatePostcode() {
          if (!this.forms.postalcode) {
            this.errors.postalcode = '郵便番号を入力してください。';
            return false;
          }
          const formattedPostalCode = this.formatPostalCode(this.forms.postalcode);
          if (formattedPostalCode.length < 7) {
            this.errors.postalcode = 'この値が少なくとも 7 文字以上であることを確認してください。';
            return false;
          }
          const pattern = new RegExp("[0-9]{7}");
          if (!(formattedPostalCode && pattern.test(formattedPostalCode))) {
            this.errors.postalcode = '正しい郵便番号を入力してください';
            return false;
          }
          return true;
        },
        handleChangeEmail() {
          this.forms.email = this
            .forms
            .email
            .replace(/[\u2028\u2029]+/g, "")
            .trim()
            .normalize("NFKC");
          if (!this.forms.email) {
            this.errors.email = 'メールアドレスを入力してください。';
            return;
          }
          if (!this.forms.email.match(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/)) {
            this.errors.email = 'メールアドレスが正しく入力されていません。';
            return;
          }
          if (!this.forms.email.match(/^[a-zA-Z0-9!\x22\#$%&@'()*+,-._¥/ ]+$/)) { // halfWidthAndSpecialCharacters
            this.errors.email = '半角英数で入力してください。';
            return;
          }
          if (this.forms.email.endsWith("@privaterelay.appleid.com")) {
            this.errors.email = 'こちらのメールアドレスは利用できません。別のメールアドレスに変更してください。';
            return;
          }
          const emailSplit = this
            .forms
            .email
            .split("@");
          if (emailSplit.length > 1 && emailSplit[1].toUpperCase().search(/(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}|[A-Z0-9-]{2,})$/) !== 0) { // validateDomain
            this.errors.email = 'メールアドレスが正しく入力されていません。';
            return;
          }
          this.errors.email = '';
        },
        handleChangePhoneNumber() {
          if (!this.forms.phoneNumber) {
            this.errors.phoneNumber = '電話番号を入力してください。';
            return;
          }

          const isNullPhoneNumberPart = this
            .forms
            .phoneNumber
            .split("-")
            .every((part) => !!part);
          if (!isNullPhoneNumberPart) {
            this.errors.phoneNumber = '正しい電話番号を入力してください。';
            return;
          }

          const normalizedPhoneNumber = this.normalizePhoneNumber(this.forms.phoneNumber);
          this.forms.phoneNumber = normalizedPhoneNumber;
          const phoneNumberRemovedBar = normalizedPhoneNumber.replace(/[-]/g, "");
          if (!phoneNumberRemovedBar.match(/^\d+$/) || phoneNumberRemovedBar.length > 12) {
            this.errors.phoneNumber = '正しい電話番号を入力してください。';
            return;
          }

          if (phoneNumberRemovedBar.length < 10) {
            this.errors.phoneNumber = '電話番号の値が少なくとも 10 文字以上であることを確認してください。';
            return;
          }

          this.errors.phoneNumber = '';
        },

        normalizePhoneNumber(text) {
          return text
            .replace(/[‐－―ー−-]/g, "-") // バーを半角に変換
            .replace(/\s+/g, "") // スペース削除
            .replace(/[０-９]/g, (s) => {
              return String.fromCharCode(s.charCodeAt(0) - 0xfee0); // 全角 -> 半角
            });
        },
        async submit() {
          try {
            this.isLoading = true;
            this.validateReservationDate();
            this.validatePostcode();
            this.handleChangePhoneNumber();
            this.handleChangeEmail();
            for (const field of this.requiredFields) {
              if ((!this.forms[field] && this.forms[field] !== 0) || (!!this.errors[field] && ["phoneNumber", "email", "postalcode"].includes(field))) {
                this.errors[field] = this.errors[field] || `${this.defaultFieldKeyMapping[field] || this.defaultFieldKeyMapping.default}を入力してください。`;
              } else if (["lastname", "firstname", "city", "address"].includes(field) && !this.forms[field].trim()) {
                this.errors[field] = `${this.defaultFieldKeyMapping[field] || this.defaultFieldKeyMapping.default}を入力してください。`;
              } else {
                this.errors[field] = "";
              }
            }
            if (Object.values(this.errors).some(e => !!e) || this.forms.packages.some(e => !!e.errorPackageId || !!e.errorQuantity)) {
              this.isLoading = false;
              return;
            }
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), currentStep: 2, forms: this.forms}));
            window.onbeforeunload = function () {}
            const response = await axiosClient.post(`${this.reservationUrl}${initialData.selectedPackage.categoryCode}/`, {
              timestamp: new Date().getTime(),
              currentStep: 2,
              forms: this.forms
            });
            if (response?.data?.error) {
              location.reload();
              return;
            }
            window.location.href = `${this.reservationUrl}${initialData.selectedPackage.categoryCode}/confirm/`;
          } catch (e) {
            location.reload();
          }
        },
        addMorePackage() {
          if (this.forms.packages.length < this.maxPackageSelected) {
            this
              .forms
              .packages
              .push({id: "", quantity: 0, fee: 0, name: ""});
          }
        },
        deletePackage(index) {
          this
            .forms
            .packages
            .splice(index, 1);
          this.calculateTotalFee();
        },
        handleFormChange() {
          setTimeout(() => {
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), currentStep: 1, forms: this.forms}));
          }, 0);
        }
      };
    }
  </script>
  {% raw %}
    <template id="reservation-form">
      <div class="bac-c_white pad-l_16 pad-r_16 pad-t_16 pad-b_32 mar-b_16 m_box-shadow">
        <div v-scope="ProgressBar()"></div>
        <h1 class="fon-s_20 fon-w_700 pad-b_16 mar_0">ご予約情報を入力</h1>
        <p>本サービスには、ご利用中のビルトイン食洗機の品番が必要です。予約リクエスト完了後、専用フォームから品番を送信してください。</p>
      </div>
      <form @submit.prevent="submit" @input="handleFormChange">
        <div class="m_box-shadow_2-2-p12 bac-c_white pad-t_32 pad-r_16 pad-b_32 pad-l_16 mar-b_16">
          <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_16">訪問日</h2>
          <p class="mar-b_32">訪問日は、工事担当との打ち合わせで確定します。ご希望に沿えない場合もありますのでご了承ください。</p>
          <div v-scope="ReserveDatetime(1, blockedDates)" :key="1" class="mar-b_8"></div>
          <div v-scope="ReserveDatetime(2, blockedDates)" :key="2"></div>
          <p class="fon-s_12 col_red10 mar-b_8" v-if="errors.datesError">{{ errors.datesError }}</p>
        </div>
        <div class="m_box-shadow_2-2-p12 bac-c_white pad-t_32 pad-r_16 pad-b_32 pad-l_16 mar-b_16" v-scope="OptionSelection()"></div>
        <div v-scope="ContactInformation()"></div>
        <div class="bac-c_white pad-r_16 pad-b_32 pad-l_16">
          <button type="submit" class="m_btn_ora10 fon-s_16" data-test-id="reservation-confirmation-page-btn" id="id_btn_confirm">確認画面に進む</button>
          <p class="fon-s_14 col_red10 mar-t_4" v-for="error in getSubmitErrors()">{{ error }}</p>
        </div>
      </form>
    </template>
  {% endraw %}
{% endmacro %}
