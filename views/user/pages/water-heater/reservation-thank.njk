{% extends "layout/user/main_layout.njk" %}
{% import "components/data-layer/_macro.njk" as dataLayer %}
{% import "components/security-macros/_macro.njk" as security %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
{% endblock %}
{% block scripts %}
  <script>
    window.addEventListener('pageshow', (event) => {
      if (event.persisted) {
        location.reload();
      }
    });
  </script>
{% endblock %}
{% block body %}
  {{ security.renderTemplate("currentForm", currentForm | dump) }}
  {{ security.renderTemplate("activePackages", activePackages | dump) }}
  {{ dataLayerScript() }}

  <div class="pos_r m_box-shadow_2-2-p12 bac-c_white pad_16 mar-b_16">
    <ol class="rsv_flow step dis_f m_list_none ove_h mar-b_32">
      <li>予約</li>
      <li>確認</li>
      <li class="is-current">完了</li>
    </ol>
    <div class="pad-b_16">
      <h1 class="fon-s_20 fon-w_700 pad-b_32 mar_0">予約リクエスト完了</h1>
      <p class="fon-s_14 mar-b_56">ご予約ありがとうございます。<br>現地調査担当より数日以内にお電話いたします。<br>今しばらくお待ちください。</p>
      <a href="/" class="m_btn_bla_bor">トップページに戻る</a>
    </div>
  </div>
  <script>
    const currentForm = {{ security.getTemplateContent("currentForm") }}
    const activePackages = {{ security.getTemplateContent("activePackages") }}
    window.nk_params = {
      pagetype: 'cv',
      itemid: currentForm?.forms?.packages.map((pack) => pack.id),
      price: currentForm?.forms?.packages.map((pack) => pack.fee),
      quantity: currentForm?.forms?.packages.map((pack) => pack.quantity),
      orderid: "{{reservationCode}}",
      totalprice: currentForm?.forms?.totalFee
    }
    window.ecommerce_data = {
      pagetype: 'conversion',
      items: currentForm?.forms?.packages.map((pack) => {
        const packageData = activePackages.find((p) => p.id === pack.id);
        return {
          item_id: pack.id,
          item_name: packageData?.code,
          item_brand: 'WaterHeaterPack',
          item_category: 'water-heater-pack',
          item_category2: packageData?.name,
          item_list_name: packageData?.name,
          item_list_id: packageData?.name,
          price: pack.fee,
          currency: 'JPY',
          quantity: pack.quantity
        };
      }),
      orderId: "{{reservationCode}}",
      value: currentForm?.forms?.totalFee,
      tax: 0,
      shipping: 0,
    }
    notifyEcommerceEventToApp(window.ecommerce_data);
    sessionStorage.removeItem("water-heater-pack-form");
  </script>
{% endblock %}