{% extends "layout/user/main_layout.njk" %}
{% import "components/enhanced-ec/_macro.njk" as enhancedEC %}
{% import "components/structured-data/_macro.njk" as structuredData %}
{% import "components/review-text/_macro.njk" as reviewText %}
{% from "partials/service/service-share.njk" import getMarketPriceTbody %}
{% import "components/dynamic-links-script/_macro.njk" as dynamicLinksScript %}
{% import "components/security-macros/_macro.njk" as security %}
{% from "user/common/progress-bar.njk" import ProgressBar %}
{% from "user/pages/water-heater/components/reservation-form.njk" import ReservationForm %}
{% from "user/pages/water-heater/components/reservation-review.njk" import ReservationReview %}
{% from "user/common/contact-information.njk" import ContactInformation %}
{% from "user/common/reserve-datetime.njk" import ReserveDatetime %}
{% from "user/common/package-selection.njk" import PackageSelection %}
{% from "shared/lp/data-layer.njk" import dataLayerScript %}
{% set ReEnableButtons_selector = "button:not(#js-submit-button), input" %}
{% set noFooter = true %}

{% block metas %}{% endblock %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
  {% include "../../../assets/dayjs.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>

{% endblock %}
{% block styles %}
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link rel="stylesheet" href="{{ static.legacy("static/sp/css/curama-datepicker-theme.css") }}">
  {% if usingCalendarApp %}
    <link rel="stylesheet" href="{{ static.legacy("static/sp/css/calendar_user.css") }}">
  {% endif %}
  <link href="{{ assets.url("css/user/styles.css") }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
  <script src="{{ static.legacy("common/js/jquery-ui.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/jquery.ui.datepicker-ja.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/common.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block body %}
  {% set combined = {
    user: user,
    reservationConfirmPageUrl: reservationConfirmPageUrl,
    defaultDate: defaultDate,
    blockedDates: blockedDates,
    activePackages: activePackages,
    waterHeaterDiscount: waterHeaterDiscount,
    reservationOptions: reservationOptions,
    userId: userId,
    maxPackageQuantity: maxPackageQuantity,
    randomIdempotencyKey: randomIdempotencyKey,
    errorMessages: errorMessages,
    usingApp: usingApp
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ ProgressBar() }}
  {{ ReservationForm() }}
  {{ ReservationReview() }}
  {{ ContactInformation() }}
  {{ ReserveDatetime() }}
  {{ PackageSelection() }}
  {{ dataLayerScript() }}
  {% raw %}
    <div v-scope="NewReservation()" @vue:mounted="mounted" v-cloak class="pos_r">
      <div v-if="isLoading" class="reservation-loading-wrap">
        <div class="sl-loading">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div v-scope="ReservationForm()"></div>
    </div>
  {% endraw %}
  <div class="pad-t_32 pad-r_16 pad-b_96 pad-l_16">
    <img 
            src="{{ static.staticImage("frontend/loading_SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.gif") }}"
            data-src="{{ static.staticImage("SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.webp") }}" 
            data-not-webp-img="{{ static.staticImage("SGS_ISO-IEC_27001_with_ISMS-AC_TCL_LR.jpg") }}"
            data-loading-lazy="true"
            height="58" width="100" alt="ISMS-AC 認定シンボル"/>
    <p class="tex-a_c pad-t_16 fon-s_12">{{ copyright.getCopyright() }}</p>
  </div>
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    function NewReservation(props) {
      return {
        currentStep: 1,
        isLoading: false,
        cacheKey: "water-heater-pack-form",
        reservationConfirmPageUrl: initialData.reservationConfirmPageUrl,
        maxPackageSelected: Number(initialData.maxPackageQuantity),
        waterHeaterDiscount: Number(initialData.waterHeaterDiscount),
        packageQuantityOptions: Array.from({
          length: 10
        }, (_, i) => {
          return {
            value: i + 1,
            label: `${i + 1}台`
          };
        }),
        defaultDate: initialData.defaultDate,
        blockedDates: initialData.blockedDates,
        activePackages: initialData.activePackages,
        reservationOptions: initialData.reservationOptions,
        errorMessages: initialData.errorMessages,
        optionsData: {},
        calculateTotalFee() {
          const packageFees = this
            .forms
            .packages
            .reduce((acc, cur) => acc + cur.fee * cur.quantity, 0);
          this.forms.totalFee = packageFees;
        },
        forms: {
          idempotencyKey: initialData.randomIdempotencyKey,
          userId: initialData.userId,
          totalFee: 0,
          workingDate1: "",
          workingDate2: "",
          packages: [
            {
              id: "",
              name: "",
              fee: 0,
              quantity: 0
            }
          ],
          lastname: initialData.user
            ?.last_name ?? "",
          firstname: initialData.user
            ?.first_name ?? "",
          postalcode: initialData.user
            ?.address
              ?.postal_code ?? "",
          prefecture: initialData.user
            ?.address
              ?.prefecture ?? "",
          city: initialData.user
            ?.address
              ?.city ?? "",
          address: initialData.user
            ?.address
              ?.address ?? "",
          phoneNumber: initialData.user
            ?.phone_number ?? "",
          email: initialData.user
            ?.mail_address ?? ""
        },
        requiredFields: [
          'lastname',
          'firstname',
          'postalcode',
          'prefecture',
          'city',
          'address',
          'phoneNumber',
          'email'
        ],
        defaultFieldKeyMapping: {
          dates: "全ての希望日時",
          lastname: "お名前（姓）",
          firstname: "お名前（名）",
          postalcode: "郵便番号",
          prefecture: "都道府県",
          city: "市区町村",
          address: "それ以降の住所",
          phoneNumber: "電話番号",
          email: "メールアドレス",
          packages: "製品と数量",
          productDeliveryType: "製品配送方法",
          default: "このフィールド"
        },
        errors: {
          datesError: "",
          packagesError: "",
          productDeliveryType: "",
          lastname: "",
          firstname: "",
          postalcode: "",
          prefecture: "",
          city: "",
          address: "",
          phoneNumber: "",
          email: ""
        },
        formatCurrency: {{formatCurrency | safe}},
        validateRequired(value, field, message) {
          if (!value.trim()) {
            this.errors[field] = message;
            return;
          }
          this.errors[field] = "";
        },
        validateReservationDate() {
          const dateRequire = !this.forms[`workingDate1`] || !this.forms[`workingDate2`];
          const dateConflict = this.forms[`workingDate1`] === this.forms[`workingDate2`];
          this.errors.datesError = dateRequire
            ? `${this.defaultFieldKeyMapping["dates"]}を入力してください。`
            : dateConflict
              ? "訪問日の希望はずらして設定してください。"
              : "";
        },
        validateSelectedPackages() {
          for (const packageSelected of this.forms.packages) {
            const package = this
              .activePackages
              .find(p => p.id === packageSelected.id);
            if (package?.outOfStock) {
              packageSelected.errorPackageStock = `製品は在庫切れです。`;
            } else {
              packageSelected.errorPackageStock = '';
            }
            if (!packageSelected.quantity) {
              packageSelected.errorQuantity = `${this.defaultFieldKeyMapping.packages}を入力してください。`;
            } else {
              packageSelected.errorQuantity = '';
            }
          }
        },
        getTitle(option) {
          switch (option) {
            default:
              return "";
          }
        },
        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
          window.onbeforeunload = function (e) {
            e.returnValue = 'まだ登録が完了していません。\nこのページを離れると、入力した内容は破棄されます。';
          };

          const cachedData = sessionStorage.getItem(this.cacheKey);
          const {timestamp, currentStep, forms} = cachedData
            ? JSON.parse(cachedData)
            : {};
          const cachedForm = forms || {};
          const urlParams = new URLSearchParams(window.location.search);
          const packageCode = urlParams.get('packageCode');
          if (packageCode) {
            const package = this
              .activePackages
              .find(e => e.code === packageCode);
            if (package && !package.outOfStock) {
              this.forms = {
                ...this.forms,
                packages: [
                  {
                    id: package?.id,
                    name: package?.name,
                    fee: package?.fee,
                    quantity: 1
                  }
                ]
              };
            } else {
              this.forms = {
                ...this.forms,
                packages: [
                  {
                    id: "",
                    name: "",
                    fee: 0,
                    quantity: 0
                  }
                ]
              };
            }

            if (package) {
              window.nk_params = {
                pagetype: 'detail',
                itemid: [package.id],
                price: [package.fee],
                quantity: [0],
                orderid: '',
                totalprice: 0
              }
              window.ecommerce_data = {
                pagetype: "detail",
                items: [{
                  item_id: package.id,
                  item_name: package.code,
                  item_brand: 'WaterHeaterPack',
                  item_category: 'Water Heater',
                  item_category2: 'Water Heater Pack',
                  item_list_name: package?.name.split('/')[0],
                  item_list_id: package?.name.split('/')[0],
                  price: package.fee,
                  currency: 'JPY',
                  quantity: 1
                }]
              }
              notifyEcommerceEventToApp(window.ecommerce_data);
            }
            this.calculateTotalFee();
            sessionStorage.setItem(this.cacheKey, JSON.stringify({timestamp: new Date().getTime(), currentStep: 1, forms: this.forms}));
            window
              .history
              .replaceState({}, document.title, window.location.pathname);
            return;
          }
          if (!timestamp || !forms || !currentStep) 
            return
          if (initialData.userId === cachedForm.userId && (new Date().getTime() - timestamp) < 1000 * 60 * 30) {
            if (this.blockedDates.includes(cachedForm.workingDate1)) {
              cachedForm.workingDate1 = "";
            }
            if (this.blockedDates.includes(cachedForm.workingDate2)) {
              cachedForm.workingDate2 = "";
            }
            const activePackageIds = this
              .activePackages
              .map(e => e.id);
            cachedForm.packages = cachedForm
              .packages
              .filter(e => !e.id || activePackageIds.includes(e.id));
            if (cachedForm.packages.length === 0) {
              delete cachedForm.packages;
            }
            this.forms = {
              ...this.forms,
              ...cachedForm
            };
            this.validateSelectedPackages();
            this.calculateTotalFee();
          } else {
            sessionStorage.removeItem(this.cacheKey);
          }

        }
      }
    };

    PetiteVue
      .createApp({NewReservation})
      .mount();
  </script>
{% endblock %}

{% block tracking %}{% endblock %}