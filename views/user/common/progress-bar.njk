{% macro ProgressBar() %}
  <script>
    function ProgressBar() {
      return {$template: '#progress-bar'}
    };
  </script>
  {% raw %}
    <template id="progress-bar">
      <ol class="rsv_flow step dis_f m_list_none ove_h mar-b_32">
        <li :class="{ 'is-current': currentStep === 1 }">予約</li>
        <li :class="{ 'is-current': currentStep === 2 }">確認</li>
        <li :class="{ 'is-current': currentStep === 3 }">完了</li>
      </ol>
    </template>
  {% endraw %}
{% endmacro %}