{% macro PackageSelection() %}
  <script>
    function PackageSelection(index, showPlaceholder = true) {
      return {
        $template: '#package-selection',
        index: index,
        showPlaceholder: showPlaceholder,
        changePackageName(e) {
          const newPackageId = e.target.value;
          const packageInfo = this
            .activePackages
            .find(p => p.id === newPackageId);
          this
            .forms
            .packages[this.index]
            .name = packageInfo
              ?.name;
          this
            .forms
            .packages[this.index]
            .fee = packageInfo
              ?.fee;
          this
            .forms
            .packages[this.index]
            .id = newPackageId;
          this
            .forms
            .packages[this.index]
            .errorPackageId = '';
          this
            .forms
            .packages[this.index]
            .errorPackageStock = '';
          this.calculateTotalFee();
          e.target.blur();
        },
        changePackageQuantity(e) {
          this
            .forms
            .packages[this.index]
            .quantity = Number(e.target.value);
          this
            .forms
            .packages[this.index]
            .errorQuantity = '';
          this.calculateTotalFee();
        }
      }
    };
  </script>
  {% raw %}
    <template id="package-selection">
      <div>
        <dt class="mar-b_4">注文{{ index + 1  }}</dt>
        <div class="m_clearfix mar-b_8">
          <order-question-item class="jus-c_c">
            <preset-question-item>
              <dropdown-type class="select-place package-name">
                <div class="m_selectwrap">
                  <select @change="changePackageName" class="select-place m_selectbox truncated" :value="forms.packages[index]?.id" :class="(!forms.packages[index]?.id && forms.packages[index]?.errorPackageId) || forms.packages[index]?.errorPackageStock ? 'bor_1_red10' : ''">
                    <option value="" disabled>
                      -----
                    </option>
                    <option :title="opt.name + '　（' + formatCurrency(opt.fee) + ' /台）'" v-for="opt in activePackages" :key="opt.id + new Date().getTime()" :value="opt.id" :disabled="forms.packages.some(e => e.id === opt.id) || opt.outOfStock">{{opt.name}}　（{{formatCurrency(opt.fee)}} /台）</option>
                  </select>
                </div>
              </dropdown-type>
              <dropdown-type class="select-num package-num">
                <div class="m_selectwrap">
                  <select @change="changePackageQuantity" class="select-num m_selectbox truncated" placeholder="-----" :value="forms.packages[index]?.quantity" :class="(!forms.packages[index]?.quantity && forms.packages[index]?.errorQuantity) || forms.packages[index]?.errorPackageStock ? 'bor_1_red10' : ''">
                    <option v-if="showPlaceholder" value="0" disabled>
                      -----
                    </option>
                    <option v-for="opt in packageQuantityOptions" :key="opt.value" :value="opt.value">{{opt.label}}</option>
                  </select>
                </div>
              </dropdown-type>
            </preset-question-item>
          </order-question-item>
          <div class="m_clearfix wid_24 flo_r cur_p" v-if="maxPackageSelected > 1">
            <svg class="wid_24 hei_24 fil_bla06 flo_r mar-t_16" role="img" v-if="index > 0" @click="deletePackage(index)">
              <use xlink:href="#close"></use>
            </svg>
          </div>
        </div>
        <p class="fon-s_12 col_red10 mar-b_8" v-if="(!forms.packages[index]?.id && forms.packages[index]?.errorPackageId) || (!forms.packages[index]?.quantity && forms.packages[index]?.errorQuantity) || forms.packages[index]?.errorPackageStock">{{ forms.packages[index]?.errorPackageId || forms.packages[index]?.errorQuantity || forms.packages[index]?.errorPackageStock }}</p>
      </div>
    </template>
  {% endraw %}
{% endmacro %}