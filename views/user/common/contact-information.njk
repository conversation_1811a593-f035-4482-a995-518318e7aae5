{% macro ContactInformation() %}
  <script>
    function ContactInformation(param) {
      return {
        $template: '#contact-information',
        title: param?.title || "訪問先",
        subtitle: param?.subtitle || "※取付工事を行う住所をご入力ください。",
        classExtend: param?.classExtend || "m_box-shadow",
        prefectures: [
          {
            label: "北海道",
            value: "北海道"
          }, {
            label: "青森県",
            value: "青森県"
          }, {
            label: "岩手県",
            value: "岩手県"
          }, {
            label: "宮城県",
            value: "宮城県"
          }, {
            label: "秋田県",
            value: "秋田県"
          }, {
            label: "山形県",
            value: "山形県"
          }, {
            label: "福島県",
            value: "福島県"
          }, {
            label: "茨城県",
            value: "茨城県"
          }, {
            label: "栃木県",
            value: "栃木県"
          }, {
            label: "群馬県",
            value: "群馬県"
          }, {
            label: "埼玉県",
            value: "埼玉県"
          }, {
            label: "千葉県",
            value: "千葉県"
          }, {
            label: "東京都",
            value: "東京都"
          }, {
            label: "神奈川県",
            value: "神奈川県"
          }, {
            label: "新潟県",
            value: "新潟県"
          }, {
            label: "富山県",
            value: "富山県"
          }, {
            label: "石川県",
            value: "石川県"
          }, {
            label: "福井県",
            value: "福井県"
          }, {
            label: "山梨県",
            value: "山梨県"
          }, {
            label: "長野県",
            value: "長野県"
          }, {
            label: "岐阜県",
            value: "岐阜県"
          }, {
            label: "静岡県",
            value: "静岡県"
          }, {
            label: "愛知県",
            value: "愛知県"
          }, {
            label: "三重県",
            value: "三重県"
          }, {
            label: "滋賀県",
            value: "滋賀県"
          }, {
            label: "京都府",
            value: "京都府"
          }, {
            label: "大阪府",
            value: "大阪府"
          }, {
            label: "兵庫県",
            value: "兵庫県"
          }, {
            label: "奈良県",
            value: "奈良県"
          }, {
            label: "和歌山県",
            value: "和歌山県"
          }, {
            label: "鳥取県",
            value: "鳥取県"
          }, {
            label: "島根県",
            value: "島根県"
          }, {
            label: "岡山県",
            value: "岡山県"
          }, {
            label: "広島県",
            value: "広島県"
          }, {
            label: "山口県",
            value: "山口県"
          }, {
            label: "徳島県",
            value: "徳島県"
          }, {
            label: "香川県",
            value: "香川県"
          }, {
            label: "愛媛県",
            value: "愛媛県"
          }, {
            label: "高知県",
            value: "高知県"
          }, {
            label: "福岡県",
            value: "福岡県"
          }, {
            label: "佐賀県",
            value: "佐賀県"
          }, {
            label: "長崎県",
            value: "長崎県"
          }, {
            label: "熊本県",
            value: "熊本県"
          }, {
            label: "大分県",
            value: "大分県"
          }, {
            label: "宮崎県",
            value: "宮崎県"
          }, {
            label: "鹿児島県",
            value: "鹿児島県"
          }, {
            label: "沖縄県",
            value: "沖縄県"
          }
        ],
        autofillLocation() {
          const isValid = this.validatePostcode();
          if (!isValid) {
            return;
          }
          this.errors.postalcode = '';
          const data = {
            postalcode: this.forms.postalcode
          };
          this.getLocationData(data);
        },
        getLocationData(requestData, views, options) {
          const fixedPostalcode = this.formatPostalCode(this.forms.postalcode);
          requestData.postalcode = fixedPostalcode;
          const queryParams = new URLSearchParams(requestData).toString()
          axios({
            type: 'GET',
            url: `/api/postal/area-validation/?${queryParams}`,
            headers: {
              "Content-Type": "application/json"
            }
          })
            .then((res) => {
              if (!res.data.pref) {
                this.resetAddress();
                this.errors.postalcode = res.data.message || '';
                this.errors.prefecture = '';
                this.errors.city = '';
                this.errors.address = '';
                return;
              }
              this.autofill(res.data, fixedPostalcode);
              this.handleSuccessGetLocation(res.data);
            })
            .catch((e) => {
              this.locationErrorHandler(e)
            })
          },

        handleSuccessGetLocation(locationData) {
          $("#id_postal_code_ajax_error").remove();
          this.errors.postalcode = '';
          this.errors.prefecture = '';
          this.errors.city = '';
          this.errors.address = '';

          if (!locationData.supported_area && !locationData.pref) {
            // invalid postalcode
            return $(".search-code").after('<p id="id_postal_code_ajax_error" class="text-attention text-error">' + locationData.message + '</p>');

          } else if (!locationData.supported_area) {
            // out of area
            $(".search-code").after('<p id="id_postal_code_ajax_error" class="text-attention text-error">' + locationData.message + '</p>');

            return $(".no_alert").click(() => {
              $(window).off('beforeunload');
            });
          }

          $('#id_address').focus();
        },

        handleFailureGetLocation(data) {
          $("#id_postal_code_ajax_error").remove();
          if (data && data.status === 0) {
            $(".search-code").after('<p id="id_postal_code_ajax_error" class="text-attention text-error">住所を取得できませんでした。インターネットの接続環境を確認して再度お試しください。</p>');
          }
        },

        isPostalcodeValid(postalcode) {
          return /^\d{7}$/.test(postalcode);
        },

        resetAddress() {
          const normalizedPostalcode = this.formatPostalCode(this.forms.postalcode);
          const locationData = {
            pref: "",
            city: "",
            address: "",
            town: ""
          };
          this.autofill(locationData, normalizedPostalcode);
        },
        autofill(locationData, normalizedPostalcode) {
          this.forms.postalcode = normalizedPostalcode;
          this.forms.prefecture = locationData.pref;
          this.forms.city = locationData.city;
          this.forms.address = locationData.town;
          this.handleFormChange();
        },

        locationErrorHandler(ajaxData) {
          this.resetAddress();
          this.handleFailureGetLocation(ajaxData);
        },

        replaceToHankaku() {
          const formattedPostalCode = this.formatPostalCode(this.forms.postalcode);
          this.forms.postalcode = formattedPostalCode;
        },

        trimString(field) {
          this.forms[field] = this.forms[field].replace(/^[\u0020\u3000]+|[\u0020\u3000]+$/g, '').trim();
        },
      }
    };
  </script>
  {% raw %}
    <template id="contact-information">
      <div :class="'pad-t_32 pad-r_16 pad-b_32 pad-l_16 ' + classExtend">
        <h2 class="fon-s_16 fon-w_700 mar-t_0 mar-b_16">{{ title }}</h2>
        <p v-if="subtitle" class="col_red10 mar-b_16">{{ subtitle }}</p>
        <div class="dis_f jus-c_s-b mar-b_16">
          <dl class="wid_48p">
            <dt class="mar-b_4">姓</dt>
            <dd>
              <input 
                v-model="forms.lastname" 
                v-on:input="validateRequired(forms.lastname, 'lastname', 'お名前（姓）を入力してください。')"
                type="text" 
                name="lastname" 
                class="m_textbox"
                :class="errors.lastname ? 'bor_1_red10' : ''"
                placeholder="山田" 
                @blur="trimString('lastname')"
                maxlength="256"/>
              <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.lastname">{{ errors.lastname }}</p>
            </dd>
          </dl>
          <dl class="wid_48p">
            <dt class="mar-b_4">名</dt>
            <dd>
              <input 
                v-model="forms.firstname" 
                v-on:input="validateRequired(forms.firstname, 'firstname', 'お名前（名）を入力してください。')"
                type="text" 
                name="firstname" 
                class="m_textbox"
                :class="errors.firstname ? 'bor_1_red10' : ''"
                placeholder="花子" 
                @blur="trimString('firstname')"
                maxlength="256"/>
              <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.firstname">{{ errors.firstname }}</p>
            </dd>
          </dl>
        </div>
        <dl>
          <dt class="mar-b_4 m_clearfix">
            郵便番号
            <a
              id="search-code"
              class="flo_r fon-w_n col_bla10 tex-d_u dis_f ali-i_c"
              href="http://www.post.japanpost.jp/zipcode/"
              target="_blank"
            >
            郵便番号を調べる
            <svg class="dis_i-b hei_18 wid_18 fil_bla10 mar-l_4 ver-a_m" role="img">
                <use xlink:href="#open_in_new"></use>
              </svg>
            </a>
          </dt>
          <dd class="mar-b_16">
            <input 
              v-model="forms.postalcode"
              v-on:input="autofillLocation"
              type="tel"
              name="postalcode"
              id="id_postal_code"
              class="m_textbox"
              :class="errors.postalcode ? 'bor_1_red10' : ''"
              placeholder="1070062"
              @blur="replaceToHankaku"
              maxlength="8"/>
            <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.postalcode">{{ errors.postalcode }}</p>
          </dd>

          <dt class="mar-b_4">都道府県</dt>
          <dd class="mar-b_16">
            <div class="m_selectwrap">
              <select
                v-model="forms.prefecture" 
                @change="errors.prefecture = ''"
                name="prefecture"
                class="m_selectbox"
                :style="{'color':forms.prefecture ? '#222' : 'gray'}"
                :class="!forms.prefecture && errors.prefecture ? 'bor_1_red10' : ''"
                id="id_pref">
                <option value="" disabled>選択してください</option>
                <option
                  v-for="elm in prefectures"
                  v-bind:value="elm.value"
                  :key="elm.value"
                >
                  {{ elm.label }}
                </option>
              </select>
            </div>
            <p class="fon-s_12 col_red10 mar-t_4" v-if="!forms.prefecture && errors.prefecture">{{ errors.prefecture }}</p>
          </dd>

          <dt class="mar-b_4">市区町村</dt>
          <dd class="mar-b_16">
            <input
              v-model="forms.city"
              v-on:input="validateRequired(forms.city, 'city', '市区町村を入力してください。')"
              name="city"
              type="text"
              placeholder="港区"
              class="m_textbox"
              :class="errors.city ? 'bor_1_red10' : ''"
              id="id_city"
              @blur="trimString('city')"
              maxlength="32"/>
            <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.city">{{ errors.city }}</p>
          </dd>

          <dt class="mar-b_4">それ以降の住所・マンション名・部屋番号</dt>
          <dd class="mar-b_16">
            <input
              v-model="forms.address"
              v-on:input="validateRequired(forms.address, 'address', 'それ以降の住所を入力してください。')"
              name="address"
              type="text"
              placeholder="南青山2丁目5-17ポーラ青山ビルディング7階"
              id="id_address"
              class="m_textbox"
              @blur="trimString('address')"
              :class="errors.address ? 'bor_1_red10' : ''"
              maxlength="128"/>
            <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.address">{{ errors.address }}</p>
          </dd>
          <dt class="mar-b_4">電話番号</dt>
          <dd class="mar-b_16">
            <input
              v-model="forms.phoneNumber"
              v-on:input="handleChangePhoneNumber"
              name="phoneNumber"
              type="tel"
              id="id_phone"
              placeholder="0300000000"
              class="m_textbox"
              :class="errors.phoneNumber ? 'bor_1_red10' : ''"
              maxlength="14"/>
            <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.phoneNumber">{{ errors.phoneNumber }}</p>
          </dd>
          <dt class="mar-b_4">メールアドレス</dt>
          <dd class="mar-b_0">
            <input
              v-model="forms.email"
              v-on:input="handleChangeEmail"
              autocapitalize="off"
              name="email"
              type="email"
              id="id_email"
              maxlength="256"
              placeholder="メールアドレス"
              class="m_textbox"
              :class="errors.email ? 'bor_1_red10' : ''"/>
            <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.email">{{ errors.email }}</p>
          </dd>
        </dl>
      </div>
    </template>
  {% endraw %}
{% endmacro %}