{% macro ReserveDatetime() %}
  <script>
    function ReserveDatetime(index, disabledDates) {
      return {
        $template: '#reserve-datetime',
        isLoadingReservationDatetime: false,
        index: index,
        maxDate: 60,
        selectedDate: "",
        disabledDates: disabledDates,
        onFocusedDateTimeField(e) {
          $(`#inline_datepicker${this.index}`).datepicker({
            autoSize: true,
            minDate: 0,
            maxDate: this.maxDate,
            dateFormat: 'yy年m月d日',
            defaultDate: this.forms[`workingDate${this.index}`]
              ? dateUtils.formatDateFilter(this.forms['workingDate' + index])
              : this.defaultDate
                ? dateUtils.formatDateFilter(this.defaultDate)
                : null,
            beforeShowDay: (date) => {
              const currentDate = jQuery
                .datepicker
                .formatDate('yy-mm-dd', date);
              return [
                this
                  .disabledDates
                  .indexOf(currentDate) === -1
              ];
            },
            onSelect: (dateText, inst) => {
              $(`#id_working_date${this.index}`).val(dateText);
              const dateFormated = dateText
                .replace('年', '-')
                .replace('月', '-')
                .replace('日', '');
              this.forms[`workingDate${this.index}`] = dayjs(dateFormated).format('YYYY-MM-DD');
              $(`#reservationModal${this.index}`).modal("hide");
              if (this.forms.workingDate1 && this.forms.workingDate2 && this.forms.workingDate1 === this.forms.workingDate2) {
                this.errors.datesError = "訪問日の希望はずらして設定してください。";
              } else if (this.forms.workingDate1 && this.forms.workingDate2) {
                this.errors.datesError = "";
              }
              this.handleFormChange();
            }
          })
        }
      }
    };
  </script>
  {% raw %}
    <template id="reserve-datetime">
      <dl>
        <div class="pos_r">
          <dt class="mar-b_4">第{{ index }}希望</dt>
          <dd>
            <a href="javascript:void(0);" data-toggle="modal" :data-target="'#reservationModal'+ index">
              <input
                    type="text"
                    size="35"
                    placeholder="----年--月--日"
                    :id="'id_working_date' + index"
                    :value="dateUtils.formatDateFilter(forms['workingDate' + index])"
                    class="m_textbox fon-s_16 mar-b_8 datepicker"
                    :class="(!forms['workingDate' + index] || (index === 2 && forms['workingDate1'])) && errors.datesError ? 'bor_1_red10' : ''"
                    readonly="true"
                    @focus="onFocusedDateTimeField">
            </a>
          </dd>

          <div class="modal modal-calendar" :id="'reservationModal' + index" tabindex="-1" role="dialog">
            <div class="hei_464 mar_0 top_a pos_f bot_0 wid_100p bac-c_white bor-r_top10 animation-sheet pc_fullsize">
              <div class="min-h_56">
                <a type="button" class="pos_a top_16 rig_16" data-dismiss="modal">
                  <svg class="wid_24 hei_24 fil_bla06" role="img">
                    <use xlink:href="#close"></use>
                  </svg>
                </a>
              </div>
              <div class="hei_a pad_16" :id="'inline_datepicker' + index"></div>
            </div>
          </div>
        </div>
      </dl>
    </template>
  {% endraw %}
{% endmacro %}