{% macro UploadAdditionalFiles() %}
  <script>
    function UploadAdditionalFiles(index) {
      return {
        $template: '#upload-additional-files',
        index: index,
        addFileIcon: "{{ assets.url('image/common/add-file-icon.svg') }}",

        imageCompressor(file) {
          return new Promise((resolve, reject) => {
            new Compressor(file, {
              quality: 0.65,
              maxWidth: 3840,
              maxHeight: 3840,
              convertSize: 0,
              success(blob) {
                resolve(blob);
              },
              error() {
                reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
              }
            });
          });
        },

        async handleChangeAdditionalFile(event) {
          if (!event.target.files[0]) {
            return;
          }
          const field = event.target.name.split('-')[0];
          const index = Number(event.target.name.split('-')[1]);
          let file = event.target.files[0];
          const SIZE_5MB = 5 * 1000 * 1000;
          const SIZE_25MB = 25 * 1000 * 1000;

          if (!new RegExp(/^(image\/(png|jpe?g))$/).exec(file.type)) {
            this.errors.additionalAttachments[index] = 'jpgまたはpng以外のファイルが含まれているため、登録できませんでした。';
            return;
          }
          if (file.size > SIZE_5MB && file.type === "application/pdf") {
            this.errors.additionalAttachments[index] = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。5MB以下のPDFファイルを選択してください。";
            return;
          }
          if (file.type !== "application/pdf") {
            if (file.size > SIZE_25MB) {
              this.errors.additionalAttachments[index] = "サイズが大きすぎる画像が含まれているため、登録できませんでした。25MB以下の画像を選択してください。";
              return;
            }
            if (file.size > SIZE_5MB) {
              const compressedFile = await this.imageCompressor(file);
              if (compressedFile.size > SIZE_5MB) {
                this.errors.additionalAttachments[index] = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                return;
              }
              file = compressedFile;
            }
          }
          const reader = new FileReader();
          this.additionalAttachments[index][field] = {
            name: file.name,
            file: file,
            url: URL.createObjectURL(file),
            type: file.type === 'application/pdf' ? 2 : 1,
          };
          this.errors.additionalAttachments[index] = "";
        },

        removeAdditionalFile(index, field) {
          this.additionalAttachments[index][field] = {
            name: "",
            file: null,
            url: "",
            type: 1,
          };
          this.errors.additionalAttachments[index] = "";
          document.getElementById(field + '-' + index).value = "";
        },
      }
    };
  </script>
  {% raw %}
    <template id="upload-additional-files">
      <div class="dis_f jus-c_s-b ali-i_c mar-b_16">
        <p class="fon-s_16 fon-w_700">回収するエアコン（{{index+1}}台目）</p>
        <svg v-if="index !== 0" class="wid_24 hei_24 fil_bla06 flo_r cur_p" role="img" @click="removeSlot(index)">
          <use xlink:href="#close"></use>
        </svg>
      </div>
      <div id="attachment-container" class="dis_f wid_100p ali-i_c mar-b_16">
        <div id="attachment-input-wrap" class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0 wid_150 hei_150">
          <div v-if="!additionalAttachments[index]?.nameplate?.file" class="square wid_100p">
            <label :for="'nameplate-' + index" class="pos_a top_0 dis_f jus-c_c ali-i_c wid_100p hei_100p cur_p">
              <div class="tex-a_c"><img class="fil_bla08" :src="addFileIcon"><p class="fon-s12 col_bla08">上限5MB<br>jpg/png</p></div>
              <input type="file" class="wid_0 hei_0 opa_0" @change="handleChangeAdditionalFile" :id="'nameplate-' + index" :name="'nameplate-' + index" accept="image/png, image/jpeg, image/jpg">
            </label>
          </div>
          <div v-else class="square wid_100p">
            <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="additionalAttachments[index]?.nameplate?.type === 2 ? pdfIcon : additionalAttachments[index]?.nameplate?.url">
            <button type="button" @click="removeAdditionalFile(index, 'nameplate')" class="m_btn-r pos_a rig_0 top_0 cur_p pad_0 lin-h_0 bac-c_04 bor-t-r-r_4" aria-label="閉じる">
              <svg class="wid_24 hei_24 fil_white" role="img"><use xlink:href="#close"></use></svg>
            </button>
          </div>
        </div>
        <p>①銘板の写真</p>
      </div>
      <div id="attachment-container" class="dis_f wid_100p ali-i_c mar-b_16">
        <div id="attachment-input-wrap" class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0 wid_150 hei_150">
          <div v-if="!additionalAttachments[index]?.indoorUnit?.file" class="square wid_100p">
            <label :for="'indoorUnit-' + index" class="pos_a top_0 dis_f jus-c_c ali-i_c wid_100p hei_100p cur_p">
              <div class="tex-a_c"><img class="fil_bla08" :src="addFileIcon"><p class="fon-s12 col_bla08">上限5MB<br>jpg/png</p></div>
              <input type="file" class="wid_0 hei_0 opa_0" @change="handleChangeAdditionalFile" :id="'indoorUnit-' + index" :name="'indoorUnit-' + index" accept="image/png, image/jpeg, image/jpg">
            </label>
          </div>
          <div v-else class="square wid_100p">
            <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="additionalAttachments[index]?.indoorUnit?.type === 2 ? pdfIcon : additionalAttachments[index]?.indoorUnit?.url">
            <button type="button" @click="removeAdditionalFile(index, 'indoorUnit')" class="m_btn-r pos_a rig_0 top_0 cur_p pad_0 lin-h_0 bac-c_04 bor-t-r-r_4" aria-label="閉じる">
              <svg class="wid_24 hei_24 fil_white" role="img"><use xlink:href="#close"></use></svg>
            </button>
          </div>
        </div>
        <p>②室内機の写真</p>
      </div>
      <div id="attachment-container" class="dis_f wid_100p ali-i_c">
        <div id="attachment-input-wrap" class="bor_1_dot_bla06 bor-r_4 mar-r_16 m_3n-mar-r-0 fle-s_0 wid_150 hei_150">
          <div v-if="!additionalAttachments[index]?.otherFile?.file" class="square wid_100p">
            <label :for="'otherFile-' + index" class="pos_a top_0 dis_f jus-c_c ali-i_c wid_100p hei_100p cur_p">
              <div class="tex-a_c"><img class="fil_bla08" :src="addFileIcon"><p class="fon-s12 col_bla08">上限5MB<br>jpg/png</p></div>
              <input type="file" class="wid_0 hei_0 opa_0" @change="handleChangeAdditionalFile" :id="'otherFile-' + index" :name="'otherFile-' + index" accept="image/png, image/jpeg, image/jpg">
            </label>
          </div>
          <div v-else class="square wid_100p">
            <img alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="additionalAttachments[index]?.otherFile?.type === 2 ? pdfIcon : additionalAttachments[index]?.otherFile?.url">
            <button type="button" @click="removeAdditionalFile(index, 'otherFile')" class="m_btn-r pos_a rig_0 top_0 cur_p pad_0 lin-h_0 bac-c_04 bor-t-r-r_4" aria-label="閉じる">
              <svg class="wid_24 hei_24 fil_white" role="img"><use xlink:href="#close"></use></svg>
            </button>
          </div>
        </div>
        <p>③その他の製造年がわかる写真</p>
      </div>
      <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.additionalAttachments[index]">{{ errors.additionalAttachments[index] }}</p>
    </template>
  {% endraw %}
{% endmacro %}
