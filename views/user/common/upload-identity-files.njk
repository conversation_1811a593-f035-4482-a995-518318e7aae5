{% macro UploadIdentityFiles() %}
  <script>
    function UploadIdentityFiles(maxIdentityFiles) {
      return {
        $template: '#upload-identity-files',
        maxIdentityFiles: maxIdentityFiles,
        addFileIcon: "{{ assets.url('image/common/add-file-icon.svg') }}",

        imageCompressor(file) {
          return new Promise((resolve, reject) => {
            new Compressor(file, {
              quality: 0.65,
              maxWidth: 3840,
              maxHeight: 3840,
              convertSize: 0,
              success(blob) {
                resolve(blob);
              },
              error() {
                reject("画像の圧縮に失敗しました。\n再度、画像の選択をお願いします。");
              }
            });
          });
        },

        async handleChangeFile(event) {
          if (!event.target.files[0]) {
            return;
          }
          const SIZE_5MB = 5 * 1000 * 1000;
          const SIZE_25MB = 25 * 1000 * 1000;

          let listFile = Object.values(event.target.files);
          if (listFile.some(file => !new RegExp(/^(image\/(png|jpe?g))$/).exec(file.type))) {
            this.errors.inputFiles = 'jpgまたはpng以外のファイルが含まれているため、登録できませんでした。';
            return;
          }
          if (listFile.length + this.inputFiles.length > this.maxIdentityFiles) {
            this.errors.inputFiles = `添付ファイルの上限数は${this.maxIdentityFiles}ファイルです。（jpgまたはpng）`;
            return;
          }
          for (const [index, file] of listFile.entries()) {
            if (file.size > SIZE_5MB && file.type === "application/pdf") {
              this.errors.inputFiles = "サイズが大きすぎるPDFファイルが含まれているため、登録できませんでした。5MB以下のPDFファイルを選択してください。";
              return;
            }
            if (file.type !== "application/pdf") {
              if (file.size > SIZE_25MB) {
                this.errors.inputFiles = "サイズが大きすぎる画像が含まれているため、登録できませんでした。25MB以下の画像を選択してください。";
                return;
              }
              if (file.size > SIZE_5MB) {
                const compressedFile = await this.imageCompressor(file);
                if (compressedFile.size > SIZE_5MB) {
                  this.errors.inputFiles = "圧縮後の画像サイズが大きすぎる画像が含まれているため、登録できませんでした。";
                  return;
                }
                listFile[index] = compressedFile;
              }
            }
          }
          const reader = new FileReader();
          this.inputFiles.push(...listFile.map(e => ({
            name: e.name,
            file: e,
            url: URL.createObjectURL(e),
            type: e.type === 'application/pdf' ? 2 : 1,
          })));
          this.errors.inputFiles = '';
          document.getElementById("identity-attachments").value = "";
        },

        removeFile(index) {
          this.errors.inputFiles = '';
          this.inputFiles.splice(index, 1);
          document.getElementById("identity-attachments").value = "";
        },
      }
    };
  </script>
  {% raw %}
    <template id="upload-identity-files">
      <dl class="pad-t_32">
        <dt class="mar-b_8 fon-w_700">本人確認書類アップロード</dt>
        <div id="attachment-container" class="dis_f wid_100p">
          <div v-for="(_, index) in maxIdentityFiles" :key="'identity-attachment-' + index" class="bor_1_dot_bla06 bor-r_4 wid_100p box-siz_c square mar-r_16" :class="inputFiles.length > index ? '' : 'dis_n'">
            <img v-if="inputFiles[index]?.url" alt="選択した画像のプレビュー" class="pos_a top_0 wid_100p hei_100p bor-r_4 obj-f_cov" :src="inputFiles[index]?.type === 2 ? pdfIcon : inputFiles[index]?.url">
            <button v-if="inputFiles[index]?.url" type="button" @click="removeFile(index)" class="m_btn-r pos_a rig_0 top_0 cur_p pad_0 lin-h_0 bac-c_04 bor-t-r-r_4" aria-label="閉じる">
              <svg class="wid_24 hei_24 fil_white" role="img"><use xlink:href="#close"></use></svg>
            </button>
          </div>
          <div v-if="inputFiles.length < maxIdentityFiles" id="attachment-input-wrap" class="wid_100p bor_1_dot_bla06 bor-r_4 mar-r_16">
            <div class="square wid_100p">
              <label for="identity-attachments" class="pos_a top_0 dis_f jus-c_c ali-i_c wid_100p hei_100p cur_p">
                <div class="tex-a_c"><img class="fil_bla08" :src="addFileIcon"><p class="fon-s12 col_bla08">上限5MB<br>jpg/png</p></div>
                <input type="file" @change="handleChangeFile" id="identity-attachments" name="identity-attachments" class="wid_0 hei_0 opa_0" accept="image/png, image/jpeg, image/jpg" multiple/>
              </label>
            </div>
          </div>
          <div v-for="(_, index) in (maxIdentityFiles - inputFiles.length - 1)" :key="'blank-' + index" class="wid_100p mar-r_16 bor_1_tra"></div>
        </div>
        <p class="fon-s_12 col_red10 mar-t_4" v-if="errors.inputFiles">{{ errors.inputFiles }}</p>
      </dl>
    </template>
  {% endraw %}
{% endmacro %}
