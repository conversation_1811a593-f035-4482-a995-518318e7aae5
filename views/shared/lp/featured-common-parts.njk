{% macro pcHeader() %}
<svg display="none" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <symbol id="logo" viewBox="0 0 200 30">
            <g>
                <path class="st0" d="M154.11,23.93c-0.44-0.7-1.1-1.58-1.43-2.05c-0.31-0.44-0.47-1.52,0.6-2.03c1.2-0.57,4.06-2.1,5.14-4.16
                        c0.58-1.1,0.89-2.28,1.05-4.1h-3.93c-1.29,2.01-2,2.97-3.01,3.8c-0.47,0.39-1.21,0.39-1.66,0.02c-0.39-0.32-1.47-1.2-1.78-1.41
                        c-0.66-0.45-0.78-1.45-0.22-2.04c0.95-0.99,2.7-2.88,3.83-5.36c0.62-1.33,0.83-2.05,0.89-2.37c0.12-0.62,0.79-1.21,1.61-1.04
                        l2.28,0.47c0.66,0.14,1.25,0.89,0.97,1.77c0,0-0.13,0.31-0.18,0.45c-0.21,0.53-0.26,0.67-0.47,1.13h7.97
                        c1.69,0,2.54-0.07,2.66-0.07c0.91,0,1.4,0.73,1.4,1.32v2.08c0,0.63-0.58,1.34-1.36,1.32c-0.09,0-1.06-0.07-2.73-0.07h-1.21
                        c-0.27,2.87-0.62,4.39-1.4,6.1c-0.92,1.96-2.31,3.6-4.24,5.01c-1.16,0.83-2.22,1.34-3,1.71C155.08,24.78,154.4,24.38,154.11,23.93z
                        " />
                <path class="st0" d="M171.12,17.54c-0.44-1.62-0.64-2.01-0.94-2.78c-0.25-0.64,0.14-1.21,0.63-1.37l1.17-0.32
                        c0.56-0.13,0.98,0.15,1.11,0.45c0.46,1.08,0.62,1.51,1.2,3.1c0.15,0.41-0.09,1.08-0.6,1.23l-1.28,0.38
                        C171.8,18.42,171.25,18.05,171.12,17.54z" />
                <path class="st0" d="M175.17,23.92c-0.4-0.5-0.8-1.08-0.9-1.21c-0.32-0.42-0.28-1.26,0.4-1.53c1.43-0.58,3.97-2.41,4.93-4.98
                        c0.4-1.08,0.6-2.04,0.63-2.57c0.03-0.42,0.48-1.04,1.25-0.88l1.25,0.3c0.4,0.07,0.87,0.55,0.75,1.16
                        c-0.46,2.35-1.22,4.39-2.15,5.78c-0.89,1.32-2.06,2.45-3.49,3.37c-0.59,0.37-0.69,0.44-1.42,0.81
                        C175.99,24.37,175.47,24.29,175.17,23.92z" />
                <path class="st0" d="M175.36,16.8c-0.31-0.98-0.59-1.72-1.01-2.77c-0.19-0.48,0.04-1.18,0.65-1.34l1.11-0.3
                        c0.5-0.12,0.99,0.07,1.16,0.46c0.49,1.11,0.68,1.61,1.19,2.93c0.13,0.34-0.05,1.04-0.59,1.24l-1.25,0.39
                        C176.04,17.59,175.49,17.23,175.36,16.8z" />
                <path class="st0" d="M188.51,25.1c-1.08-0.04-1.43-0.94-1.39-1.36c0.03-0.37,0.06-0.83,0.06-2.59V7.84c0-0.78-0.06-1.25-0.06-2.43
                        c0-0.75,0.67-1.34,1.36-1.33l2.44,0c0.83,0,1.38,0.76,1.34,1.42c-0.05,0.7-0.04,1.15-0.04,2.37v2.81c3.48,1.16,7.07,3.31,7.07,3.31
                        c0.74,0.38,0.83,1.33,0.58,1.77l-1.3,2.35c-0.31,0.62-1.25,0.94-1.93,0.46c-0.24-0.16-0.61-0.46-0.74-0.56
                        c-1.05-0.78-1.68-1.25-3.27-1.92c-0.16-0.07-0.3-0.12-0.4-0.16v5.24c0,1.51,0.05,2.41,0.06,2.55c0.03,0.55-0.41,1.4-1.36,1.4
                        H188.51z" />
                <path class="st0" d="M44.33,24.43c-0.58-0.58-0.76-0.79-1.68-1.74c-1.83-1.88-2.65-2.41-6.56-5.45c-1.18-0.92-1.61-1.36-1.93-1.98
                        c-0.24-0.51-0.39-1.14-0.39-1.68c0-0.88,0.32-1.72,0.93-2.43c0.35-0.38,0.7-0.68,2.1-1.74c2.85-2.14,4.27-3.33,5.84-4.9
                        c0.22-0.22,0.24-0.3,0.66-0.7c0.22-0.21,0.9-0.67,1.7-0.18l2.03,1.44c0.77,0.5,0.71,1.63,0.12,2.14c-0.36,0.31-0.42,0.37-1.81,1.61
                        l-0.07,0.06c-1.43,1.26-3.29,2.73-5.51,4.37c-0.13,0.09-0.44,0.33-0.54,0.44c0.17,0.17,0.79,0.62,1.91,1.45
                        c2.33,1.69,4.23,3.21,6.82,5.48c0,0,0.03,0.04,0.08,0.08c0.34,0.32,0.59,1.17,0.03,1.82l-1.81,1.85
                        C45.73,24.91,44.82,24.93,44.33,24.43z" />
                <g>
                    <path class="st0" d="M64.41,10.13c-4.14-0.9-7.38-2.08-8.04-2.4c-0.97-0.49-0.8-1.56-0.62-1.86l0.93-1.66
                            c0.25-0.42,0.88-0.92,1.74-0.56c0.89,0.38,1.64,0.69,3.71,1.23c1.43,0.38,2.36,0.57,3.47,0.77c1.02,0.19,1.19,1.27,1.02,1.7
                            l-0.64,1.98C65.63,10.08,64.93,10.25,64.41,10.13z" />
                    <path class="st0" d="M61,24.38c-1.92,0-3.76-0.27-5.05-0.54c-0.7-0.15-0.99-0.84-1.03-1.17c-0.07-0.52-0.15-1.29-0.2-1.86
                            c-0.09-0.93,0.67-1.82,1.7-1.55c0.61,0.16,2.76,0.63,4.43,0.63c2.68,0,4.48-0.88,4.48-2.2c0-1.3-1.91-1.44-2.74-1.44
                            c-1.63,0-3.23-0.02-4.69,0.85c-0.37,0.22-0.85,0.66-1.14,0.99c-0.08,0.09-0.2,0.14-0.33,0.14c-0.01,0-3.73-0.16-3.73-0.16
                            c-0.13-0.01-0.25-0.07-0.33-0.17c-0.08-0.1-0.11-0.24-0.08-0.36c0.04-0.16,0.04-0.19,0.05-0.34l0.01-0.05
                            c0.06-0.55,0.21-1.09,0.4-2.23c0.18-1.11,0.48-2.49,0.64-4.03c0.03-0.25,0.05-0.44,0.07-0.56c0.04-0.32,0.34-1.23,1.41-1.19
                            c0.01,0,1.93,0.12,1.93,0.12c0.99,0.07,1.37,1.08,1.19,1.73c-0.2,0.7-0.33,1.16-0.51,2.22c1.72-0.92,3.55-1.34,5.83-1.34
                            c4.22,0,7.06,2.29,7.06,5.7c0,2.07-0.99,3.93-2.72,5.11C65.95,23.84,63.84,24.38,61,24.38z" />
                </g>
                <path class="st0"
                    d="M81.58,24.57c-2.4,0-4.2-0.6-5.49-1.82c-1.38-1.33-1.84-2.87-1.84-6.11c0-0.59,0-0.59,0.11-5.09l0.02-0.8
                        c0.04-1.98,0.08-3.68,0.07-4.45c0-0.23-0.02-0.53-0.02-0.71c-0.01-0.73,0.5-1.41,1.38-1.41c0,0,2.37,0.04,2.37,0.04
                        c1.01,0.01,1.36,0.95,1.31,1.5c-0.11,1.38-0.41,6.27-0.41,10.07c0,2.26,0.1,2.8,0.4,3.28c0.34,0.54,1.18,0.87,2.24,0.87
                        c2.12,0,3.74-1.24,4.55-3.49c0.42-1.13,0.55-1.8,0.67-2.56c0.15-0.93,1.25-1.39,2.06-0.89c0.87,0.54,1.34,0.83,1.93,1.2
                        c0.77,0.49,0.83,1.11,0.7,1.58c-0.78,2.89-1.42,4.33-2.72,5.76C87.08,23.55,84.61,24.57,81.58,24.57z" />
                <path class="st0" d="M120.45,23.32c-0.87-1.33-1.99-2.94-3.8-4.99c-1.8-2.07-2.73-2.87-3.71-3.78c-0.6-0.56-0.55-1.67,0.11-2.09
                        l1.68-1.18c0.17-0.15,0.98-0.44,1.6,0.08c1.77,1.48,2.05,1.76,3.18,2.94c1.17-1.01,2.44-2.31,3.18-3.27c0.35-0.44,0.74-1,1.04-1.49
                        l-9.7,0.04c-1.92,0.01-2.21,0.03-2.77,0.05c-0.57,0.01-1.39-0.43-1.39-1.38V6.24c0-0.92,0.8-1.41,1.45-1.37
                        c0.64,0.04,0.99,0.09,2.77,0.09l12.54-0.02c0.67-0.01,0.86-0.06,0.98-0.08c0.7-0.11,1.06,0.17,1.3,0.39l1.35,1.53
                        c0.34,0.3,0.41,1.07,0.2,1.39c-0.09,0.14-0.2,0.35-0.71,1.15c-2.41,3.76-4.15,6.13-7.01,8.59c0.81,0.97,0.92,1.03,1.92,2.43
                        c0.28,0.4,0.56,1.09-0.21,1.86l-1.96,1.53C121.8,24.24,120.96,24.1,120.45,23.32z" />
                <path class="st0" d="M143.75,18.53c-0.66,0-1.28-0.06-3.81-0.06h-3.86c-2.54,0-2.91,0.02-3.78,0.07c-0.6,0.03-1.37-0.47-1.38-1.32
                        v-2.46c0-0.8,0.66-1.42,1.48-1.36c0.39,0.03,1.21,0.1,3.66,0.1h3.9c2.32,0,3.16-0.1,3.74-0.1c1.04,0,1.41,0.92,1.4,1.27v2.56
                        C145.1,17.52,144.78,18.52,143.75,18.53z" />
                <path class="st0" d="M102.78,23.42c-0.13-0.26-0.18-0.38-0.21-0.46c-0.21-0.63-0.13-1.64,0.81-1.95c1.08-0.36,1.9-1.78,1.9-3.23
                        c0-1.13-0.4-2.02-1.14-2.59c-0.23-0.18-0.43-0.29-0.7-0.36c-0.33,2.35-0.82,4.17-1.59,5.84c-0.9,1.98-2.01,2.91-3.49,2.91
                        c-2.18,0-3.7-2.02-3.7-4.92c0-1.81,0.62-3.44,1.78-4.71c1.34-1.46,3.32-2.27,5.57-2.27c2.05,0,3.88,0.7,5.03,1.91
                        c1,1.07,1.53,2.56,1.53,4.32c0,1.6-0.49,3.11-1.39,4.26c-0.23,0.3-0.45,0.54-0.68,0.74c-0.81,0.73-1.43,1.01-2,1.2
                        C103.84,24.32,103.08,24.04,102.78,23.42z M100.16,14.89c-0.32,0.11-0.61,0.28-0.93,0.55c-0.89,0.76-1.47,2.01-1.47,3.2
                        c0,0.97,0.36,1.49,0.54,1.49c0,0,0.3-0.02,0.85-1.39C99.6,17.59,99.97,16.16,100.16,14.89z" />
                <g>
                    <path class="st0" d="M24.35,18.79c-2.64,0.06-6.48,2.38-10.12,0.5c-3.15-1.67-1.21-7.11,1.41-6.7c2.08,0.37,8.89,0.51,11.64-0.57
                            c0.75-0.3,1.17-0.89,1.17-1.79c0-1.94-1.45-6.62-1.83-7.84C26.38,1.47,25.28,0,23.08,0C17.04,0,6.38,3.1,1.78,11.77
                            c-7.37,14.76,9.55,19.77,23.97,14.43C29.82,24.77,28.95,18.74,24.35,18.79z M25.24,24.19c-8.64,3.53-23.31,2.97-23.12-6.53
                            C2.59,11.98,8.7,3.28,22.71,2.05c0.83-0.08,1.76,0.38,1.96,1.09c0.35,1.29,1.31,4.87,1.67,6.33c0.14,0.59-0.41,0.75-1.02,0.91
                            c-2.18,0.59-6.16,0.56-9.55,0.12c-4.25-0.32-6.92,5.77-4.23,9.22c2.84,3.71,7.86,2.41,12.69,1.18
                            C26.18,20.46,27.28,23.39,25.24,24.19z" />
                    <circle class="st0" cx="22.74" cy="7.03" r="1.44" />
                    <circle class="st0" cx="17.42" cy="7.14" r="1.44" />
                </g>
            </g>
        </symbol>
        <symbol id="arrow_upward" viewBox="0 0 24 24">
            <path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z" />
        </symbol>
        <symbol id="open_in_new" viewBox="0 0 24 24">
            <path
                d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z" />
        </symbol>
    </defs>
</svg>
<header>
    <div id="top" class="header-content center-block">
        <div class="logo">
            <div class="logo">
                <a href="/">
                    <img src="{{ static.staticImage(" img_logo.svg") }}" height="80" width="273" alt="くらしのマーケット">
                </a>
            </div>
        </div>
        <a href="/category/" class="btn btn-category-list btn-basic">カテゴリ一覧</a>
        <div class="link-area">
            <ul class="user-guide-menu list-unstyled">
                <li><a href="/lp/shop/">出店しませんか？</a></li>
                <li><a href="/about/">はじめての方へ</a></li>
            </ul>
            <div class="text-right">
                <a class="btn btn-mypage btn-m" href="/mypage/" role="button">マイページ</a>
            </div>
        </div>
    </div>
</header>
{% endmacro %}

{% macro breadCrumb(title) %}
<div class="breadcrumb">
    <ol class="container center-block" itemscope itemtype="http://schema.org/BreadcrumbList">
        <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <a itemprop="item" href="/"><span itemprop="name">くらしのマーケット</span></a>
            <meta itemprop="position" content="1" />
        </li>
        <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <a href="/featured/list/" itemprop="item"><span itemprop="name">特集一覧</span></a>
            <meta itemprop="position" content="2" />
        </li>
        <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <strong><span itemprop="name">{{ title }}</span></strong>
            <meta itemprop="position" content="3" />
        </li>
    </ol>
</div>
{% endmacro %}

{% macro spBreadCrumb(title, level4="", titleLink="") %}
<div class="pad-t_16 pad-r_16 pad-b_24 pad-l_16 fon-s_12">
    <ol class="mar_0 pad_0 dis_b breadcrumb" itemscope itemtype="http://schema.org/BreadcrumbList">
        <li class="m_list_none dis_i" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <a href="/" itemprop="item" class="col_bla10"><span itemprop="name">くらしのマーケット</span></a>
            <meta itemprop="position" content="1" />
        </li>
        <li class="m_list_none dis_i" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <a href="/featured/list/" class="col_bla10" itemprop="item"><span itemprop="name">特集一覧</span></a>
            <meta itemprop="position" content="2" />
        </li>
        <li class="m_list_none dis_i" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            {% if titleLink %}
                <a href="{{ titleLink }}" class="col_bla10" itemprop="item"><span itemprop="name">{{ title }}</span></a>
            {% else %}
                <span itemprop="name">{{ title }}</span>
            {% endif %}
            <meta itemprop="position" content="3" />
        </li>
        {% if level4 %}
        <li class="m_list_none dis_i" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <span itemprop="name">{{ level4 }}</span>
            <meta itemprop="position" content="4" />
        </li>
        {% endif %}
    </ol>
</div>
{% endmacro %}