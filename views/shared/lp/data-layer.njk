{% macro dataLayerScript() %}
    <script type="text/javascript">
        function notifyEcommerceEventToApp(params) {
            if (!params || !navigator.userAgent.match(/UserApp/i)) { return; }
            try {
                if (navigator.userAgent.match(/Android/i)) {
                    gaApp.notifyEcommerceEvent(JSON.stringify(params));
                } else if(navigator.userAgent.match(/iPhone/i)) {
                    webkit.messageHandlers.gaApp.postMessage(params);
                }
            } catch(err) {
                console.log('The native context does not exist yet');
            }
        }
    </script>
{% endmacro %}
