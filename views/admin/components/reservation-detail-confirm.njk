{% macro ReservationDetailConfirm() %}
  <script>
    function ReservationDetailConfirm() {
      return {
        $template: '#reservation-detail-confirm',
        getTemplateDisabled(templateId) {
          if (!templateId) {
            return true;
          }
          const activeDocument = this
            .activeAdditionalInfo
            .find(obj => obj.templateType.toString() === templateId);
          return !!activeDocument;
        },

        getAddingDocumentStatus(statusCode) {
          if (!statusCode) {
            return '';
          }
          const status = this
            .listAddingDocumentStatus
            .find(obj => obj.id === statusCode.toString());
          return status
            ?.name
        },

        shortenString(str, maxLength = 50) {
          if (str.length <= maxLength) return str;
          if (maxLength <= 3) return str.slice(0, maxLength);
          return str.slice(0, maxLength - 3) + '...';
        },

        copyUrl(id, url) {
          navigator.clipboard.writeText(url);
          this.setTooltip(id, "Copied!");
          this.hideTooltip(id);
        },

        setTooltip(id, message) {
          $("#jsCopy-"+ id)
            .tooltip("hide")
            .attr("data-original-title", message)
            .tooltip("show");
        },

        hideTooltip(id) {
          setTimeout(function() {
            $("#jsCopy-"+ id)
              .tooltip("hide")
              .removeAttr("data-original-title");
          }, 1000);
        },

        generateAddingDocumentUrl(token) {
          return `${this.appUrl}/additional-information/?token=${token}`;
        },

        async handleCreateUrl(templateId) {
          if (this.getTemplateDisabled(templateId)) return;
          this.isLoading = true;
          try {
            const response = await axiosClient.post(`/admin/labo-install-pack/api/reservations/${this.reservation.id}/create-url/`, {
              templateId: templateId,
            });
            if (response.data?.error) {
              this.errorData = 'エラーが発生しました';
              this.isLoading = false;
              return;
            }
            location.reload();
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        async handleRefreshUrl() {
          if (!this.selectedAdditionalInfoId) {
            $('#refreshUrlModal').modal('hide');
            return;
          }
          this.isLoading = true;
          try {
            const response = await axiosClient.put(`/admin/labo-install-pack/api/additional-information/${this.selectedAdditionalInfoId}/refresh/`, {});
            if (response.data?.error) {
              this.errorData = 'エラーが発生しました';
              this.isLoading = false;
              return;
            }
            location.reload();
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        async handleDeletePackage() {
          if (!this.selectedAdditionalInfoId) {
            $('#deleteUrlModal').modal('hide');
            return;
          }
          this.isLoading = true;
          try {
            const response = await axiosClient.delete(`/admin/labo-install-pack/api/additional-information/${this.selectedAdditionalInfoId}/`);
            if (response.data?.error) {
              this.errorData = 'エラーが発生しました';
              this.isLoading = false;
              return;
            }
            location.reload();
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        handleOpenRefreshModal(additionalInfo) {
          if (additionalInfo.status !== 3) {
            return;
          }
          this.selectedAdditionalInfoId = additionalInfo.id;
          $('#refreshUrlModal').modal('show');
        },

        handleOpenDeleteModal(id) {
          this.selectedAdditionalInfoId = id;
          $('#deleteUrlModal').modal('show');
        },
      };
    }
  </script>
  {% raw %}
    <template id="reservation-detail-confirm">
      <div class="row mb-10">
        <div class="col-md-3">ユーザーID</div>
        <div class="col-md-9">{{reservation.userId}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3">お名前（姓）</div>
        <div class="col-md-3">{{reservation.lastname}}</div>
        <div class="col-md-3">お名前（名）</div>
        <div class="col-md-3">{{reservation.firstname}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3">電話番号</div>
        <div class="col-md-9">{{reservation.phoneNumber}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3">Email</div>
        <div class="col-md-9">{{reservation.email}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3">Card</div>
        <div class="col-md-9">{{reservation.cardLast4Digit && `xxxx-xxxxx-xxxxx-${reservation.cardLast4Digit}`}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3">Amount</div>
        <div class="col-md-9">{{reservation.totalFee > 0 ? formatCurrency(reservation.totalFee) : ""}}{{reservation.totalDiscount ? `（Discount: ${formatCurrency(reservation.totalDiscount)}）` : ""}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3">支払日</div>
        <div class="col-md-9">{{reservation.paymentDate || ''}}</div>
      </div>
      <div v-for="additionalInfo in activeAdditionalInfo" :key="additionalInfo.id" class="row mb-10 items-center pl-15">
        <div>
          <div class="row mb-5">
            <div class="col-md-12">
              <span class="mr-8 template-title">登録状況</span>
              <span class="mr-8">{{getAddingDocumentStatus(additionalInfo.status)}}</span>
              <span><span class="glyphicon glyphicon-file mr-5"></span>{{getDocumentTemplate(additionalInfo.templateType)}}</span>
            </div>
          </div>
          <div class="row mb-5">
            <div class="col-md-12">
              <div class="overflow-url" :class="additionalInfo.status !== 3 ? '' : 'col-gre0'"><span class="glyphicon glyphicon-link mr-5"></span>{{generateAddingDocumentUrl(additionalInfo.verificationToken)}}</div>
            </div>
          </div>
        </div>
        <div class="ml-10 min-w-65">
          <span class="glyphicon glyphicon-copy mr-5 cur_p" @click="copyUrl(additionalInfo.id, generateAddingDocumentUrl(additionalInfo.verificationToken))"  :id="'jsCopy-' + additionalInfo.id" :data-clipboard-text="generateAddingDocumentUrl(additionalInfo.verificationToken)"></span>
          <span class="glyphicon glyphicon-refresh mr-5" :class="additionalInfo.status !== 3 ? 'col-gre0' : 'cur_p'" @click="handleOpenRefreshModal(additionalInfo)"></span>
          <span class="glyphicon glyphicon-trash mr-5 col-red0 cur_p" @click="handleOpenDeleteModal(additionalInfo.id)"></span>
        </div>
      </div>
      <div class="row mb-10">
        <div class="col-md-3"></div>
        <div class="col-md-9 block-confirm-button">
          <button class="btn btn-primary">Confirm</button>
          <button class="btn btn-warning" data-target="#createUrlModal" data-toggle="modal">Create URL</button>
          <button class="btn btn-danger">Cancel</button>
        </div>
      </div>

      <div id="createUrlModal" class="modal fade" data-backdrop="static" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title">Create URL</h4>
              <div class="close-modal cur_p">
                <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
              </div>
            </div>
            <div class="form-horizontal modal-form" id="createUrlModal" name="createUrlModal">
              <div class="container-fluid">
                <div class="form-group">
                  <div class="modal-content-custom">
                    フォームを選んでください
                  </div>
                  <div class="list-group action">
                    <a v-for="option in listDocumentTemplate" :key="option.id" class="list-group-item content no-border overflow-anywhere text-left" :class="getTemplateDisabled(option.id) ? 'disabled' : ''" @click="handleCreateUrl(option.id)">{{option.name}}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div id="refreshUrlModal" class="modal fade" data-backdrop="static" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="form-horizontal modal-form" id="refreshUrlModal" name="refreshUrlModal">
              <div class="container-fluid">
                <div class="form-group">
                  <div class="modal-content-custom">
                    URLを再有効化してもよろしいですか？
                  </div>
                </div>
                <div class="form-group items-center modal-form-button-center">
                  <button class="btn btn-default" data-dismiss="modal">Cancel</button>
                  <button class="btn btn-primary" @click="handleRefreshUrl">Confirm</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div id="deleteUrlModal" class="modal fade" data-backdrop="static" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="form-horizontal modal-form" id="deleteUrlModal" name="deleteUrlModal">
              <div class="container-fluid">
                <div class="form-group">
                  <div class="modal-content-custom">
                    この登録フォームを削除してもよろしいですか？<br>対応するアップロード済みファイルも削除されます。
                  </div>
                </div>
                <div class="form-group items-center modal-form-button-center">
                  <button class="btn btn-default" data-dismiss="modal">Cancel</button>
                  <button class="btn btn-danger" @click="handleDeletePackage">Delete</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
