{% macro ReservationDetailInfo() %}
  <script>
    function ReservationDetailInfo() {
      return {
        $template: '#reservation-detail-info',
        getPackageDetailUrl(packageId) {
          return `/admin/labo-install-pack/packages/${packageId}`;
        },
      };
    }
  </script>
  {% raw %}
    <template id="reservation-detail-info">
      <div class="row mb-10">
        <div class="col-md-6">予約コード</div>
        <div class="col-md-6">{{reservation.code}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">パッケージ名</div>
        <div class="col-md-6">
          <div v-if="isUnspecifiedReservation">
            製品未定
          </div>
          <div v-else>
            <div v-for="pack in reservation.reservationsPackages" :key="pack.id" class="flex-column">
              <span>{{pack.package.name}} {{formatCurrency(pack.packPrice)}}/台（{{pack.quantity}}台）（<a :href="getPackageDetailUrl(pack.package.id)">{{pack.package.code}}</a>）</span>
              <span v-if="pack.packOptions">
                <span v-for="opt in pack.packOptions.listOptions" :key="opt.code">ドアパネル : {{opt.value}}（{{formatCurrency(opt.fee)}}）</span>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="row mb-10" v-for="option in reservationOptionsData" :key="option.id">
        <div class="col-md-6">{{option.title}}</div>
        <div class="col-md-6">{{option.value}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">設置場所の郵便番号（ハイフンなし）</div>
        <div class="col-md-6">{{reservation.postalcode}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">設置場所の住所（都・県）</div>
        <div class="col-md-6">{{reservation.prefecture}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">設置場所の住所（市区町村）</div>
        <div class="col-md-6">{{reservation.city}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">設置場所の住所</div>
        <div class="col-md-6">{{reservation.address}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">設置日の最終期限</div>
        <div class="col-md-6">{{reservation.workingDate2 || reservation.workingDate1}}</div>
      </div>
      <div class="row mb-10">
        <div class="col-md-6">予約日</div>
        <div class="col-md-6">{{dateUtils.formatDateTime(reservation.createdAt, 'YYYY-MM-DD')}}</div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
