{% macro ReservationDetailAction() %}
  <script>
    function ReservationDetailAction() {
      return {
        $template: '#reservation-detail-action',
        handleChangeStatus() {
          // TODO: Implement change status
          return;
        },

        handleChangeWorkingDate() {
          // TODO: Implement change working date
          return;
        },

        handleChangePartner() {
          // TODO: Implement change partner
          return;
        },
      };
    }
  </script>
  {% raw %}
    <template id="reservation-detail-action">
      <div class="row mb-10">
        <div class="col-md-2">ステータス</div>
        <div class="col-md-4">
          <select 
            v-model="selectedStatus" 
            class="form-control" 
            name="status" 
            id="status" 
            @change="handleChangeStatus"
            :disabled="isDisabled">
            <option v-for="option in listStatus" :key="option.id" v-bind:value="option.id">{{ option.name }}</option>
          </select>
        </div>
        <div class="col-md-2">パートナー</div>
        <div class="col-md-4">
          <select 
            v-model="selectedPartner" 
            class="form-control" 
            name="partner" 
            id="partner" 
            @change="handleChangePartner"
            :disabled="isDisabled">
            <option value="">-----</option>
            <option value="Curama">Curama</option>
          </select>
        </div>
      </div>
      <div class="row mb-10">
        <div class="col-md-2">作業日</div>
        <div class="col-md-4">
          <input type="text" id="dayOff" name="dayOff" class="daterange-field form-control" readonly="">
        </div>
        <div class="col-md-2">
          <span id="dayOffDatePicker" class="glyphicon glyphicon-calendar btn btn-default" aria-hidden="true"></span>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
