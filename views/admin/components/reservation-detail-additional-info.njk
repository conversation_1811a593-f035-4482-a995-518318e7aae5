{% macro ReservationDetailAdditionalInfo() %}
  <script>
    function ReservationDetailAdditionalInfo() {
      return {
        $template: '#reservation-detail-additional-info',
        async handleChangeTab(id) {
          if (this.activeAdditionTab === id) {
            return;
          }
          this.isLoading = true;
          try {
            const currentSelectedAddition = this.reservation.additionalInformation.find((addition) => addition.id === id);
            const response = await axiosClient.get(`/admin/labo-install-pack/api/additional-information/${id}/activities/`);
            this.activeAdditionTab = id;
            const activities = response.data?.data;
            if (activities[0]?.type === 4 && currentSelectedAddition?.isEnable) {
              location.reload();
              return;
            }
            if (activities[0]?.type !== 4 && !currentSelectedAddition.isEnable) {
              location.reload();
              return;
            }
            this.additionActivities = activities;
            this.isLoading = false;
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        async handleDownloadDocument() {
          this.isLoading = true;
          const refreshAttemptData = await axiosClient.get(`/admin/labo-install-pack/api/additional-information/${this.activeAdditionTab}/addition-attempts/${this.selectedAttemptId}/`);
          if (!refreshAttemptData?.data?.data) {
            return;
          }
          const currentAttempt = refreshAttemptData.data.data;
          let zip = new JSZip();
          let fileName = `${this.reservation.code}-${dateUtils.formatDateTime(currentAttempt.createdAt, 'YYYYMMDD')}`;
          let promises = [];
          let outputFiles = zip.folder(fileName);
          currentAttempt?.documents?.forEach((document) => {
            promises.push(this.urlToPromise(document.url)
              .then((data) => {
                if(data) {
                  outputFiles.file(document.name, data, {binary:true});
                }
              }));
          });
          await Promise.all(promises).then(() => {
            zip.generateAsync({type:"blob"})
              .then((content) => {
                saveAs(content, `${fileName}.zip`);
              });
          });
          this.isLoading = false;
        },

        urlToPromise(url) {
          return new Promise((resolve) => {
            JSZipUtils.getBinaryContent(url, function (err, data) {
              if(err) {
                console.error(`Error fetching ${url}:`, err);
                resolve(null);
              } else {
                resolve(data);
              }
            });
          });
        },

        async handleOpenAttemptDocumentsModal(attempt, doc) {
          this.isLoading = true;
          this.selectedAttemptId = attempt.id;
          this.selectedDocumentId = doc.id;
          const refreshAttemptData = await axiosClient.get(`/admin/labo-install-pack/api/additional-information/${this.activeAdditionTab}/addition-attempts/${this.selectedAttemptId}/`);
          if (refreshAttemptData.data?.data?.isDeleted) {
            location.reload();
            return;
          }
          this.selectedAttemptDocuments = refreshAttemptData.data.data.documents;
          if (doc.documentType == 2) {
            this.handleShowPdfDocument(doc);
          }
          $('#attemptDocumentsModal').modal('show');
          this.isLoading = false;
        },

        handleChangeShowingDocument(doc) {
          if (this.selectedDocumentId === doc.id) {
            return;
          }
          this.selectedDocumentId = doc.id;
          if (doc.documentType == 2) {
            this.handleShowPdfDocument(doc);
          }
        },

        handleShowPdfDocument(doc) {
          document.querySelectorAll('.pdf-document').forEach((ele) => {
            ele.innerHTML = '';
          });
          setTimeout(() => {
            fetch(doc.url)
              .then((response) => {
                if (response.ok) {
                  PDFObject.embed(doc.url, `#document-presentation-${doc.id}`, {height: "500px", pdfOpenParams: {toolbar: 0, navpanes: 0, scrollbar: 0}});
                } else {
                  if (response.status === 403) {
                    location.reload();
                    return;
                  }
                  document.getElementById(`document-presentation-${doc.id}`).innerHTML = `<p class="error-message">PDFの読み込みに失敗しました。</p>`;
                }
              })
              .catch((error) => {
                document.getElementById(`document-presentation-${doc.id}`).innerHTML = `<p class="error-message">PDFの読み込みに失敗しました。</p>`;
              });
          }, 0);
        },

        generateClassDocumentContainer(document) {
          let className = [];
          if (document.documentType == 2) {
            className.push('pdf-document');
          }
          if (this.selectedDocumentId !== document.id) {
            className.push('hide');
          }
          return className.join(' ');
        },

        generateClassAdditionTab(addition) {
          let className = [];
          if (!addition?.isEnable) {
            className.push('deleted-tab');
          }
          if (addition?.id === this.activeAdditionTab) {
            className.push('deleted-tab-highlight');
          }
          return className.join(' ');
        },

        getAdditionalInfoType(activity) {
          const additionalInfo = this.reservation.additionalInformation.find((addition) => addition.id === activity?.attempt?.additionalInformationId);
          if (!additionalInfo) {
            return '';
          }
          return additionalInfo.templateType;
        },

        generateActivityContent(activity) {
          switch (activity.type) {
            case 1:
              return `新規作成されました。`;
            case 3:
              return `再有効化されました。`;
            case 4:
              return `削除されました。`;
            default:
              return '';
          }
        },
      };
    }
  </script>
  {% raw %}
    <template id="reservation-detail-additional-info">
      <ul class="nav nav-tabs">
        <li v-for="addition in reservation.additionalInformation" :key="addition.id" :class="addition.id === activeAdditionTab ? 'active' : ''" >
          <a :class="generateClassAdditionTab(addition)" @click="handleChangeTab(addition.id)" href="javascript:void(0)">{{ getDocumentTemplate(addition.templateType) }}</a>
        </li>
      </ul>
      <div class="additional-info-container">
        <div class="additional-info-content">
          <table class="table borderless table-striped">
            <tbody>
              <tr v-for="activity in additionActivities" :key="activity.id">
                <td class="addition-time">{{ dateUtils.formatDateTime(activity.createdAt, 'YYYY-MM-DD HH:mm') }}</td>
                <td>
                  <div v-if="activity.type !== 2">{{ generateActivityContent(activity) }}</div>
                  <div v-else>
                    <div v-if="getAdditionalInfoType(activity) == 3">
                      「{{ activity?.attempt?.detail?.faucetModelNumber }}」の水栓品番が更新されました。
                    </div>
                    <div v-if="getAdditionalInfoType(activity) == 4">
                      「{{ activity?.attempt?.detail?.faucetModelNumber }}」の既設ビルトイン食洗機品番が更新されました。
                    </div>
                    <div v-else>
                      <div v-if="activity?.attempt?.isDeleted">
                        <div v-for="(_, index) in new Array(Math.min(5, activity?.attempt?.documents?.length)).fill(0)" :key="index" class="deleted-thumbnail">
                          <div class="deleted-thumbnail-content"><p>DELETED<p></div>
                        </div>
                      </div>
                      <div v-else>
                        <div v-for="(_, index) in new Array(Math.min(5, activity?.attempt?.documents?.length)).fill(0)" :key="index" class="document-thumbnail-container">
                          <img loading="lazy" :src="activity?.attempt?.documents[index].documentType == 1 ? activity?.attempt?.documents[index].urlThumb : pdfIcon" :alt="activity?.attempt?.documents[index].name" class="document-thumbnail cur_p" @click="handleOpenAttemptDocumentsModal(activity.attempt, activity?.attempt?.documents[index])">
                        </div>
                        <div v-if="activity?.attempt?.documents.length > 5" class="show-more-document-container cur_p" @click="handleOpenAttemptDocumentsModal(activity.attempt, activity?.attempt?.documents[5])">
                          <div class="show-more-document">
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div id="attemptDocumentsModal" class="modal fade" data-backdrop="static" role="dialog">
        <div class="modal-lg modal-dialog">
          <div class="modal-content attempt-modal">
            <div class="modal-header">
              <h4 class="modal-title"></h4>
              <div class="close-modal cur_p">
                <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
              </div>
            </div>
            <div class="form-horizontal modal-form" id="attemptDocumentsModal" name="attemptDocumentsModal">
              <div class="container-fluid">
                <div class="row mb-20">
                  <div class="col-md-9 flex-center">
                    <div v-for="document in selectedAttemptDocuments" :key="selectedAttemptId + '_' + document.id" :id="'document-presentation-' + document.id" class="col-xs-12 flex-center" :class="generateClassDocumentContainer(document)">
                      <img v-if="document.documentType == 1" :src="document.url" class="document-image" loading="lazy" :alt="document.name">
                    </div>
                  </div>
                  <div class="col-md-3 documents-container">
                    <div 
                      v-for="document in selectedAttemptDocuments" 
                      :key="selectedAttemptId + '_' + document.id" 
                      class="document-preview-container" 
                      :class="document.id === selectedDocumentId ? 'selected-document' : ''">
                      <img 
                        v-if="document.documentType == 1"
                        :src="document.urlThumb" 
                        @click="handleChangeShowingDocument(document)"
                        class="document-preview cur_p" 
                        loading="lazy" 
                        :alt="document.name">
                      <div 
                        v-if="document.documentType == 2"
                        @click="handleChangeShowingDocument(document)"
                        class="document-preview cur_p">
                        <img 
                          :src="pdfIcon" 
                          loading="lazy" 
                          :alt="document.name"
                          class="w-80">
                        <div class="document-filename" v-html="document.name"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-group modal-document-footer">
                  <span class="glyphicon glyphicon-download-alt mr-10 col-blu0 cur_p f-s_25" @click="handleDownloadDocument"></span>
                  <button class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  {% endraw %}
{% endmacro %}
