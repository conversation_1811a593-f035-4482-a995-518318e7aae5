{% macro adminSidebar(activePage) %}
  <ul class="nav nav-sidebar">
    <li class="{% if activePage == 'Reservations' %}active{% else %}inactive{% endif %}">
      <a href="/admin/labo-install-pack/reservations">Reservations
        <span class="sr-only visually-hidden">(current)</span>
      </a>
    </li>
    <li class="{% if activePage == 'Packages' %}active{% else %}inactive{% endif %}">
      <a href="/admin/labo-install-pack/packages">Packages
        <span class="sr-only visually-hidden">(current)</span>
      </a>
    </li>
    <li class="{% if activePage == 'CalendarBlock' %}active{% else %}inactive{% endif %}">
      <a href="/admin/labo-install-pack/calendar-blocks">Calendar block
        <span class="sr-only visually-hidden">(current)</span>
      </a>
    </li>
    <li class="{% if activePage == 'ReservationOptions' %}active{% else %}inactive{% endif %}">
      <a href="/admin/labo-install-pack/reservation-options">Reservation options
        <span class="sr-only visually-hidden">(current)</span>
      </a>
    </li>
  </ul>
{% endmacro %}