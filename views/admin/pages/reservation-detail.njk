{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}
{% from "admin/components/reservation-detail-info.njk" import ReservationDetailInfo %}
{% from "admin/components/reservation-detail-confirm.njk" import ReservationDetailConfirm %}
{% from "admin/components/reservation-detail-action.njk" import ReservationDetailAction %}
{% from "admin/components/reservation-detail-additional-info.njk" import ReservationDetailAdditionalInfo %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
  <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/daterangepicker.css") }}">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  {% include "../../assets/dayjs.njk" %}
  {% include "../../assets/daterangepicker.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
  <script src="{{ assets.url("js/pdfobject.min.js") }}"></script>
  {% include "../../assets/jszip.njk" %}
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}
  {{ ReservationDetailInfo() }}
  {{ ReservationDetailConfirm() }}
  {{ ReservationDetailAction() }}
  {{ ReservationDetailAdditionalInfo() }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}

    function PackageDetailPage(props) {
      const requiredMsg = "このフィールドは必須です。";
      return {
        pdfIcon: "{{ assets.url('image/common/pdf-icon.png') }}",
        isLoading: false,
        isDisabled: false,
        appUrl: initialData.appUrl,
        reservation: initialData.reservationDetail || {},
        reservationOptionsData: initialData.reservationOptionsData || {},
        activeAdditionalInfo: initialData.reservationDetail?.additionalInformation?.filter((item) => item?.isEnable) || [],
        listStatus: initialData.status,
        listPackageType: initialData.listPackageType || [],
        listAddingDocumentStatus: initialData.addingDocumentStatus,
        listDocumentTemplate: initialData.documentTemplate,
        additionActivities: initialData.additionActivities,
        activeAdditionTab: initialData.reservationDetail?.additionalInformation?.length ? initialData.reservationDetail?.additionalInformation[0].id : "",
        isUnspecifiedReservation: !initialData.reservationDetail?.reservationsPackages?.length || false,
        selectedStatus: initialData.reservationDetail.status?.toString(),
        selectedWorkingDate: initialData.reservationDetail.workingDate1,
        selectedPartner: initialData.reservationDetail.partner || "",
        selectedAttemptId: "",
        selectedAttemptDocuments: [],
        selectedDocumentId: "",
        errorData: '',
        formatCurrency: {{formatCurrency | safe}},
        selectedAdditionalInfoId: '',
        errors: errors,

        manipulateDatePicker() {
          const daterangePickerInit = {
            minDate: dayjs().startOf('day'),
            locale: {
              format: 'YYYY-MM-DD'
            },
            singleDatePicker: true
          }
          if (this.selectedWorkingDate) {
            daterangePickerInit.startDate = this.selectedWorkingDate;
            $('#dayOff').val(this.selectedWorkingDate);
          }
          $('#dayOffDatePicker').daterangepicker(daterangePickerInit);
          $('#dayOffDatePicker').on('apply.daterangepicker', (ev, picker) => {
            const selectedWorkingDate = picker.startDate.format("YYYY-MM-DD");
            this.selectedWorkingDate = selectedWorkingDate;
            $("#dayOff").val(selectedWorkingDate);
          });
        },

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
          this.manipulateDatePicker();
          $("#refreshUrlModal").on("hidden.bs.modal", () => {
            this.selectedAdditionalInfoId = '';
          });
          $("#deleteUrlModal").on("hidden.bs.modal", () => {
            this.selectedAdditionalInfoId = '';
          });
        },

        getPackTypeName(packType) {
          if (!packType) {
            return '';
          }
          const packageType = this
            .listPackageType
            .find(obj => obj.id === packType.toString());
          if (!packageType) {
            return '';
          }
          return packageType.name;
        },

        getDocumentTemplate(templateId) {
          if (!templateId) {
            return '';
          }
          const template = this
            .listDocumentTemplate
            .find(obj => obj.id === templateId.toString());
          return template?.name;
        },
      }
    }
  </script>

  {% raw %}
    <div v-scope="PackageDetailPage()" @vue:mounted="mounted" v-cloak>
      <div class="loading-mask" v-if="isLoading">
        <div class="loader"></div>
      </div>
      <div class="tab-content" :class="isLoading ? 'tranparent-block' : ''">
        <div class="tab-pane fade in active">
          <h2>Reservation</h2>
          <hr>
          <div>
            <div class="panel panel-primary">
              <div class="panel-heading">Reservation detail {{reservation.code}}（{{getPackTypeName(reservation.packType)}}）</div>
              <div class="panel-body">
                <div class="block-detail">
                  <div v-scope="ReservationDetailInfo()" class="block-info"></div>
                  <div v-scope="ReservationDetailConfirm()" class="block-confirm"></div>
                  <div v-scope="ReservationDetailAction()" class="block-action"></div>
                  <div v-scope="ReservationDetailAdditionalInfo()" class="block-additional-info"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}