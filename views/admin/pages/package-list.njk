{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}

    function PackageListPage() {
      return {
        limit: initialData.pagination.perPage,
        page: initialData.pagination.pageNumber,
        total: initialData.pagination.total,
        listStatus: initialData.status,
        packType: initialData.packType,
        listPackageGroups: initialData.packageGroups,
        isLoading: false,
        data: initialData.packages || [],
        allPackages: [],
        selectedPackages: [],
        filterInput: { packType: initialData.packType },
        errors: errors,

        async changePage(page) {
          this.filterInput = {
            ...this.filterInput,
            page
          }
          await this.handleGetPackages(this.filterInput)
        },

        async handleGetPackages(params) {
          this.isLoading = true;
          try {
            const response = await axiosClient.get(`/admin/labo-install-pack/api/packages`, {params});
            this.data = response.data.packages;
            this.total = response.data.pagination
              ?.total;
            this.page = response.data.pagination
              ?.pageNumber;
          } catch (e) {
            alert(`[ERROR]: ${e.response.data.message}`);
          }
          this.isLoading = false;
        },

        getPackageStatus(statusCode) {
          const status = this
            .listStatus
            .find(obj => obj.id === statusCode.toString());
          return status?.name
        },

        getPackageBadge(statusCode) {
          switch (statusCode) {
            case 1:
              return 'label-primary';
            case 2:
              return 'label-default';
            default:
              return '';
          }
        },

        getPackageGroup(groupId) {
          const group = this
            .listPackageGroups
            .find(obj => obj.id === groupId.toString());
          return group?.name
        },

        handleSelectAllPackage() {
          if (this.selectedPackages.length === this.allPackages.length) {
            this.selectedPackages = [];
          } else {
            this.selectedPackages = this.allPackages.map(package => package.id);
          }
        },

        getPackageDetailUrl(packageId) {
          return `/admin/labo-install-pack/packages/${packageId}`;
        },

        async exportPackages() {
          try {
            this.isLoading = true;
            if (!this.selectedPackages.length) {
              this.isLoading = false;
              return;
            }
            const response = await axiosClient.post(`/admin/labo-install-pack/api/packages/export/`, { packageIds: this.selectedPackages });
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'packages.csv');
            document.body.appendChild(link);
            link.click();
            $('#exportPackageModal').modal('hide');
            this.selectedPackages = [];
            window.location.reload();
            this.isLoading = false;
          } catch (e) {
            alert(`[ERROR]: ${e.response.data.message}`);
            this.isLoading = false;
          }
        },

        async handleAllGetPackages() {
          if (this.allPackages.length) {
            return;
          }
          this.isLoading = true;
          try {
            const params = { packType: this.filterInput.packType };
            const response = await axiosClient.get(`/admin/labo-install-pack/api/packages/all`, {params});
            this.allPackages = response.data.packages;
          } catch (e) {
            alert(`[ERROR]: ${e.response.data.message}`);
          }
          this.isLoading = false;
        },

        async handleChangeFile(event) {
          if (!event.target.files[0]) {
            return;
          }
          this.isLoading = true;
          try {
            const messageData = new FormData();
            messageData.append("file", event.target.files[0], event.target.files[0].name);
            const response = await axiosClient({
              method: "post",
              url: "/admin/labo-install-pack/api/packages/import/",
              data: messageData,
              headers: {
                "Content-Type": "multipart/form-data"
              }
            })
            if (response.data?.error) {
              alert(response.data.error);
              this.isLoading = false;
              document.getElementById("file-input").value = "";
              return;
            }
            alert(response.data?.message);
            document.getElementById("file-input").value = "";
            window.location.reload();
            this.isLoading = false;
          } catch (error) {
            alert(`[ERROR]: ${error.response.data.message}`);
            document.getElementById("file-input").value = "";
            this.isLoading = false;
          }
        }
      }
    }
  </script>

  {% raw %}
    <div v-scope="PackageListPage()" v-cloak>
      <div class="tab-pane fade in active">
        <h2>Package</h2>
        <hr>
        <ul class="nav nav-tabs">
          <li :class="packType === 1 ? 'active' : ''">
            <a href="/admin/labo-install-pack/packages?packType=1">Aircon Install Pack</a>
          </li>
          <li :class="packType === 2 ? 'active' : ''">
            <a href="/admin/labo-install-pack/packages?packType=2">Water Heater Pack</a>
          </li>
          <li :class="packType === 3 ? 'active' : ''">
            <a href="/admin/labo-install-pack/packages?packType=3">Dishwasher Pack</a>
          </li>
        </ul>
        <div>
          <div class="panel panel-primary mt-10">
            <div class="panel-heading">
              <h3 class="panel-title">Package List</h3>
            </div>
            <div class="panel-body">
              <div>
                <div class="row pl-15 pr-15 mb-20">
                  <div class="router-control">
                    <input id="file-input" type="file" @change="handleChangeFile" class="hidden" accept=".csv"></input>
                    <button class="btn btn-primary">
                      <label for="file-input" class="m-0 f-w_400">import</label>
                    </button>
                    <button type="button" class="btn btn-primary ml-10" data-target="#exportPackageModal" data-toggle="modal" @click="handleAllGetPackages">export</button>
                    <a href="/admin/labo-install-pack/packages/new" class="btn btn-primary ml-10">新しいパッケージを作成する</a>
                  </div>
                </div>

                <div class="row pl-15 pr-15 mt-10">
                  <div class="loading-mask" v-if="isLoading">
                    <div class="loader"></div>
                  </div>
                  <table class="table borderless table-striped" :class="isLoading ? 'tranparent-block' : ''">
                    <thead>
                      <tr>
                        <th>パッケージコード</th>
                        <th>パッケージ名</th>
                        <th>予約の数</th>
                        <th>ステータス</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="package in data" :key="package.id">
                        <td>
                          <a :href="getPackageDetailUrl(package.id)">{{package.code}}</a>
                        </td>
                        <td>{{package.name}}</td>
                        <td>{{package.reservationCount}}</td>
                        <td>
                          <div class="label badge-custom" :class="getPackageBadge(package.status)">{{getPackageStatus(package.status)}}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <nav>
                    <ul class="pager">
                      <li>
                        <a v-if="page>1" @click="changePage(page-1)">前の{{ limit }}件</a>
                        <template v-else>前の{{ limit }}件</template>
                      </li>
                      <li>
                        <a v-if="total > page*limit" @click="changePage(page+1)">次の{{ limit }}件</a>
                        <template v-else>次の{{ limit }}件</template>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div id="exportPackageModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title">Export packages</h4>
              <div class="close-modal">
                <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
              </div>
            </div>
            <div class="form-horizontal modal-form" name="exportPackageModal">
              <div class="container-fluid">
                <div class="loading-mask" v-if="isLoading">
                  <div class="loader"></div>
                </div>
                <div class="export-packages" :class="isLoading ? 'tranparent-block' : ''">
                  <table class="table borderless table-striped">
                    <thead>
                      <tr>
                        <th>パッケージコード</th>
                        <th>パッケージ名</th>
                        <th><button class="btn btn-default" @click="handleSelectAllPackage">Select all</button></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="package in allPackages" :key="package.id">
                        <td>{{package.code}}</td>
                        <td>{{package.name}}</td>
                        <td>
                          <div class="flex-center">
                            <input 
                              type="checkbox" 
                              v-bind:value="package.id" 
                              v-model="selectedPackages" 
                              name="selectedPackages">
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="form-group modal-form-button">
                  <button class="btn btn-default" data-dismiss="modal">Cancel</button>
                  <button class="btn btn-primary" @click="exportPackages" :disabled="!selectedPackages.length">Export</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}