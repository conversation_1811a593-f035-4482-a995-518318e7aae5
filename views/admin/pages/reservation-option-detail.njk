{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}

    function PackageDetailPage(props) {
      const requiredMsg = "このフィールドは必須です。";
      const invalidFeeMsg = "1以上の半角数字を入力してください。";
      const invalidQuantityMsg = "1～10の大きな半角数字を入力してください。";
      return {
        isLoading: false,
        isDisabled: false,
        initOption: JSON.parse(JSON.stringify(initialData.reservationOption)),
        reservationOption: JSON.parse(JSON.stringify(initialData.reservationOption)),
        errorOptions: {},
        errorData: '',
        errors: errors,

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
        },

        async handleFormSubmit(event) {
          if (!this.validateForm()) {
            return;
          }
          if (JSON.stringify(this.initOption) === JSON.stringify(this.reservationOption)) {
            window.location.href = `/admin/labo-install-pack/reservation-options?packType=${this.reservationOption.packType}`;
            return;
          }
          this.isLoading = true;
          const { id, createdAt, updatedAt, title, code, ...rest } = this.reservationOption;
          try {
            const response = await axiosClient.put(`/admin/labo-install-pack/reservation-options/${this.reservationOption.id}/`, rest);
            if (response.status === 200) {
              window.location.href = `/admin/labo-install-pack/reservation-options?packType=${this.reservationOption.packType}`;
            }
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
          }
          this.isLoading = false;
          try {
            return;
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        validateForm() {
          this.errorData = '';
          this.errorOptions = {};
          let isValid = true;
          if (this.reservationOption.type === 'text') {
            this.reservationOption.detail.listOptions.forEach((option, index) => {
              if (!option.title) {
                this.errorOptions[`title-${index}`] = requiredMsg;
                isValid = false;
              }
              if (this.reservationOption.hasPrice) {
                if (option.price === '' || isNaN(option.price) || option.price < 0) {
                  this.errorOptions[`price-${index}`] = invalidFeeMsg;
                  isValid = false;
                }
              }
            });
          }
          if (this.reservationOption.type === 'quantity') {
            if (!this.reservationOption.detail.unit) {
              this.errorOptions['unit'] = requiredMsg;
              isValid = false;
            }
            if (!this.reservationOption.detail.maxQuantity || isNaN(this.reservationOption.detail.maxQuantity) || this.reservationOption.detail.maxQuantity < 0 || this.reservationOption.detail.maxQuantity > 10) {
              this.errorOptions['maxQuantity'] = invalidQuantityMsg;
              isValid = false;
            }
            if (this.reservationOption.hasPrice && (!this.reservationOption.detail.pricePerUnit || isNaN(this.reservationOption.detail.pricePerUnit) || this.reservationOption.detail.pricePerUnit < 0)) {
              this.errorOptions['pricePerUnit'] = invalidFeeMsg;
              isValid = false;
            }
          }
          return isValid;
        },

        handleChangeType() {
          if (this.reservationOption.type === 'quantity') {
            const detailItem = { unit: '', maxQuantity: 0 };
            if (this.reservationOption.hasPrice) {
              detailItem.pricePerUnit = 0;
            }
            this.reservationOption.detail = detailItem;
          }
          if (this.reservationOption.type === 'text') {
            const detailItem = { title: '' };
            if (this.reservationOption.hasPrice) {
              detailItem.price = 0;
            }
            this.reservationOption.detail = { listOptions: [detailItem] };
          }
        },

        handleChangeHasPrice() {
          if (this.reservationOption.type === 'quantity') {
            if (this.reservationOption.hasPrice) {
              this.reservationOption.detail.pricePerUnit = 0;
            } else {
              const { pricePerUnit, ...rest } = this.reservationOption.detail;
              this.reservationOption.detail = rest;
            }
          }
          if (this.reservationOption.type === 'text') {
            const modifiedDetail = this.reservationOption.detail.listOptions.map(option => {
              if (this.reservationOption.hasPrice) {
                return { ...option, price: 0 };
              } else {
                return { title: option.title };
              }
            });
            this.reservationOption.detail = { listOptions: modifiedDetail };
          }
        },

        removeOption(index) {
          this.reservationOption.detail.listOptions.splice(index, 1);
        },

        addOption() {
          if (this.reservationOption.hasPrice) {
            this.reservationOption.detail.listOptions.push({ title: '', price: 0 });
          } else {
            this.reservationOption.detail.listOptions.push({ title: '' });
          }
        },

        handleResetForm() {
          this.reservationOption = JSON.parse(JSON.stringify(this.initOption));
          this.errorOptions = {};
          this.errorData = '';
        },

        handleBlurMaxQuantity(event) {
          if (!event.target.value) {
            event.target.value = 0;
          }
        },

        handleBlurFee(event) {
          if (!event.target.value) {
            event.target.value = 0;
          }
        },
      }
    }
  </script>

  {% raw %}
    <div v-scope="PackageDetailPage()" @vue:mounted="mounted" v-cloak>
      <div class="loading-mask" v-if="isLoading">
        <div class="loader"></div>
      </div>
      <div class="tab-content" :class="isLoading ? 'tranparent-block' : ''">
        <div class="tab-pane fade in active">
          <h2>Reservation Options</h2>
          <hr>
          <div>
            <div class="panel panel-primary">
              <div class="panel-heading">Reservation Options Management</div>
              <div class="panel-body">
                <form class="packageDetailForm form-inline" @submit.prevent="handleFormSubmit" name="packageDetailForm">
                  <div class="row input-control">
                    <div class="col-sm-3">
                      <label class="input-label">Title</label>
                    </div>
                    <div class="col-sm-9 input-label">{{ reservationOption.title }}</div>
                  </div>

                  <div class="row input-control">
                    <div class="col-sm-3">
                      <label class="input-label">Code</label>
                    </div>
                    <div class="col-sm-9 input-label">{{ reservationOption.code }}</div>
                  </div>

                  <div class="row input-control">
                    <div class="col-sm-3">
                      <label class="input-label">Enable</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="reservationOption.enabled" class="form-control" name="enabled">
                        <option :value="true">有効</option>
                        <option :value="false">無効</option>
                      </select>
                    </div>
                  </div>

                  <div class="row input-control">
                    <div class="col-sm-3">
                      <label class="input-label">Type</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="reservationOption.type" class="form-control" name="optionType" @change="handleChangeType" disabled>
                        <option value="quantity">Quantity</option>
                        <option value="text">Text</option>
                      </select>
                    </div>
                  </div>

                  <div class="row input-control">
                    <div class="col-sm-3">
                      <label class="input-label">Need to pay</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="reservationOption.hasPrice" class="form-control" name="hasPrice" @change="handleChangeHasPrice" disabled>
                        <option :value="true">Yes</option>
                        <option :value="false">No</option>
                      </select>
                    </div>
                  </div>

                  <template v-if="reservationOption.type === 'quantity'">
                    <div>
                      <div class="row input-control">
                        <div class="col-sm-3">
                          <label class="input-label">Unit</label>
                        </div>
                        <div class="col-sm-9">
                          <input 
                            type="string" 
                            name="unit"
                            v-model="reservationOption.detail.unit" 
                            class="form-control">
                        </div>
                      </div>
                      <div class="row input-control" v-if="errorOptions.unit">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-9">
                          <span class="error-msg">{{ errorOptions.unit }}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <div class="row input-control">
                        <div class="col-sm-3">
                          <label class="input-label">Max Quantity</label>
                        </div>
                        <div class="col-sm-9">
                          <input 
                            type="number" 
                            name="maxQuantity"
                            v-model="reservationOption.detail.maxQuantity" 
                            @blur="handleBlurMaxQuantity"
                            class="form-control">
                        </div>
                      </div>
                      <div class="row input-control" v-if="errorOptions.maxQuantity">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-9">
                          <span class="error-msg">{{ errorOptions.maxQuantity }}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <div v-if="reservationOption.hasPrice" class="row input-control">
                        <div class="col-sm-3">
                          <label class="input-label">Price per unit</label>
                        </div>
                        <div class="col-sm-9">
                          <input 
                            type="number" 
                            name="pricePerUnit"
                            v-model="reservationOption.detail.pricePerUnit" 
                            @blur="handleBlurFee"
                            class="form-control">
                        </div>
                      </div>
                      <div class="row input-control" v-if="reservationOption.hasPrice && errorOptions.pricePerUnit">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-9">
                          <span class="error-msg">{{ errorOptions.pricePerUnit }}</span>
                        </div>
                      </div>
                    </div>
                  </template>

                  <template v-if="reservationOption.type === 'text'">
                    <div class="row input-control align-start">
                      <div class="col-sm-3">
                        <label class="input-label">List options</label>
                      </div>

                      <div v-if="reservationOption.detail.listOptions?.length" class="col-sm-9">
                        <div v-for="(option, index) in reservationOption.detail.listOptions" class="mb-10" :key="index" :id="'option-detail-' + index">
                          <div class="option-detail-container">
                            <div>
                              <label class="mr-20">Title</label>
                              <input 
                                type="text" 
                                :name="'title-' + index"
                                v-model="option.title" 
                                class="form-control"
                                :class="!reservationOption.hasPrice ? 'w-250' : ''">
                            </div>
                            <div v-if="reservationOption.hasPrice">
                              <label class="mr-20">Price</label>
                              <input 
                                type="number" 
                                :name="'price-' + index"
                                v-model="option.price" 
                                @blur="handleBlurFee"
                                class="form-control"
                              >
                            </div>
                            <button v-if="reservationOption.detail.listOptions?.length > 1" type="button" @click="removeOption(index)" class="btn btn-danger">
                              <span class="glyphicon glyphicon-trash"></span>
                            </button>
                          </div>
                          <div class="option-detail-container">
                            <div v-if="errorOptions[`title-${index}`]" class="mr-20">
                              <span class="error-msg">{{ errorOptions[`title-${index}`] }}</span>
                            </div>
                            <div v-if="errorOptions[`price-${index}`]" class="mr-20">
                              <span class="error-msg">{{ errorOptions[`price-${index}`] }}</span>
                            </div>
                          </div>
                        </div>

                        <button type="button" @click="addOption" class="btn btn-primary">
                          <span class="glyphicon glyphicon-plus"></span>
                        </button>
                      </div>
                    </div>
                  </template>

                  <div v-if="errorData" class="row">
                    <div class="col-sm-3"></div>
                    <div class="col-sm-9">
                      <span class="error-msg">{{ errorData }}</span>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-sm-3"></div>
                    <div class="col-sm-9 mt-10">
                      <button type="submit" class="btn btn-primary w-100">更新</button>
                      <button type="button" class="btn btn-default w-100" @click="handleResetForm">リセット</button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}