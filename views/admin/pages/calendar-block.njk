{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
  <link href="{{ assets.url("css/admin/calendar.css") }}" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/bootstrap-datepicker.min.css") }}">
  <link rel="stylesheet" type="text/css" href="{{ assets.url("css/admin/daterangepicker.css") }}">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("js/index.global.min.js") }}"></script>
  {% include "../../assets/dayjs.njk" %}
  {% include "../../assets/daterangepicker.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}
    let calendarEl, calendar;
    function CalendarBlockPage(props) {
      return {
        isLoading: false,
        isDisabledEdit: true,
        packType: initialData.packType,
        blockDates: [],
        blockRange: JSON.parse(JSON.stringify(initialData.calendarBlockRange)),
        selectedDates: [],
        inputBlockRange: JSON.parse(JSON.stringify(initialData.calendarBlockRange)),
        selectedDatePickerType: [],
        errorBlockRangeInput: '',
        errors: errors,

        manipulateCalendar() {
          calendarEl = document.getElementById('calendar');
          calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ja',
            selectable: true,
            headerToolbar: {
              left: 'title',
              center: '',
              right: 'today prevYear,prev,next,nextYear'
            },
            events: (fetchInfo, successCallback, failureCallback) => {
              const params = {
                start: fetchInfo.startStr,
                end: fetchInfo.endStr,
                packType: this.packType,
              }
              axiosClient.get('/admin/labo-install-pack/api/calendar-blocks?' + new URLSearchParams(params))
                .then(response => {
                  this.blockDates = response.data?.blockDates;
                  const events = response.data?.blockDates?.map(event => ({
                    title: 'Blocked',
                    start: event,
                    end: event,
                    allDay: true,
                    display: 'background',
                    backgroundColor: '#FFB6C7'
                  }));
                  successCallback(events);
                })
                .catch(error => {
                  console.error('Error fetching day off:', error);
                  failureCallback(error);
                });
            },
            loading: (isLoading) => {
              this.isLoading = isLoading;
            },
            select: (info) => {
              const selectedDates = this.getDatesInRange(info.startStr, info.endStr);
              selectedDates.pop();
              this.selectedDates = selectedDates;
              const isSelectedBlockedDate = this.checkIsSelectedBlockedDates();
              if (isSelectedBlockedDate) {
                $('#deleteDayOffModal').modal('show');
              } else {
                if (selectedDates.length === 1) {
                  this.selectedDatePickerType = [];
                  $('#dayOff').val(selectedDates[0]);
                  $('#dayOffDatePicker').data('daterangepicker').setStartDate(selectedDates[0]);
                  $('#dayOffDatePicker').data('daterangepicker').setEndDate(selectedDates[0]);
                  $('#setDayOffModal').modal('show');
                }
                if (selectedDates.length > 1) {
                  this.selectedDatePickerType = ['isPickDaterange'];
                  $('#dayOffFrom').val(selectedDates[0]);
                  $('#dayOffTo').val(selectedDates[selectedDates.length - 1]);
                  $('#dayOffDaterangePicker').data('daterangepicker').setStartDate(selectedDates[0]);
                  $('#dayOffDaterangePicker').data('daterangepicker').setEndDate(selectedDates[selectedDates.length - 1]);
                  $('#setDayOffModal').modal('show');
                }
              }
            }
          });
          calendar.render();
        },

        manipulateDaterangePicker() {
          $('#dayOffDaterangePicker').daterangepicker({
            locale: {
              format: 'YYYY-MM-DD'
            }
          });
          $('#dayOffDaterangePicker').on('apply.daterangepicker', (ev, picker) => {
            const startDate = picker.startDate.format("YYYY-MM-DD");
            const endDate = picker.endDate.format("YYYY-MM-DD");
            $("#dayOffFrom").val(startDate);
            $("#dayOffTo").val(endDate);
            this.selectedDates = this.getDatesInRange(startDate, endDate);
            document.getElementById('errorDaterange').innerHTML = '';
          });
        },

        manipulateDatePicker() {
          $('#dayOffDatePicker').daterangepicker({
            locale: {
              format: 'YYYY-MM-DD'
            },
            singleDatePicker: true
          });
          $('#dayOffDatePicker').on('apply.daterangepicker', (ev, picker) => {
            const selectedDate = picker.startDate.format("YYYY-MM-DD");
            this.selectedDates = [selectedDate];
            $("#dayOff").val(selectedDate);
            document.getElementById('errorDate').innerHTML = '';
          });
        },

        mounted() {
          this.manipulateCalendar();
          this.manipulateDaterangePicker();
          this.manipulateDatePicker();
          $('#setDayOffModal').on('hidden.bs.modal', () => {
            this.handleResetDatePicker();
          });
        },

        async handleEditBlockRange() {
          if (this.isDisabledEdit) {
            this.isDisabledEdit = !this.isDisabledEdit;
            return;
          }
          if (this.inputBlockRange.value <= 0) {
            this.errorBlockRangeInput = 'Please input valid number';
            return;
          }
          if (JSON.stringify(this.inputBlockRange) === JSON.stringify(this.blockRange)) {
            this.isDisabledEdit = !this.isDisabledEdit;
            return;
          }
          await this.handleUpdateBlockRange();
          this.isDisabledEdit = !this.isDisabledEdit;
        },

        async handleUpdateBlockRange() {
          this.isLoading = true;
          try {
            const response = await axiosClient.put(
              `/admin/labo-install-pack/api/configurations/${this.inputBlockRange.id}/`, 
              { value: this.inputBlockRange.value.toString() }
            );
            this.blockRange = JSON.stringify(this.inputBlockRange);
          } catch (e) {
            alert(`[ERROR]: ${e.response.data.message}`);
          }
          this.isLoading = false;
        },

        async handleSaveDayOff() {
          if (!this.selectedDates.length) {
            if (this.checkIsSelectedDaterangePicker()) {
              document.getElementById('errorDaterange').innerHTML = 'Please input valid range';
            } else {
              document.getElementById('errorDate').innerHTML = 'Please input valid date';
            }
            return;
          }
          const isSelectedBlockedDate = this.checkIsSelectedBlockedDates();
          if (isSelectedBlockedDate) {
            this.handleResetDatePicker();
            $('#setDayOffModal').modal('hide');
            return;
          }
          this.isLoading = true;
          try {
            const params = {
              dates: this.selectedDates,
              packType: this.packType,
            }
            const response = await axiosClient.post(`/admin/labo-install-pack/api/calendar-blocks/`, params);
            this.handleResetDatePicker();
            $('#setDayOffModal').modal('hide');
            window.location.reload();
          } catch (e) {
            alert(`[ERROR]: ${e.response.data.message}`);
          }
          this.isLoading = false;
        },

        handleDeleteDayOff() {
          this.isLoading = true;
          try {
            const params = {
              dates: this.selectedDates,
              packType: this.packType,
            }
            const response = axiosClient.delete(`/admin/labo-install-pack/api/calendar-blocks/`, { data: params });
            this.handleResetDatePicker();
            $('#deleteDayOffModal').modal('hide');
            window.location.reload();
          } catch (e) {
            alert(`[ERROR]: ${e.response.data.message}`);
          }
          this.isLoading = false;
        },

        handleResetDatePicker() {
          const currenDate = dayjs().format('YYYY-MM-DD');
          $('#dayOffDaterangePicker').data('daterangepicker').setStartDate(currenDate);
          $('#dayOffDaterangePicker').data('daterangepicker').setEndDate(currenDate);
          $('#dayOffDatePicker').data('daterangepicker').setStartDate(currenDate);
          $('#dayOffDatePicker').data('daterangepicker').setEndDate(currenDate);
          $('#dayOff').val('');
          $('#dayOffFrom').val('');
          $('#dayOffTo').val('');
          document.getElementById('errorDaterange').innerHTML = '';
          document.getElementById('errorDate').innerHTML = '';
          this.selectedDates = [];
        },

        handleChangeBlockRange() {
          this.errorBlockRangeInput = '';
          if (this.inputBlockRange.value <= 0) {
            this.errorBlockRangeInput = 'Please input valid number';
          }
        },

        getDatesInRange(fromDate, toDate) {
          const dateArray = [];
          let currentDate = dayjs(fromDate);
          const endDate = dayjs(toDate);
          while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
            dateArray.push(currentDate.format('YYYY-MM-DD'));
            currentDate = currentDate.add(1, 'day');
          }
          return dateArray;
        },

        checkIsSelectedDaterangePicker() {
          return this.selectedDatePickerType.includes('isPickDaterange');
        },

        checkIsSelectedBlockedDates() {
          const setSelectedDates = new Set(this.selectedDates);
          const setBlockDates = new Set(this.blockDates);
          const isSelectedBlockedDate = [...setSelectedDates].every(item => setBlockDates.has(item));
          return isSelectedBlockedDate;
        }
      }
    }
  </script>

  {% raw %}
    <div v-scope="CalendarBlockPage()" @vue:mounted="mounted" v-cloak>
      <div class="loading-mask" v-if="isLoading">
        <div class="loader"></div>
      </div>
      <div class="tab-content" :class="isLoading ? 'tranparent-block' : ''">
        <div class="tab-pane fade in active">
          <h2>Calendar block</h2>
          <hr>
          <ul class="nav nav-tabs mb-10">
            <li :class="packType === 1 ? 'active' : ''">
              <a href="/admin/labo-install-pack/calendar-blocks?packType=1">Aircon Install Pack</a>
            </li>
            <li :class="packType === 2 ? 'active' : ''">
              <a href="/admin/labo-install-pack/calendar-blocks?packType=2">Water Heater Pack</a>
            </li>
            <li :class="packType === 3 ? 'active' : ''">
              <a href="/admin/labo-install-pack/calendar-blocks?packType=3">Dishwasher Pack</a>
            </li>
          </ul>
          <div>
            <div class="panel panel-primary">
              <div class="panel-heading">
                <h3 class="panel-title">Calendar</h3>
              </div>
              <div class="panel-body">
                <div class="calendar-configs">
                  <div class="calendar-config">
                    <div class="calendar-config-title">ブロック日数を入力してください</div>
                    <div class="calendar-config-input">
                      <input type="number" v-model="inputBlockRange.value" @change="handleChangeBlockRange" class="form-control" :disabled="isDisabledEdit">
                      <button class="btn btn-primary" @click="handleEditBlockRange">{{isDisabledEdit ? 'Edit' : 'Save'}}</button>
                    </div>
                  </div>
                  <div v-if="errorBlockRangeInput" class="error-msg" id="errorBlockRangeInput">{{errorBlockRangeInput}}</div>
                  <div class="calendar-config">
                    <div class="calendar-config-title">Calendar</div>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#setDayOffModal" data-backdrop="static">Set day off calendar</button>
                  </div>
                </div>
                <div class="calendar-container">
                  <div id="calendar"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div id="setDayOffModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title">Set day off calendar</h4>
              <div class="close-modal">
                <span class="glyphicon glyphicon-remove action" data-dismiss="modal" @click="handleResetDatePicker"></span>
              </div>
            </div>
            <div class="form-horizontal modal-form" id="setDayOffModal" name="setDayOffModal">
              <div class="container-fluid">
                <div class="form-group" :class="checkIsSelectedDaterangePicker() ? '' : 'hidden'">
                  <label for="dayOffDaterangePicker" class="control-label">Date range</label>
                  <div class="dayoff-picker">
                    <span id="dayOffDaterangePicker" class="glyphicon glyphicon-calendar btn btn-default" aria-hidden="true"></span>
                    <input type="text" id="dayOffFrom" name="dayOffFrom" class="daterange-field form-control" readonly="">
                    <strong> ~ </strong>
                    <input type="text" id="dayOffTo" name="dayOffTo" class="daterange-field form-control" readonly="">
                  </div>
                  <div>
                    <span class="error-msg" id="errorDaterange"></span>
                  </div>
                </div>
                <div class="form-group" :class="checkIsSelectedDaterangePicker() ? 'hidden' : ''">
                  <label for="dayOffDatePicker" class="control-label">Date</label>
                  <div class="dayoff-picker">
                    <span id="dayOffDatePicker" class="glyphicon glyphicon-calendar btn btn-default" aria-hidden="true"></span>
                    <input type="text" id="dayOff" name="dayOff" class="daterange-field form-control" readonly="">
                  </div>
                  <div>
                    <span class="error-msg" id="errorDate"></span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="datePickerType">
                    <span>Select range date</span>
                    <input type="checkbox" id="datePickerType" name="datePickerType" value="isPickDaterange" v-model="selectedDatePickerType" @change="handleResetDatePicker">
                  </label>
                </div>
                <div class="form-group modal-form-button">
                  <button class="btn btn-default" data-dismiss="modal" @click="handleResetDatePicker">Cancel</button>
                  <button class="btn btn-primary" @click="handleSaveDayOff">Save</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div id="deleteDayOffModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title">Delete day off</h4>
              <div class="close-modal">
                <span class="glyphicon glyphicon-remove action" data-dismiss="modal" @click="handleResetDatePicker"></span>
              </div>
            </div>
            <div class="form-horizontal modal-form" id="deleteDayOffModal" name="deleteDayOffModal">
              <div class="container-fluid">
                <div class="form-group">
                  <label for="dayOffDatePicker" class="control-label">
                    Remove day off {{ selectedDates.length === 1 ? `for ${selectedDates[0]}` : `from ${selectedDates[0]} to ${selectedDates[selectedDates.length - 1]}` }}
                  </label>
                </div>
                <div class="form-group modal-form-button">
                  <button class="btn btn-default" data-dismiss="modal" @click="handleResetDatePicker">Cancel</button>
                  <button class="btn btn-primary" @click="handleDeleteDayOff">Delete</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}