{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
  {% include "../../assets/dayjs.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}

    function ReservationsListPage() {
      return {
        limit: initialData.pagination.perPage,
        page: initialData.pagination.pageNumber,
        total: initialData.pagination.total,
        listStatus: initialData.status,
        packType: initialData.packType,
        isLoading: false,
        data: initialData.reservations || [],
        query: { 
          packType: initialData.packType,
          reservationCode: initialData.query?.reservationCode || '',
          page: initialData.query?.page || 1
        },
        errors: errors,

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
        },

        async changePage(page) {
          this.query = {
            ...this.query,
            page
          }
          await this.handleGetReservations(this.query)
        },

        async handleGetReservations(params) {
          this.isLoading = true;
          const filteredParams = Object.fromEntries(
            Object.entries(this.query).filter(([key, value]) => value !== '')
          );
          const searchParams = new URLSearchParams(filteredParams).toString();
          window.location.href = `/admin/labo-install-pack/reservations?${searchParams}`;
        },

        async handleFilterSubmit(event) {
          event.preventDefault();
          const inputs = {};
          $('form[name=reservationFilterForm]')
            .find(':input')
            .each(function () {
              if (this.name) {
                inputs[this.name] = $(this).val();
              }
            });
          this.query = {
            ...this.query,
            ...inputs,
            page: 1
          };
          await this.handleGetReservations(this.query);
        },

        getReservationStatus(statusCode) {
          const status = this
            .listStatus
            .find(obj => obj.id === statusCode.toString());
          return status
            ?.name
        },

        getReservationBadge(statusCode) {
          switch (statusCode) {
            case 1:
              return 'label-warning';
            case 2:
              return 'label-success';
            case 3:
              return 'label-primary';
            case 4:
              return 'label-danger';
            default:
              return '';
          }
        },

        getReservationDetailUrl(reservationId) {
          return `/admin/labo-install-pack/reservations/${reservationId}`;
        }
      }
    }
  </script>

  {% raw %}
    <div v-scope="ReservationsListPage()" @vue:mounted="mounted" v-cloak>
      <div class="tab-pane fade in active">
        <h2>Reservation</h2>
        <hr>
        <ul class="nav nav-tabs">
          <li :class="packType === 1 ? 'active' : ''">
            <a href="/admin/labo-install-pack/reservations?packType=1">Aircon Install Pack</a>
          </li>
          <li :class="packType === 2 ? 'active' : ''">
            <a href="/admin/labo-install-pack/reservations?packType=2">Water Heater Pack</a>
          </li>
          <li :class="packType === 3 ? 'active' : ''">
            <a href="/admin/labo-install-pack/reservations?packType=3">Dishwasher Pack</a>
          </li>
        </ul>
        <div>
          <div class="panel panel-primary mt-10">
            <div class="panel-heading">
              <h3 class="panel-title">Reservation List</h3>
            </div>
            <div class="panel-body">
              <div>
                <div class="row pl-15 pr-15 mb-20" :class="isLoading ? 'tranparent-block' : ''">
                  <form @submit="handleFilterSubmit" name="reservationFilterForm">
                    <div class="row">
                      <div class="col-md-6 gap-5 filter-field">
                        <input type="text" v-model="query.reservationCode" name="reservationCode" class="form-control" placeholder="予約コード">
                        <button type="submit" class="btn btn-search btn-default">検索</button>
                      </div>
                    </div>
                  </form>
                </div>

                <div class="row pl-15 pr-15 mt-10">
                  <div class="loading-mask" v-if="isLoading">
                    <div class="loader"></div>
                  </div>
                  <table class="table borderless table-striped" :class="isLoading ? 'tranparent-block' : ''">
                    <thead>
                      <tr>
                        <th>予約コード</th>
                        <th>ユーザー名（ユーザーID）</th>
                        <th>作業日</th>
                        <th>ステータス</th>
                        <th>パートナー</th>
                        <th>作成日時</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="reservation in data" :key="reservation.id">
                        <td>
                          <a :href="getReservationDetailUrl(reservation.id)">{{reservation.code}}</a>
                        </td>
                        <td>{{reservation.lastname}}{{reservation.firstname}}（{{reservation.userId}}）</td>
                        <td>{{reservation.workingDate1}}</td>
                        <td>
                          <div class="label badge-custom" :class="getReservationBadge(reservation.status)">{{getReservationStatus(reservation.status)}}</div>
                        </td>
                        <td>{{reservation.partner || ''}}</td>
                        <td>{{dateUtils.formatDateTime(reservation.createdAt, 'YYYY-MM-DD HH:mm') || ''}}</td>
                      </tr>
                    </tbody>
                  </table>
                  <nav>
                    <ul class="pager">
                      <li>
                        <a v-if="page>1" @click="changePage(page-1)">前の{{ limit }}件</a>
                        <template v-else>前の{{ limit }}件</template>
                      </li>
                      <li>
                        <a v-if="total > page*limit" @click="changePage(page+1)">次の{{ limit }}件</a>
                        <template v-else>次の{{ limit }}件</template>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}