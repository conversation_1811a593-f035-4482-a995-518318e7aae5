{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}
    const initPackage = {
      name: '',
      code: '',
      fee: '',
      sort: 1,
      outOfStock: false,
      status: 1,
      packType: 1,
    }

    function PackageDetailPage(props) {
      const requiredMsg = "このフィールドは必須です。";
      const invalidFeeMsg = "1以上の半角数字を入力してください。";
      const invalidSortMsg = "1以上の半角数字を入力してください。";
      return {
        isLoading: false,
        isDisabled: false,
        packageDetail: initialData.packageDetail ? JSON.parse(JSON.stringify(initialData.packageDetail)) : {...initPackage, code: initialData.nextCode},
        listStatus: initialData.status,
        listPackageType: initialData.packageType,
        errorForm: {},
        errorData: '',
        errors: errors,

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
        },

        validateField(value, validations, errorField) {
          this.errorForm[errorField] = '';
          for (const validation of validations) {
            const error = validation(value);
            if (error) {
              this.errorForm[errorField] = error;
              break;
            }
          }
        },

        validateForm() {
          let isValid = true;
          this.validateName();
          this.validateCode();
          this.validateFee();
          this.validateSort();
          this.validateStatus();
          this.validatePackType();
          if (Object.values(this.errorForm).some((error) => error)) {
            isValid = false;
          }
          return isValid;
        },

        validateName() {
          const validations = [
            (value) => !value ? requiredMsg : '',
          ];
          this.validateField(this.packageDetail?.name, validations, 'errorName');
        },

        validateCode() {
          const validations = [
            (value) => !value ? requiredMsg : '',
          ];
          this.validateField(this.packageDetail?.code, validations, 'errorCode');
        },

        validateFee() {
          const validations = [
            (value) => value <= 0 ? invalidFeeMsg : '',
            (value) => !value ? requiredMsg : '',
          ];
          this.validateField(this.packageDetail?.fee, validations, 'errorFee');
        },

        validateSort() {
          const validations = [
            (value) => value <= 0 ? invalidSortMsg : '',
            (value) => !value ? requiredMsg : '',
          ];
          this.validateField(this.packageDetail?.sort, validations, 'errorSort');
        },

        validateStatus() {
          const validations = [
            (value) => !value ? requiredMsg : '',
          ];
          this.validateField(this.packageDetail?.status, validations, 'errorStatus');
        },

        validatePackType() {
          const validations = [
            (value) => !value ? requiredMsg : '',
          ];
          this.validateField(this.packageDetail?.packType, validations, 'errorPackType');
        },

        async handleFormSubmit(event) {
          if (!this.validateForm()) {
            return;
          }
          this.isLoading = true;
          try {
            const response = await axiosClient.request({
              method: this.packageDetail.id ? 'put' : 'post',
              url: this.packageDetail.id ? `/admin/labo-install-pack/api/packages/${this.packageDetail.id}/` : `/admin/labo-install-pack/api/packages/`,
              data: this.packageDetail,
            })
            if (response.data?.error) {
              this.errorData = 'エラーが発生しました';
              this.isLoading = false;
              return;
            }
            this.errorData = '';
            window.location.href = `/admin/labo-install-pack/packages?packType=${this.packageDetail.packType}`;
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        async handleDeletePackage() {
          this.isLoading = true;
          try {
            const response = await axiosClient.delete(`/admin/labo-install-pack/api/packages/${this.packageDetail.id}/`)
            if (response.data?.error) {
              this.errorData = 'エラーが発生しました';
              this.isLoading = false;
              $('#deletePackageModal').modal('hide');
              return;
            }
            this.errorData = '';
            window.location.href = `/admin/labo-install-pack/packages?packType=${this.packageDetail.packType}`;
          } catch (error) {
            alert(`[ERROR]: エラーが発生しました`);
            this.isLoading = false;
          }
        },

        handleBlurSort(event) {
          if (!event.target.value) {
            event.target.value = 0;
          }
        },

        handleBlurFee(event) {
          if (!event.target.value) {
            event.target.value = 0;
          }
        },
      }
    }
  </script>

  {% raw %}
    <div v-scope="PackageDetailPage()" @vue:mounted="mounted" v-cloak>
      <div class="loading-mask" v-if="isLoading">
        <div class="loader"></div>
      </div>
      <div class="tab-content" :class="isLoading ? 'tranparent-block' : ''">
        <div class="tab-pane fade in active">
          <h2>Package</h2>
          <hr>
          <div>
            <div class="panel panel-primary">
              <div class="panel-heading">Create new package</div>
              <div class="panel-body">
                <form class="packageDetailForm" @submit.prevent="handleFormSubmit" name="packageDetailForm">
                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label for="name" class="input-label">パッケージ名</label>
                      <input 
                        type="text" 
                        id="name" 
                        name="name"
                        v-model="packageDetail.name"
                        class="form-control"
                        @input="validateName"
                        :disabled="isDisabled">
                      <div v-if="errorForm.errorName">
                        <span class="error-msg">{{ errorForm.errorName }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label for="code" class="input-label">コード</label>
                      <input 
                        type="text" 
                        id="code" 
                        name="code"
                        v-model="packageDetail.code"
                        class="form-control"
                        @input="validateCode"
                        :disabled="isDisabled">
                      <div v-if="errorForm.errorCode">
                        <span class="error-msg">{{ errorForm.errorCode }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label for="fee" class="input-label">価格</label>
                      <input 
                        type="number" 
                        id="fee" 
                        name="fee" 
                        v-model="packageDetail.fee" 
                        class="form-control"
                        @input="validateFee"
                        @blur="handleBlurFee"
                        :disabled="isDisabled">
                      <div v-if="errorForm.errorFee">
                        <span class="error-msg">{{ errorForm.errorFee }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label for="sort" class="input-label">並び順</label>
                      <input 
                        type="number" 
                        id="sort" 
                        name="sort" 
                        v-model="packageDetail.sort" 
                        class="form-control"
                        @input="validateSort"
                        @blur="handleBlurSort"
                        :disabled="isDisabled">
                      <div v-if="errorForm.errorSort">
                        <span class="error-msg">{{ errorForm.errorSort }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label class="input-label">
                        売り切れ
                        <input 
                          type="checkbox" 
                          v-model="packageDetail.outOfStock" 
                          name="outOfStock" 
                          id="outOfStock" 
                          :disabled="isDisabled"/>
                      </label>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label for="status" class="input-label">ステータス</label>
                      <select v-model="packageDetail.status" class="form-control" name="status" @change="validateStatus">
                        <option v-for="option in listStatus" :key="option.id" v-bind:value="option.id">{{ option.name }}</option>
                      </select>
                      <div v-if="errorForm.errorStatus">
                        <span class="error-msg">{{ errorForm.errorStatus }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-20">
                      <label for="packType" class="input-label">Package Type</label>
                      <select v-model="packageDetail.packType" class="form-control" name="packType" @change="validatePackType">
                        <option v-for="option in listPackageType" :key="option.id" v-bind:value="option.id">{{ option.name }}</option>
                      </select>
                      <div v-if="errorForm.errorPackType">
                        <span class="error-msg">{{ errorForm.errorPackType }}</span>
                      </div>
                    </div>
                  </div>

                  <div v-if="errorData" class="row">
                    <div class="col-sm-12">
                      <span class="error-msg">{{ errorData }}</span>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-lg-6">
                      <button type="submit" class="btn btn-primary w-100">更新</button>
                      <button v-if="!!packageDetail.id" type="button" class="btn btn-danger w-100" data-target="#deletePackageModal" data-toggle="modal">消去</button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div id="deletePackageModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title">Delete package</h4>
              <div class="close-modal">
                <span class="glyphicon glyphicon-remove action" data-dismiss="modal"></span>
              </div>
            </div>
            <div class="form-horizontal modal-form" id="deletePackageModal" name="deletePackageModal">
              <div class="container-fluid">
                <div class="form-group">
                  <label for="dayOffDatePicker" class="control-label">
                    Are you sure you want to delete this package?
                  </label>
                </div>
                <div class="form-group modal-form-button">
                  <button class="btn btn-default" data-dismiss="modal">Cancel</button>
                  <button class="btn btn-danger" @click="handleDeletePackage">Delete</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}