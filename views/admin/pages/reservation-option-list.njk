{% extends "layout/admin/_macro.njk" %}
{% from "../sidebar/_macro.njk" import adminSidebar %}

{% block sideBar %}
  {{ adminSidebar(activePage) }}
{% endblock %}
{% block stylesheets %}
  <link href="{{ assets.url("css/admin/styles.css") }}" rel="stylesheet">
{% endblock %}
{% block scripts %}
  <script src="{{ assets.url("/vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("/vendor/js/petite-vue.js") }}" defer init></script>
  <script src="{{ assets.url("/vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}
{% block mainContent %}
  {{ security.renderTemplate("initialData", data | dump) }}
  {{ security.renderTemplate("errors", errors | dump) }}

  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const errors = {{ security.getTemplateContent("errors") }}

    function PackageDetailPage(props) {
      return {
        isLoading: false,
        listOptions: initialData.options || [],
        packType: initialData.packType,
        errors: errors,

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
        },

        getPackageDetailUrl(packageId) {
          return `/admin/labo-install-pack/reservation-options/${packageId}`;
        },

        getPackageStatus(isEnable) {
          return isEnable ? '有効' : '無効';
        },

        getPackageBadge(isEnable) {
          return isEnable ? 'label-primary' : 'label-default';
        },
      }
    }
  </script>

  {% raw %}
    <div v-scope="PackageDetailPage()" @vue:mounted="mounted" v-cloak>
      <div class="loading-mask" v-if="isLoading">
        <div class="loader"></div>
      </div>
      <div class="tab-content" :class="isLoading ? 'tranparent-block' : ''">
        <div class="tab-pane fade in active">
          <h2>Reservation Options</h2>
          <hr>
          <ul class="nav nav-tabs mb-10">
            <li :class="packType === 1 ? 'active' : ''">
              <a href="/admin/labo-install-pack/reservation-options?packType=1">Aircon Install Pack</a>
            </li>
            <li :class="packType === 2 ? 'active' : ''">
              <a href="/admin/labo-install-pack/reservation-options?packType=2">Water Heater Pack</a>
            </li>
            <li :class="packType === 3 ? 'active' : ''">
              <a href="/admin/labo-install-pack/reservation-options?packType=3">Dishwasher Pack</a>
            </li>
          </ul>
          <div>
            <div class="panel panel-primary">
              <div class="panel-heading">
                <h3 class="panel-title">Reservation Options Management</h3>
              </div>
              <div class="panel-body">
                <div class="row pl-15 pr-15 mt-10">
                  <div class="loading-mask" v-if="isLoading">
                    <div class="loader"></div>
                  </div>
                  <table class="table borderless table-striped" :class="isLoading ? 'tranparent-block' : ''">
                    <thead>
                      <tr>
                        <th>Code</th>
                        <th>Option</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="option in listOptions" :key="option.id">
                        <td>
                          <a :href="getPackageDetailUrl(option.id)">{{option.code}}</a>
                        </td>
                        <td>{{option.title}}</td>
                        <td>
                          <div class="label badge-custom" :class="getPackageBadge(option.enabled)">{{getPackageStatus(option.enabled)}}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endraw %}

{% endblock %}