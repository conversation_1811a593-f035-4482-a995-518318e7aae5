###################
# BUILD FOR LOCAL DEVELOPMENT
###################

FROM --platform=x86_64 node:18.18.2-alpine As development

ARG CI_JOB_TOKEN
ENV CI_JOB_TOKEN=$CI_JOB_TOKEN

# Create app directory
WORKDIR /usr/src/app

# Copy application dependency manifests to the container image.
# A wildcard is used to ensure copying both package.json AND package-lock.json (when available).
# Copying this first prevents re-running npm install on every code change.
COPY --chown=node:node package*.json ./

RUN npm config set @vietnam:registry=https://gitlab.wkwk.io/api/v4/packages/npm/
RUN npm config set -- //gitlab.wkwk.io/api/v4/packages/npm/:_authToken=${CI_JOB_TOKEN}
RUN npm config set strict-ssl=false

# Install app dependencies using the `npm ci` command instead of `npm install`
RUN npm ci

# Bundle app source
COPY --chown=node:node . .

# Use the node user from the image (instead of the root user)
USER node

###################
# BUILD FOR PRODUCTION
###################

FROM --platform=x86_64 node:18.18.2-alpine As build

WORKDIR /usr/src/app

COPY --chown=node:node package*.json ./

# In order to run `npm run build` we need access to the Nest CLI which is a dev dependency.
# In the previous development stage we ran `npm ci` which installed all dependencies,
# so we can copy over the node_modules directory from the development image
COPY --chown=node:node --from=development /usr/src/app/node_modules ./node_modules

COPY --chown=node:node . .

# Run the build command which creates the production bundle
RUN npm run build

# Set NODE_ENV environment variable
ENV NODE_ENV production

ARG CI_JOB_TOKEN
ENV CI_JOB_TOKEN=$CI_JOB_TOKEN

RUN npm config set @vietnam:registry=https://gitlab.wkwk.io/api/v4/packages/npm/
RUN npm config set -- //gitlab.wkwk.io/api/v4/packages/npm/:_authToken=${CI_JOB_TOKEN}
RUN npm config set strict-ssl=false

# Running `npm ci` removes the existing node_modules directory and passing in --only=production ensures that only the production dependencies are installed. This ensures that the node_modules directory is as optimized as possible
RUN npm ci --only=production && npm cache clean --force

USER node

###################
# PRODUCTION
###################

FROM --platform=x86_64 node:18.18.2-alpine As production

WORKDIR /usr/src/app

# Bundle app source
# Copy the bundled code from the build stage to the production image
COPY --chown=node:node --from=build /usr/src/app .
COPY --chown=node:node ./entrypoint.sh ./entrypoint.sh

RUN chmod +x ./entrypoint.sh

EXPOSE 3000

# Install AWS Modules
RUN apk update \
    && apk add --no-cache py3-pip \
    && pip install awscli --upgrade --user

ENV PATH=$PATH:/root/.local/bin

ENTRYPOINT ["/bin/sh", "./entrypoint.sh"]
