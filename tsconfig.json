{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": false,
    "skipLibCheck": false,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "resolveJsonModule": true,
    "paths": {
      "@src/*": [
        "src/*"
      ],
      "@configs/*": [
        "src/configs/*"
      ],
      "@modules/*": [
        "src/modules/*"
      ],
      "@providers/*": [
        "src/providers/*"
      ],
      "@exceptions/*": [
        "src/exceptions/*"
      ],
      "@common/*": [
        "src/common/*"
      ],
      "@entities/*": [
        "src/entities/*"
      ],
      "@migrations/*": [
        "src/migrations/*"
      ],
    }
  }
}
