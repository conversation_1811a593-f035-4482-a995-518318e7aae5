{"name": "labo-install-pack", "version": "0.0.1", "description": "Create a friendly form for user input their data instead of Google Form", "author": "<EMAIL>", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run scss", "build": "nest build", "debug": "nodemon", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "npm run prebuild && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "scss": "npx sass views:public/css --style=compressed", "scss:dev": "npx sass --watch views:public/css", "lint": "eslint \"{src,apps,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:ver": "jest --verbose", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm migration:generate", "migration:show": "npm run typeorm migration:show", "migration:run": "npm run typeorm migration:run -- -d src/providers/ormconfig.ts", "migration:revert": "npm run typeorm migration:revert -- -d src/providers/ormconfig.ts", "migration:create": "npm run typeorm migration:create", "command": "node dist/src/command", "command:debug": "nest build && node dist/src/command", "lint:staged": "lint-staged", "dev:prepare": "npm install --save-dev @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint eslint-config-prettier eslint-plugin-prettier prettier husky lint-staged && npx husky install"}, "dependencies": {"@golevelup/nestjs-rabbitmq": "^3.6.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs/axios": "^2.0.0", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.0", "@nestjs/core": "^9.0.0", "@nestjs/platform-express": "^9.4.3", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^7.1.1", "@nestjs/typeorm": "^9.0.1", "@vietnam/cnga-frontend": "1.1.11", "@vietnam/cnga-http-request": "1.0.2-rc", "@vietnam/cnga-middleware": "1.3.2", "@vietnam/curama-py3-api": "1.1.0", "@vietnam/curama-worker": "^1.0.0", "@vietnam/document-generator": "^1.0.1-rc", "aws-sdk": "^2.1416.0", "axios": "^1.7.5", "cache-manager": "^5.7.6", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "connect-flash": "^0.1.1", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "crypto-js": "^4.1.1", "csrf": "^3.1.0", "csv-parse": "^5.5.2", "dayjs": "^1.11.9", "express-session": "^1.17.3", "googleapis": "^105.0.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "nest-commander": "^3.11.0", "nestjs-i18n": "^9.2.2", "nodemon": "^3.0.1", "papaparse": "^5.4.1", "pg": "^8.11.0", "sharp": "^0.33.2", "typeorm": "^0.3.20"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/amqplib": "^0.10.1", "@types/express": "^4.17.13", "@types/express-session": "^1.17.7", "@types/jest": "28.1.8", "@types/jsonwebtoken": "^9.0.8", "@types/multer": "^1.4.7", "@types/node": "^16.18.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "jest": "28.1.3", "lint-staged": "^13.2.3", "prettier": "^2.8.8", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["!**/infrastructure/**", "!**/mocks/**", "!**/*.module.ts", "!**/index.ts", "!**/*.json"], "coverageDirectory": "coverage", "testEnvironment": "node", "coverageReporters": ["lcov", "text", "text-summary"], "verbose": true, "collectCoverage": true, "coverageThreshold": {"**": {"statements": 80, "branches": 80, "functions": 80}}, "coveragePathIgnorePatterns": ["/node_modules/", "/dist/", "/public/", "/coverage/", "/src/common", "/src/configs", "/src/providers", "/src/entities", "/src/migrations", "/dtos/", ".eslintrc.js", "main.ts"], "moduleNameMapper": {"^@src/(.*)": "<rootDir>/src/$1", "^@configs/(.*)": "<rootDir>/src/configs/$1", "^@modules/(.*)": "<rootDir>/src/modules/$1", "^@providers/(.*)": "<rootDir>/src/providers/$1", "^@exceptions/(.*)": "<rootDir>/src/common/exceptions/$1", "^@common/(.*)": "<rootDir>/src/common/$1", "^@migrations/(.*)": "<rootDir>/src/migrations/$1"}}, "lint-staged": {"*.ts": ["npm run format", "npm run lint"]}}