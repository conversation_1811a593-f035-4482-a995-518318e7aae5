
.check-package-version: &check-package-version |
  apk add --update coreutils && apk add jq curl
  RED='\033[1;31m'
  INFO='\033[1;36m'
  ownPackages=$(npm ls --depth=0 --json | jq '.dependencies | to_entries[] | select(.key | startswith("@vietnam"))');
  ownPackages="$(echo "$ownPackages" | jq -r '.key + "|" + .value.version')";
  hasPackageOutdated=false;
  for package in $ownPackages ; do
    name=$(echo "$package" | cut -d'|' -f1);
    usedVersion=$(echo "$package" | cut -d'|' -f2);
    latestVersion=$(npm view "$name" version);
    if [ "$usedVersion" != "$latestVersion" ]; then
      echo -e "${RED}$name is outdated:$usedVersion -> $latestVersion";
      hashPackageOutdated=true;
    else
      echo -e "${INFO}$name is up to date: $usedVersion";
    fi
  done

  if [ "$hashPackageOutdated" = true ]; then
    echo -e "${RED}Some packages @vietnam/* are outdated";
    exit 1;
  else
    echo -e "${INFO}All packages @vietnam/* are up to date";
    exit 0;
  fi

check-pkg-version:
  stage: check-pkg-version
  needs: [npm-prepare]
  extends: npm-prepare
  script:
    - *check-package-version
  allow_failure: true
  
