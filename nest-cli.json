{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"webpack": false, "assets": [{"include": "../public", "outDir": "dist/public", "watchAssets": true}, {"include": "../views", "outDir": "dist/views", "watchAssets": true}, {"include": "lang/**/*", "watchAssets": true, "outDir": "dist/src"}, {"include": "migrations/csv/*", "watchAssets": true, "outDir": "dist"}, {"include": "../src/assets", "watchAssets": true, "outDir": "dist"}]}}